/**
 * 测试osgEarth线程池功能
 * 逐步定位WebAssembly中的线程问题
 */

#include <iostream>
#include <osgEarth/Threading>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

using namespace osgEarth;

int main() {
    std::cout << "🧵 osgEarth线程池测试程序" << std::endl;
    std::cout << "=========================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[test] WebAssembly环境检测" << std::endl;
    
    // 检查多线程支持
    if (emscripten_has_threading_support()) {
        std::cout << "[test] ✅ Emscripten多线程支持已启用" << std::endl;
    } else {
        std::cout << "[test] ❌ Emscripten多线程支持未启用" << std::endl;
        return 1;
    }
    
    // 设置环境变量减少线程数
    std::cout << "[test] 设置环境变量减少线程并发..." << std::endl;
    setenv("OSGEARTH_NODEPAGER_CONCURRENCY", "1", 1);
    setenv("OSGEARTH_TERRAIN_CONCURRENCY", "1", 1);
#endif
    
    try {
        std::cout << "[test] 步骤1: 测试jobs命名空间基础功能..." << std::endl;
        
        // 检查jobs系统是否活跃
        if (jobs::alive()) {
            std::cout << "[test] ✅ jobs系统已激活" << std::endl;
        } else {
            std::cout << "[test] ❌ jobs系统未激活" << std::endl;
            return 1;
        }
        
        std::cout << "[test] 步骤2: 获取默认线程池..." << std::endl;
        
        // 尝试获取默认线程池（不设置并发数）
        auto default_pool = jobs::get_pool();
        if (default_pool) {
            std::cout << "[test] ✅ 默认线程池获取成功" << std::endl;
            std::cout << "[test] 默认线程池名称: '" << default_pool->name() << "'" << std::endl;
            std::cout << "[test] 默认并发数: " << default_pool->concurrency() << std::endl;
        } else {
            std::cout << "[test] ❌ 默认线程池获取失败" << std::endl;
            return 1;
        }
        
        std::cout << "[test] 步骤3: 创建自定义线程池（并发数=1）..." << std::endl;
        
        // 尝试创建一个并发数为1的线程池
        auto test_pool = jobs::get_pool("test_pool", 1);
        if (test_pool) {
            std::cout << "[test] ✅ 自定义线程池创建成功" << std::endl;
            std::cout << "[test] 线程池名称: '" << test_pool->name() << "'" << std::endl;
            std::cout << "[test] 并发数: " << test_pool->concurrency() << std::endl;
        } else {
            std::cout << "[test] ❌ 自定义线程池创建失败" << std::endl;
            return 1;
        }
        
        std::cout << "[test] 步骤4: 测试设置并发数..." << std::endl;
        
        // 这是最关键的测试 - 这里会调用start_threads()
        std::cout << "[test] 尝试设置并发数为1..." << std::endl;
        test_pool->set_concurrency(1);
        std::cout << "[test] ✅ 并发数设置成功" << std::endl;
        
        std::cout << "[test] 步骤5: 测试PagingManager相关的线程池..." << std::endl;
        
        // 测试PagingManager使用的默认线程池名称
        const std::string DEFAULT_JOBPOOL_NAME = "oe.pagingmanager";
        auto paging_pool = jobs::get_pool(DEFAULT_JOBPOOL_NAME, 1);
        if (paging_pool) {
            std::cout << "[test] ✅ PagingManager线程池创建成功" << std::endl;
            std::cout << "[test] 线程池名称: '" << paging_pool->name() << "'" << std::endl;
            
            // 模拟PagingManager的操作
            std::cout << "[test] 模拟PagingManager设置并发数..." << std::endl;
            paging_pool->set_concurrency(1);
            std::cout << "[test] ✅ PagingManager并发数设置成功" << std::endl;
        } else {
            std::cout << "[test] ❌ PagingManager线程池创建失败" << std::endl;
            return 1;
        }
        
        std::cout << "[test] 步骤6: 获取线程池指标..." << std::endl;
        
        auto metrics = test_pool->metrics();
        if (metrics) {
            std::cout << "[test] ✅ 线程池指标获取成功" << std::endl;
            std::cout << "[test] 并发数: " << metrics->concurrency.load() << std::endl;
            std::cout << "[test] 待处理任务: " << metrics->pending.load() << std::endl;
            std::cout << "[test] 运行中任务: " << metrics->running.load() << std::endl;
            std::cout << "[test] 总任务数: " << metrics->total.load() << std::endl;
        } else {
            std::cout << "[test] ❌ 线程池指标获取失败" << std::endl;
        }
        
        std::cout << "[test] ✅ 所有测试通过！" << std::endl;
        std::cout << "[test] osgEarth线程池功能正常" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ 异常: " << e.what() << std::endl;
        return 1;
    } catch (...) {
        std::cout << "[test] ❌ 未知异常" << std::endl;
        return 1;
    }
    
    std::cout << "[test] 程序正常结束" << std::endl;
    return 0;
}
