@echo off
echo ========================================
echo Starting HTTP Server for WebAssembly
echo ========================================
echo.

:: Check if Python is available
python --version >nul 2>&1
if errorlevel 1 (
    echo Error: Python not found
    echo Please install Python to run the HTTP server
    pause
    exit /b 1
)

:: Check if we have WebAssembly files
if not exist "*.wasm" (
    echo Warning: No .wasm files found in current directory
    echo Make sure you have built the WebAssembly project first
)

if not exist "*.js" (
    echo Warning: No .js files found in current directory
    echo Make sure you have built the WebAssembly project first
)

echo Starting HTTP server on port 8080...
echo Open your browser and navigate to: http://localhost:8080
echo.
echo Press Ctrl+C to stop the server
echo.

:: Start Python HTTP server
python -m http.server 8080
