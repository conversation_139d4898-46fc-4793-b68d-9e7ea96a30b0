/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_COLORRAMP_DRIVEROPTIONS
#define OSGEARTH_DRIVER_COLORRAMP_DRIVEROPTIONS 1

#include <osgEarth/Common>
#include <osgEarth/TileSource>
#include <osgEarth/URI>
#include <osgEarth/ElevationLayer>
#include <osgEarth/LayerReference>

namespace osgEarth { namespace Drivers
{
    using namespace osgEarth;
    using namespace osgEarth::Contrib;

    class ColorRampOptions : public TileSourceOptions // NO EXPORT; header only
    {
    public:
        OE_OPTION_LAYER(ElevationLayer, elevationLayer);
        OE_OPTION(URI, ramp);

    public:
        ColorRampOptions( const TileSourceOptions& opt =TileSourceOptions() ) :
            TileSourceOptions( opt )      
        {
            setDriver( "colorramp" );
            fromConfig( _conf );
        }

        /** dtor */
        virtual ~ColorRampOptions() { }

    public:
        Config getConfig() const {
            Config conf = TileSourceOptions::getConfig();
            elevationLayer().set(conf, "elevation");
            conf.set("ramp", _ramp);
            return conf;
        }

    protected:
        void mergeConfig( const Config& conf ) {
            TileSourceOptions::mergeConfig( conf );
            fromConfig( conf );
        }

    private:
        void fromConfig( const Config& conf ) {
            elevationLayer().get(conf, "elevation");
            conf.get("ramp", _ramp);
        }

    };

} } // namespace osgEarth::Drivers

#endif // OSGEARTH_DRIVER_COLORRAMP_DRIVEROPTIONS

