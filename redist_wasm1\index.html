<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAssembly + osgEarth 测试中心</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            min-height: 100vh;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .header h1 {
            font-size: 3em;
            margin: 0;
            background: linear-gradient(45deg, #00d4ff, #ff6b6b);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .header p {
            font-size: 1.2em;
            color: #cccccc;
            margin: 10px 0;
        }

        .test-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: 20px;
            margin: 40px 0;
        }

        .test-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .test-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 30px rgba(0, 212, 255, 0.3);
        }

        .test-card h3 {
            font-size: 1.5em;
            margin: 0 0 15px 0;
            color: #00d4ff;
        }

        .test-card p {
            color: #cccccc;
            line-height: 1.6;
            margin: 10px 0;
        }

        .test-button {
            display: inline-block;
            background: linear-gradient(45deg, #00d4ff, #0099cc);
            color: white;
            text-decoration: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-weight: bold;
            transition: all 0.3s ease;
            margin: 10px 5px 0 0;
        }

        .test-button:hover {
            background: linear-gradient(45deg, #0099cc, #007399);
            transform: scale(1.05);
        }

        .test-button.secondary {
            background: linear-gradient(45deg, #ff6b6b, #cc5555);
        }

        .test-button.secondary:hover {
            background: linear-gradient(45deg, #cc5555, #994444);
        }

        .status-section {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 25px;
            margin: 20px 0;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .feature-list {
            list-style: none;
            padding: 0;
        }

        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .feature-list li:before {
            content: "✅ ";
            color: #00ff88;
            font-weight: bold;
        }

        .icon {
            font-size: 2em;
            margin-bottom: 10px;
            display: block;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>🌍 WebAssembly + osgEarth</h1>
            <p>多线程系统与数字地球测试中心</p>
            <p>专门测试weejobs多线程系统和SDL2+osgEarth数字地球显示</p>
        </div>

        <div class="status-section">
            <h2>🎯 测试目标</h2>
            <p>本测试中心专门针对以下核心功能进行验证：</p>
            <ul class="feature-list">
                <li>weejobs多线程系统在WebAssembly中的兼容性</li>
                <li>SDL2图形系统的基础渲染功能</li>
                <li>osgEarth数字地球的WebAssembly移植</li>
                <li>多线程环境下的稳定性和性能</li>
            </ul>
        </div>

        <div class="test-grid">
            <div class="test-card">
                <span class="icon">🧵</span>
                <h3>weejobs多线程系统测试</h3>
                <p>测试简化的weejobs多线程系统，包括：</p>
                <ul>
                    <li>基础线程创建和执行</li>
                    <li>std::async任务分发</li>
                    <li>简化线程池功能</li>
                    <li>多线程环境兼容性</li>
                </ul>
                <a href="test_weejobs.html" class="test-button">开始测试</a>
            </div>

            <div class="test-card">
                <span class="icon">🎮</span>
                <h3>SDL2图形系统测试</h3>
                <p>测试SDL2在WebAssembly中的图形功能：</p>
                <ul>
                    <li>SDL2初始化和窗口创建</li>
                    <li>2D渲染器功能</li>
                    <li>事件处理（鼠标、键盘）</li>
                    <li>动画渲染循环</li>
                </ul>
                <a href="test_sdl2.html" class="test-button">开始测试</a>
            </div>

            <div class="test-card">
                <span class="icon">🌍</span>
                <h3>简化数字地球测试</h3>
                <p>模拟osgEarth数字地球的核心功能：</p>
                <ul>
                    <li>3D地球渲染（简化版）</li>
                    <li>瓦片地图系统</li>
                    <li>交互式导航控制</li>
                    <li>坐标系统和投影</li>
                </ul>
                <a href="test_earth.html" class="test-button">开始测试</a>
            </div>

            <div class="test-card">
                <span class="icon">🔧</span>
                <h3>系统诊断工具</h3>
                <p>用于诊断WebAssembly环境的工具：</p>
                <ul>
                    <li>多线程支持检测</li>
                    <li>SharedArrayBuffer验证</li>
                    <li>HTTP头配置检查</li>
                    <li>性能基准测试</li>
                </ul>
                <a href="#" class="test-button secondary">开发中</a>
            </div>
        </div>

        <div class="status-section">
            <h2>📋 使用说明</h2>
            <p><strong>测试顺序建议：</strong></p>
            <ol>
                <li><strong>weejobs多线程系统测试</strong> - 验证基础多线程功能是否正常</li>
                <li><strong>SDL2图形系统测试</strong> - 验证图形渲染和事件处理</li>
                <li><strong>osgEarth数字地球</strong> - 完整的3D地球应用（开发中）</li>
            </ol>

            <p><strong>注意事项：</strong></p>
            <ul>
                <li>确保浏览器支持WebAssembly和SharedArrayBuffer</li>
                <li>建议使用Chrome或Firefox最新版本</li>
                <li>如果遇到多线程问题，请检查HTTP头设置</li>
                <li>测试过程中请观察浏览器控制台输出</li>
            </ul>
        </div>

        <div class="status-section">
            <h2>🚀 技术栈</h2>
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px;">
                <div>
                    <strong>编译工具：</strong><br>
                    Emscripten 4.0.10<br>
                    CMake + Ninja
                </div>
                <div>
                    <strong>图形库：</strong><br>
                    SDL2<br>
                    OpenGL ES 2.0
                </div>
                <div>
                    <strong>多线程：</strong><br>
                    pthread<br>
                    SharedArrayBuffer
                </div>
                <div>
                    <strong>地球引擎：</strong><br>
                    osgEarth<br>
                    OpenSceneGraph
                </div>
            </div>
        </div>
    </div>
</body>

</html>