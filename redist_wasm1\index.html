<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth WebAssembly Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        #header {
            background-color: #2d2d2d;
            padding: 10px;
            text-align: center;
            border-bottom: 2px solid #4a4a4a;
        }

        #header h1 {
            margin: 0;
            font-size: 24px;
            color: #ffffff;
        }

        #canvas-container {
            position: relative;
            width: 100vw;
            height: calc(100vh - 60px);
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #000000;
        }

        #canvas {
            border: 1px solid #4a4a4a;
            background-color: #000000;
            cursor: crosshair;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #ffffff;
            font-size: 18px;
        }

        #status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(45, 45, 45, 0.9);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            color: #cccccc;
        }

        .hidden {
            display: none;
        }

        .error {
            color: #ff4444;
            background-color: rgba(255, 68, 68, 0.1);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ff4444;
        }
    </style>
</head>

<body>
    <div id="header">
        <h1>osgEarth WebAssembly 地球可视化</h1>
    </div>

    <div id="canvas-container">
        <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>

        <div id="loading">
            <div>正在加载osgEarth WebAssembly模块...</div>
        </div>

        <div id="status">
            状态：初始化中...
        </div>
    </div>

    <script>
        // 全局变量
        var Module = {}; // 使用var避免重复声明错误
        let canvas = null;
        let loadingElement = null;
        let statusElement = null;

        // 初始化函数
        function init() {
            canvas = document.getElementById('canvas');
            loadingElement = document.getElementById('loading');
            statusElement = document.getElementById('status');

            // 设置Canvas大小
            resizeCanvas();

            // 监听窗口大小变化
            window.addEventListener('resize', resizeCanvas);

            // 设置Module配置
            setupModule();

            // 检查并加载WebAssembly
            checkFiles();
        }

        // 设置Canvas大小
        function resizeCanvas() {
            const container = document.getElementById('canvas-container');
            const rect = container.getBoundingClientRect();

            canvas.width = rect.width;
            canvas.height = rect.height;
        }

        // 设置Module配置
        function setupModule() {
            Module = {
                canvas: canvas,

                // 打印函数
                print: function (text) {
                    console.log('osgEarth:', text);
                },

                // 错误处理
                printErr: function (text) {
                    console.error('osgEarth Error:', text);
                    updateStatus('错误: ' + text, true);
                },

                // 模块加载完成
                onRuntimeInitialized: function () {
                    console.log('osgEarth WebAssembly模块加载完成');
                    hideLoading();
                    updateStatus('就绪');

                    // 启动应用
                    try {
                        if (Module.ccall) {
                            Module.ccall('main', 'number', ['number', 'number'], [0, 0]);
                        }
                    } catch (e) {
                        console.error('启动应用失败:', e);
                        updateStatus('启动失败: ' + e.message, true);
                    }
                }
            };
        }

        // 检查WebAssembly文件是否存在
        function checkFiles() {
            const files = ['osgearth_myviewer.js', 'osgearth_myviewer.wasm'];
            const promises = files.map(file =>
                fetch(file, { method: 'HEAD' })
                    .then(response => ({ file, exists: response.ok }))
                    .catch(() => ({ file, exists: false }))
            );

            Promise.all(promises).then(results => {
                const missing = results.filter(r => !r.exists);
                if (missing.length > 0) {
                    document.getElementById('loading').innerHTML =
                        '<div class="error">错误：缺少WebAssembly文件<br>' +
                        missing.map(r => r.file).join(', ') +
                        '<br><br>请先运行 build_wasm1.bat 编译项目</div>';
                    updateStatus('文件缺失', true);
                } else {
                    loadWebAssembly();
                }
            });
        }

        function loadWebAssembly() {
            updateStatus('加载WebAssembly模块...');

            // 确保Module在全局作用域中可用
            window.Module = Module;

            // 动态加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'osgearth_myviewer.js';
            script.onload = function () {
                updateStatus('WebAssembly脚本已加载');
            };
            script.onerror = function () {
                document.getElementById('loading').innerHTML =
                    '<div class="error">错误：无法加载WebAssembly脚本</div>';
                updateStatus('加载失败', true);
            };
            document.head.appendChild(script);
        }

        // 隐藏加载界面
        function hideLoading() {
            if (loadingElement) {
                loadingElement.style.display = 'none';
            }
        }

        // 更新状态
        function updateStatus(text, isError = false) {
            if (statusElement) {
                statusElement.textContent = '状态: ' + text;
                if (isError) {
                    statusElement.classList.add('error');
                } else {
                    statusElement.classList.remove('error');
                }
            }
        }

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>
</body>

</html>