<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth线程池测试</title>
    <style>
        body {
            font-family: 'Consol<PERSON>', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
        .step {
            background-color: #3a3a3a;
            padding: 8px;
            margin: 5px 0;
            border-radius: 3px;
            border-left: 3px solid #007acc;
        }
        .step.success { border-left-color: #44ff44; }
        .step.error { border-left-color: #ff4444; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧵 osgEarth线程池测试</h1>
        
        <div id="status" class="status">状态: 准备测试osgEarth线程池...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>逐步测试osgEarth的jobs线程池系统，定位WebAssembly中的线程创建问题：</p>
            <ul>
                <li>步骤1: 检查jobs系统基础功能</li>
                <li>步骤2: 获取默认线程池</li>
                <li>步骤3: 创建自定义线程池</li>
                <li>步骤4: 测试设置并发数（关键步骤）</li>
                <li>步骤5: 测试PagingManager线程池</li>
                <li>步骤6: 获取线程池指标</li>
            </ul>
        </div>
        
        <div class="status">
            <h3>📊 测试步骤</h3>
            <div id="test-steps"></div>
        </div>
        
        <div class="log">
            <h3>📝 详细日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        let testSteps = [];
        
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        function addTestStep(stepName, status = 'pending') {
            const stepsEl = document.getElementById('test-steps');
            const stepId = `step-${testSteps.length}`;
            
            testSteps.push({id: stepId, name: stepName, status: status});
            
            const div = document.createElement('div');
            div.id = stepId;
            div.className = `step ${status}`;
            
            let statusIcon = '⏳';
            if (status === 'success') statusIcon = '✅';
            else if (status === 'error') statusIcon = '❌';
            else if (status === 'running') statusIcon = '🔄';
            
            div.innerHTML = `${statusIcon} ${stepName}`;
            stepsEl.appendChild(div);
        }
        
        function updateTestStep(stepIndex, status) {
            const step = testSteps[stepIndex];
            if (step) {
                step.status = status;
                const stepEl = document.getElementById(step.id);
                if (stepEl) {
                    stepEl.className = `step ${status}`;
                    
                    let statusIcon = '⏳';
                    if (status === 'success') statusIcon = '✅';
                    else if (status === 'error') statusIcon = '❌';
                    else if (status === 'running') statusIcon = '🔄';
                    
                    stepEl.innerHTML = `${statusIcon} ${step.name}`;
                }
            }
        }
        
        // Module配置
        var Module = {
            print: function(text) {
                console.log('osgEarth Jobs Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 解析测试步骤
                if (text.includes('步骤')) {
                    const stepMatch = text.match(/步骤(\d+):\s*(.+)/);
                    if (stepMatch) {
                        const stepIndex = parseInt(stepMatch[1]) - 1;
                        updateTestStep(stepIndex, 'running');
                        addLog(`开始执行: ${stepMatch[2]}`, 'info');
                    }
                }
                
                // 检查成功标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                    
                    // 更新对应步骤状态
                    if (text.includes('jobs系统已激活')) {
                        updateTestStep(0, 'success');
                    } else if (text.includes('默认线程池获取成功')) {
                        updateTestStep(1, 'success');
                    } else if (text.includes('自定义线程池创建成功')) {
                        updateTestStep(2, 'success');
                    } else if (text.includes('并发数设置成功')) {
                        updateTestStep(3, 'success');
                    } else if (text.includes('PagingManager线程池创建成功')) {
                        updateTestStep(4, 'success');
                    } else if (text.includes('线程池指标获取成功')) {
                        updateTestStep(5, 'success');
                    }
                }
                
                // 检查错误标记
                if (text.includes('❌')) {
                    addLog(text, 'error');
                }
            },
            
            printErr: function(text) {
                console.error('osgEarth Jobs Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('osgEarth线程池测试模块初始化完成');
                addLog('osgEarth线程池测试模块初始化完成', 'success');
                updateStatus('osgEarth线程池测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('osgEarth线程池测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
                
                // 标记当前运行的步骤为失败
                for (let i = 0; i < testSteps.length; i++) {
                    if (testSteps[i].status === 'running') {
                        updateTestStep(i, 'error');
                        break;
                    }
                }
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                updateStatus('程序退出，状态码: ' + status, status !== 0, status === 0);
                
                if (status === 0) {
                    updateStatus('🎉 所有测试通过！osgEarth线程池功能正常', false, true);
                } else {
                    updateStatus('❌ 测试失败，发现线程池问题', true);
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始osgEarth线程池测试', 'info');
            
            // 初始化测试步骤
            addTestStep('检查jobs系统基础功能');
            addTestStep('获取默认线程池');
            addTestStep('创建自定义线程池');
            addTestStep('测试设置并发数（关键）');
            addTestStep('测试PagingManager线程池');
            addTestStep('获取线程池指标');
            
            updateStatus('加载osgEarth线程池测试模块...');
            addLog('开始加载osgEarth线程池测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_osgearth_jobs.js';
            script.onload = function() {
                addLog('osgEarth线程池测试脚本加载成功', 'success');
                updateStatus('osgEarth线程池测试脚本已加载');
            };
            script.onerror = function() {
                addLog('osgEarth线程池测试脚本加载失败', 'error');
                updateStatus('osgEarth线程池测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
