<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SDL2图形系统测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .canvas-container {
            background-color: #2d2d2d;
            padding: 20px;
            border-radius: 5px;
            margin: 10px 0;
            text-align: center;
        }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
        canvas {
            border: 2px solid #555;
            border-radius: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎮 SDL2图形系统测试</h1>
        
        <div id="status" class="status">状态: 准备测试SDL2图形系统...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>测试SDL2在WebAssembly中的图形渲染功能：</p>
            <ul>
                <li>SDL2初始化和窗口创建</li>
                <li>2D渲染器功能</li>
                <li>事件处理（鼠标、键盘）</li>
                <li>动画渲染循环</li>
            </ul>
        </div>
        
        <div class="canvas-container">
            <h3>🖼️ SDL2渲染画布</h3>
            <p>SDL2将在下面的画布中渲染图形。您可以点击和拖拽鼠标进行交互。</p>
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="log">
            <h3>📝 测试日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // Module配置
        var Module = {
            canvas: document.getElementById('canvas'),
            
            print: function(text) {
                console.log('SDL2 Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 检查成功和失败标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                } else if (text.includes('❌')) {
                    addLog(text, 'error');
                } else if (text.includes('⚠️')) {
                    addLog(text, 'warn');
                }
                
                // 检查特定事件
                if (text.includes('帧数:')) {
                    addLog(text, 'success');
                } else if (text.includes('鼠标')) {
                    addLog(text, 'info');
                } else if (text.includes('按键')) {
                    addLog(text, 'info');
                }
            },
            
            printErr: function(text) {
                console.error('SDL2 Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('SDL2图形系统测试模块初始化完成');
                addLog('SDL2图形系统测试模块初始化完成', 'success');
                updateStatus('SDL2图形系统测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('SDL2图形系统测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                
                if (status === 0) {
                    updateStatus('🎉 SDL2图形系统测试完成！', false, true);
                } else {
                    updateStatus('❌ SDL2图形系统测试失败', true);
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始SDL2图形系统测试', 'info');
            
            updateStatus('加载SDL2图形系统测试模块...');
            addLog('开始加载SDL2图形系统测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_sdl2.js';
            script.onload = function() {
                addLog('SDL2图形系统测试脚本加载成功', 'success');
                updateStatus('SDL2图形系统测试脚本已加载');
            };
            script.onerror = function() {
                addLog('SDL2图形系统测试脚本加载失败', 'error');
                updateStatus('SDL2图形系统测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
