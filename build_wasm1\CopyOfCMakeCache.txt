# This is the CMakeCache file.
# For build in directory: f:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Archiver
CMAKE_AR:FILEPATH=C:/dev/emsdk/upstream/emscripten/emar.bat

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//CXX compiler
CMAKE_CXX_COMPILER:STRING=C:/dev/emsdk/upstream/emscripten/em++.bat

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=C:/Program Files/LLVM/bin/llvm-ar.exe

//`clang-scan-deps` dependency scanner
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS:FILEPATH=C:/Program Files/LLVM/bin/clang-scan-deps.exe

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=C:/Program Files/LLVM/bin/llvm-ranlib.exe

//CXX flags
CMAKE_CXX_FLAGS:STRING=-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-g

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -g -DNDEBUG

//C compiler
CMAKE_C_COMPILER:STRING=C:/dev/emsdk/upstream/emscripten/emcc.bat

//LLVM archiver
CMAKE_C_COMPILER_AR:FILEPATH=C:/Program Files/LLVM/bin/llvm-ar.exe

//`clang-scan-deps` dependency scanner
CMAKE_C_COMPILER_CLANG_SCAN_DEPS:FILEPATH=C:/Program Files/LLVM/bin/clang-scan-deps.exe

//Generate index for LLVM archive
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/Program Files/LLVM/bin/llvm-ranlib.exe

//C flags
CMAKE_C_FLAGS:STRING=-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -I

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Linker flags
CMAKE_EXE_LINKER_FLAGS:STRING=-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/pkgRedirects

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/OSGEARTH

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/LLVM/bin/ld.lld.exe

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Windows/System32/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/Program Files/LLVM/bin/llvm-nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/Program Files/LLVM/bin/llvm-objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/Program Files/LLVM/bin/llvm-objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=osgEarth SDK

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=https://github.com/gwaldron/osgearth

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OSGEARTH

//Ranlib
CMAKE_RANLIB:FILEPATH=C:/dev/emsdk/upstream/emscripten/emranlib.bat

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/Program Files/LLVM/bin/llvm-strip.exe

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=../cmake/emscripten.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//CURL found
CURL_FOUND:BOOL=TRUE

//CURL include directory
CURL_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//CURL library
CURL_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libcurl.a

//Emscripten build
EMSCRIPTEN:BOOL=ON

//GEOS C library
GEOS_C_LIBRARY:FILEPATH=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libgeos_c.a

//GEOS found
GEOS_FOUND:BOOL=TRUE

//GEOS include directory
GEOS_INCLUDE_DIR:PATH=C:/dev/vcpkg/installed/wasm32-emscripten/include

//GEOS library
GEOS_LIBRARY:FILEPATH=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libgeos.a

//GeographicLib found
GeographicLib_FOUND:BOOL=TRUE

//GeographicLib include directory
GeographicLib_INCLUDE_DIR:PATH=C:/dev/vcpkg/installed/wasm32-emscripten/include

//GeographicLib libraries
GeographicLib_LIBRARIES:STRING=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libGeographicLib.a

//GeographicLib library
GeographicLib_LIBRARY:FILEPATH=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libGeographicLib.a

//OpenGL found
OPENGL_FOUND:BOOL=TRUE

//OpenGL include directory
OPENGL_INCLUDE_DIR:PATH=C:/dev/emsdk/upstream/emscripten/system/include

//OpenGL libraries
OPENGL_LIBRARIES:STRING=GL

//OpenGL GL library
OPENGL_gl_LIBRARY:STRING=GL

//GLX library
OPENGL_glx_LIBRARY:STRING=

//OpenGL library
OPENGL_opengl_LIBRARY:STRING=GL

//OpenSceneGraph include directory
OPENSCENEGRAPH_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OpenSceneGraph libraries
OPENSCENEGRAPH_LIBRARIES:STRING=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgManipulator.a;F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a

//OPENTHREADS found
OPENTHREADS_FOUND:BOOL=TRUE

//OPENTHREADS include directory
OPENTHREADS_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OPENTHREADS library
OPENTHREADS_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libOpenThreads.a

//OSGDB found
OSGDB_FOUND:BOOL=TRUE

//OSGDB include directory
OSGDB_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGDB library
OSGDB_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgDB.a

//Value Computed by CMake
OSGEARTH_BINARY_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1

//No help, variable specified on the command line.
OSGEARTH_BUILD_DOCS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
OSGEARTH_BUILD_EXAMPLES:UNINITIALIZED=ON

//No help, variable specified on the command line.
OSGEARTH_BUILD_TESTS:UNINITIALIZED=OFF

//No help, variable specified on the command line.
OSGEARTH_BUILD_TOOLS:UNINITIALIZED=ON

//Value Computed by CMake
OSGEARTH_IS_TOP_LEVEL:STATIC=ON

//Value Computed by CMake
OSGEARTH_SOURCE_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth

//WebAssembly build
OSGEARTH_WEBASSEMBLY:BOOL=ON

//OSGGA found
OSGGA_FOUND:BOOL=TRUE

//OSGGA include directory
OSGGA_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGGA library
OSGGA_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgGA.a

//OSGMANIPULATOR found
OSGMANIPULATOR_FOUND:BOOL=TRUE

//OSGMANIPULATOR include directory
OSGMANIPULATOR_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGMANIPULATOR library
OSGMANIPULATOR_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgManipulator.a

//OSGSHADOW found
OSGSHADOW_FOUND:BOOL=TRUE

//OSGSHADOW include directory
OSGSHADOW_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGSHADOW library
OSGSHADOW_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgShadow.a

//OSGSIM found
OSGSIM_FOUND:BOOL=TRUE

//OSGSIM include directory
OSGSIM_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGSIM library
OSGSIM_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgSim.a

//OSGTEXT found
OSGTEXT_FOUND:BOOL=TRUE

//OSGTEXT include directory
OSGTEXT_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGTEXT library
OSGTEXT_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgText.a

//OSGUTIL found
OSGUTIL_FOUND:BOOL=TRUE

//OSGUTIL include directory
OSGUTIL_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGUTIL library
OSGUTIL_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgUtil.a

//OSGVIEWER found
OSGVIEWER_FOUND:BOOL=TRUE

//OSGVIEWER include directory
OSGVIEWER_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSGVIEWER library
OSGVIEWER_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosgViewer.a

//OSG found
OSG_FOUND:BOOL=TRUE

//OSG include directory
OSG_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//OSG library
OSG_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libosg.a

//OpenSceneGraph found
OpenSceneGraph_FOUND:BOOL=TRUE

//SQLite3 found
SQLITE3_FOUND:BOOL=TRUE

//SQLite3 include directory
SQLITE3_INCLUDE_DIR:PATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include

//SQLite3 library
SQLITE3_LIBRARY:FILEPATH=F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/libsqlite3.a

//ZLIB found
ZLIB_FOUND:BOOL=TRUE

//ZLIB include directory
ZLIB_INCLUDE_DIR:PATH=C:/dev/vcpkg/installed/wasm32-emscripten/include

//ZLIB libraries
ZLIB_LIBRARIES:STRING=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libz.a

//ZLIB library
ZLIB_LIBRARY:FILEPATH=C:/dev/vcpkg/installed/wasm32-emscripten/lib/libz.a


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_CLANG_SCAN_DEPS
CMAKE_C_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1

