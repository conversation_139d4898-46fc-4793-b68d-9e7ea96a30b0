/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTHUTIL_EPHEMERIS
#define OSGEARTHUTIL_EPHEMERIS

#include <osgEarth/Common>
#include <osgEarth/DateTime>
#include <osgEarth/Units>
#include <osgEarth/GeoData>
#include <osg/Vec3d>

namespace osgEarth
{
    /**
     * Location of a celestial body relative to the Earth.
     */
    struct CelestialBody
    {
        Angle rightAscension;
        Angle declination;
        Angle latitude;
        Angle longitude;
        Distance altitude;
        osg::Vec3d geocentric;
        osg::Vec3d eci;
    };

    /**
     * An Ephemeris gives the positions of naturally occurring astronimical
     * objects; in that case, the sun and the moon. Also includes some
     * related utility functions.
     */
    class OSGEARTH_EXPORT Ephemeris : public osg::Referenced
    {
    public:

        //! Return the sun's position for a given date and time.
        virtual CelestialBody getSunPosition(const DateTime& dt) const;

        //! Return the moon's position for a given date and time.
        virtual CelestialBody getMoonPosition(const DateTime& dt) const;

        /**
         * Gets an ECEF position from the right ascension, declination and range
         *
         * @param ra    Right ascension in radians
         * @param decl  Declination in radians
         * @param range Range in meters
         */
        osg::Vec3d getECEFfromRADecl(double ra, double decl, double range) const;
    };
    
}

#endif // OSGEARTHUTIL_EPHEMERIS
