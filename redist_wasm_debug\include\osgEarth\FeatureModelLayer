/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/GeometryCompiler>
#include <osgEarth/FeatureModelSource>
#include <osgEarth/Session>
#include <osgEarth/Style>
#include <osgEarth/Layer>
#include <osgEarth/LayerReference>
#include <osgEarth/Utils>

namespace osgEarth {
    class Map;
}

namespace osgEarth
{
    class FeatureNodeFactory;

    /**
     * Layer that creates a scene graph from feature data and symbology.
     */
    class OSGEARTH_EXPORT FeatureModelLayer : public VisibleLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public VisibleLayer::Options,
                                        public FeatureModelOptions,
                                        public GeometryCompilerOptions
        {
        public:
            // constructors
            Options();
            Options(const ConfigOptions& options);
            OE_OPTION_LAYER(FeatureSource, featureSource);
            virtual Config getConfig() const;
        protected: // LayerOptions
            virtual void mergeConfig(const Config& conf);        
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, FeatureModelLayer, Options, VisibleLayer, FeatureModel);

        //! The feature source from which to read geometry.
        void setFeatureSource(FeatureSource* layer);
        FeatureSource* getFeatureSource() const;

        //! Stylesheet to apply to the features
        void setStyleSheet(StyleSheet* value);
        StyleSheet* getStyleSheet() const;

        //! Set the dislpay layout (optional)
        void setLayout(const FeatureDisplayLayout& layout);
        const FeatureDisplayLayout& getLayout() const;

        void setAlphaBlending(const bool& value);
        const bool& getAlphaBlending() const;

        void setEnableLighting(const bool& value);
        const bool& getEnableLighting() const;

        //! Forces a rebuild on this FeatureModelLayer.
        void dirty() override;

    public:
        class CreateFeatureNodeFactoryCallback : public osg::Referenced {
        public:
            virtual FeatureNodeFactory* createFeatureNodeFactory(const Options&) =0;
        };

        //! Callback that will create a new FeatureNodeFactory for this layer to use
        //! to transform feature data into OSG scene graphs
        void setCreateFeatureNodeFactoryCallback(CreateFeatureNodeFactoryCallback* callback);
        CreateFeatureNodeFactoryCallback* getCreateFeatureNodeFactoryCallback() const;


    public: // Layer

        // opens the layer and returns the status
        Status openImplementation() override;

        // closes the layer
        Status closeImplementation() override;

        // The Node representing this layer.
        osg::Node* getNode() const override;

        //! Extent of the feature layer, if available (INVALID if not)
        const GeoExtent& getExtent() const override;

        //! Serialization
        Config getConfig() const override;

    public: // key value reporter

        Stats reportStats() const override;

    protected: // Layer
        
        // called by the map when this layer is added
        void addedToMap(const Map*) override;

        // called by the map when this layer is removed
        void removedFromMap(const Map*) override;

        // post-ctor initialization
        void init() override;

    protected:

        virtual ~FeatureModelLayer();

        //! You can override this as an alternative to setting the CreateFeatureNodeFactoryCallback
        virtual FeatureNodeFactory* createFeatureNodeFactoryImplementation() const;

        //! Created the FMG; override to customize
        virtual void create();

    private:
        osg::ref_ptr<class Session> _session;
        osg::ref_ptr<osg::Group> _root;
        bool _graphDirty;
        osg::ref_ptr<CreateFeatureNodeFactoryCallback> _createFactoryCallback;
        
        FeatureNodeFactory* createFeatureNodeFactory();
    };

} // namespace osgEarth
