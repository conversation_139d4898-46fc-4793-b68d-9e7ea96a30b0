<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth WebAssembly - Minimal Mode</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            background-color: #111;
        }
        
        #debug {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #0f0;
            background: rgba(0,0,0,0.8);
            padding: 10px;
            border-radius: 5px;
            font-size: 11px;
            z-index: 1000;
            max-width: 300px;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div id="debug">
        <h4>osgEarth WebAssembly Debug</h4>
        <div id="status">Initializing...</div>
    </div>
    
    <canvas id="canvas"></canvas>
    
    <script>
        function updateStatus(text) {
            document.getElementById('status').innerHTML = text;
            console.log('Status:', text);
        }
        
        updateStatus('Starting WebAssembly...');
        
        // 创建最简单的有效配置
        const basicConfig = `<map name="Basic" type="geocentric" version="2">
    <XYZImage name="osm">
        <url>https://a.tile.openstreetmap.org/{z}/{x}/{y}.png</url>
        <profile>spherical-mercator</profile>
        <min_level>0</min_level>
        <max_level>10</max_level>
        <format>png</format>
        <options>
            <max_concurrent_requests>1</max_concurrent_requests>
            <timeout>60</timeout>
        </options>
    </XYZImage>
    <options>
        <terrain>
            <tile_size>256</tile_size>
            <max_lod>10</max_lod>
            <cast_shadows>false</cast_shadows>
            <progressive>false</progressive>
            <concurrency>1</concurrency>
        </terrain>
    </options>
</map>`;
        
        // 配置WebAssembly模块
        var Module = {
            canvas: document.getElementById('canvas'),
            
            preRun: [
                function() {
                    updateStatus('PreRun: Setting up filesystem...');
                    
                    // 创建必要的虚拟文件
                    setTimeout(() => {
                        try {
                            if (typeof FS !== 'undefined') {
                                updateStatus('Writing config to filesystem...');
                                FS.writeFile('/myviewer_config.earth', basicConfig);
                                
                                // 创建一个空的默认文件，以防程序需要
                                FS.writeFile('/default.earth', basicConfig);
                                
                                // 检查文件是否创建成功
                                if (FS.analyzePath('/myviewer_config.earth').exists) {
                                    updateStatus('Config file created successfully');
                                } else {
                                    updateStatus('ERROR: Config file not created');
                                }
                            } else {
                                updateStatus('FS not available, will retry...');
                            }
                        } catch (e) {
                            updateStatus('ERROR: ' + e.message);
                        }
                    }, 10);
                    
                    // 强制WebGL 1.0 上下文
                    Module.canvas.webglContextAttributes = {
                        alpha: true,
                        depth: true,
                        stencil: false,
                        antialias: false,
                        premultipliedAlpha: false,
                        preserveDrawingBuffer: false,
                        powerPreference: "default",
                        failIfMajorPerformanceCaveat: false,
                        majorVersion: 1,
                        minorVersion: 0
                    };
                    
                    updateStatus('WebGL context configured');
                }
            ],
            
            onRuntimeInitialized: function() {
                updateStatus('Runtime initialized');
                
                // 最后检查文件系统
                setTimeout(() => {
                    if (typeof FS !== 'undefined') {
                        try {
                            // 确保配置文件存在
                            if (!FS.analyzePath('/myviewer_config.earth').exists) {
                                updateStatus('Creating config at runtime...');
                                FS.writeFile('/myviewer_config.earth', basicConfig);
                            }
                            
                            const files = FS.readdir('/');
                            updateStatus('Files: ' + files.join(', '));
                        } catch (e) {
                            updateStatus('Filesystem error: ' + e.message);
                        }
                    }
                }, 50);
                
                updateStatus('Application ready');
            },
            
            print: function(text) {
                console.log('stdout:', text);
                if (text.includes('[myviewer]')) {
                    updateStatus('App: ' + text.replace('[myviewer] ', ''));
                }
            },
            
            printErr: function(text) {
                console.warn('stderr:', text);
                
                // 更详细的错误处理
                if (text.includes('Error reading file')) {
                    updateStatus('ERROR: File read failed - ' + text);
                } else if (text.includes('Failed to realize')) {
                    updateStatus('WARNING: Graphics setup issue');
                } else if (text.includes('No earth file loaded')) {
                    updateStatus('WARNING: Using default map');
                } else if (text.includes('Aborted')) {
                    updateStatus('FATAL: Application aborted');
                }
            },
            
            onAbort: function(what) {
                updateStatus('ABORT: ' + (what || 'Unknown error'));
                console.error('Abort details:', what);
                
                // 尝试重启
                setTimeout(() => {
                    updateStatus('Attempting recovery...');
                }, 1000);
            },
            
            onOutOfMemory: function() {
                updateStatus('OUT OF MEMORY');
            },
            
            setStatus: function(text) {
                if (text) {
                    updateStatus('Loading: ' + text);
                }
            },
            
            // 保守的内存设置
            INITIAL_MEMORY: 64 * 1024 * 1024, // 64MB
            ALLOW_MEMORY_GROWTH: true,
            MAXIMUM_MEMORY: 256 * 1024 * 1024, // 最大256MB
            
            // 环境变量 - 减少日志噪音
            ENV: {
                'OSG_NOTIFY_LEVEL': 'WARN',
                'OSGEARTH_NOTIFY_LEVEL': 'WARN',
                'OSG_GL_ERROR_CHECKING': 'OFF'
            },
            
            // 传递配置文件参数
            arguments: ['--config', '/myviewer_config.earth'],
            
            // 其他设置
            noInitialRun: false,
            noExitRuntime: true,
            
            locateFile: function(path, prefix) {
                return prefix + path;
            }
        };
        
        // 设置画布
        Module.canvas.style.backgroundColor = '#111';
        
        // 加载WebAssembly
        updateStatus('Loading WebAssembly module...');
        var script = document.createElement('script');
        script.src = 'osgearth_myviewer.js';
        script.onload = function() {
            updateStatus('WebAssembly script loaded');
        };
        script.onerror = function() {
            updateStatus('ERROR: Failed to load WebAssembly script');
        };
        document.body.appendChild(script);
    </script>
</body>
</html> 