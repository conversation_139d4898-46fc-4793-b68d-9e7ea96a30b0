/**
 * 简单的pthread测试 - 使用C风格的pthread API
 */

#include <iostream>
#include <pthread.h>
#include <unistd.h>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

// 全局变量
int g_counter = 0;
bool g_running = true;

// 线程函数
void* worker_thread(void* arg) {
    int thread_id = *(int*)arg;
    std::cout << "[Thread " << thread_id << "] 线程启动" << std::endl;
    
    while (g_running) {
        g_counter++;
        
        if (g_counter % 100 == 0) {
            std::cout << "[Thread " << thread_id << "] 计数器: " << g_counter << std::endl;
        }
        
        // 短暂休眠
        usleep(10000); // 10ms
    }
    
    std::cout << "[Thread " << thread_id << "] 线程结束" << std::endl;
    return nullptr;
}

int main() {
    std::cout << "🧵 简单pthread测试程序" << std::endl;
    std::cout << "=====================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境检测" << std::endl;
    
    // 检查多线程支持
    if (emscripten_has_threading_support()) {
        std::cout << "[main] ✅ Emscripten多线程支持已启用" << std::endl;
    } else {
        std::cout << "[main] ❌ Emscripten多线程支持未启用" << std::endl;
        return 1;
    }
#endif
    
    // 测试基本pthread功能
    std::cout << "[main] 测试基本pthread功能..." << std::endl;
    
    pthread_t thread;
    int thread_id = 1;
    
    // 创建线程
    int result = pthread_create(&thread, nullptr, worker_thread, &thread_id);
    if (result != 0) {
        std::cout << "[main] ❌ pthread_create失败，错误码: " << result << std::endl;
        return 1;
    }
    
    std::cout << "[main] ✅ pthread创建成功" << std::endl;
    
    // 让线程运行一段时间
    sleep(2);
    
    // 停止线程
    g_running = false;
    
    // 等待线程完成
    result = pthread_join(thread, nullptr);
    if (result != 0) {
        std::cout << "[main] ❌ pthread_join失败，错误码: " << result << std::endl;
        return 1;
    }
    
    std::cout << "[main] ✅ pthread测试完成，最终计数: " << g_counter << std::endl;
    
    return 0;
}
