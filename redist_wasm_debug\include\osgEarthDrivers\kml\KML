/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_KML
#define OSGEARTH_DRIVER_KML 1

#include <osgEarth/MapNode>
#include <osgEarth/URI>
#include <osgEarth/Registry>
#include <osgDB/ReaderWriter>
#include <osgDB/FileNameUtils>
#include <osgDB/Registry>
#include "KMLOptions"

namespace osgEarth { namespace KML
{
    using namespace osgEarth;

    /**
        * Loads KML or KMZ from a URI.
        *
        * @param[in ] uri     URI from which to load the KML or KMZ
        * @param[in ] mapNode MapNode to which to attach KML elements
        * @param[in ] options Optional KML options
        */
    static osg::Node* load(const URI&             uri, 
                            MapNode*              mapNode,
                            const osgDB::Options* dbOptions, 
                            const KMLOptions&     kmlOptions)
    {
        // ensure the library is loaded.
        osgDB::Registry::instance()->addArchiveExtension( "kmz" );
        if ( osgDB::getLowerCaseFileExtension(uri.full()) == "kmz" )
        {
            //OE_INFO << "[KML] Preloading KML plugin\n";
            std::string libName = osgDB::Registry::instance()->createLibraryNameForExtension("kml");
            osgDB::Registry::LoadStatus status = osgDB::Registry::instance()->loadLibrary(libName);
            if ( status == osgDB::Registry::NOT_LOADED )
            {
                OE_WARN << "[KML] Failed to load KML plugin!" << std::endl;
            }
            URI newURI( uri.full() + "/doc.kml", uri.context() );
            return load( newURI, mapNode, dbOptions, kmlOptions );
        }

        if ( !mapNode ) {
            OE_WARN << "[KML] " << "MapNode instance required" << std::endl;
            return 0L;
        }
        osg::ref_ptr<osgDB::Options> options = Registry::instance()->cloneOrCreateOptions( dbOptions );
        options->setPluginData( "osgEarth::MapNode",    mapNode );
        options->setPluginData( "osgEarth::KMLOptions", (void*)&kmlOptions );

        return uri.getNode( options.get() );
    }

    static osg::Node* load(const URI&            uri, 
                            MapNode*              mapNode,
                            const KMLOptions&     kmlOptions =KMLOptions() )
    {
        return load(uri, mapNode, 0L, kmlOptions);
    }

} } // namespace osgEarth::KML

#endif // OSGEARTH_DRIVER_KML
