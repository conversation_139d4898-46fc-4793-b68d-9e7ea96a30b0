/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHFEATURES_BUILD_TEXT_FILTER_H
#define OSGEARTHFEATURES_BUILD_TEXT_FILTER_H 1

#include <osgEarth/Common>
#include <osgEarth/Filter>
#include <osgEarth/Style>

namespace osgEarth { namespace Util
{
    /**
     * Builds text labels from a stream of input features.
     */
    class OSGEARTH_EXPORT BuildTextFilter : public FeaturesToNodeFilter
    {
    public:
        BuildTextFilter( const Style& style =Style() );

        virtual ~BuildTextFilter() { }

        /** The style to apply to feature geometry */
        const Style& getStyle() { return _style; }
        void setStyle(const Style& s) { _style = s; }

        /** Pushes a list of features through the filter. */
        osg::Node* push( FeatureList& input, FilterContext& context );

    protected:
        Style _style;
    };
} }

#endif // OSGEARTHFEATURES_BUILD_GEOMETRY_FILTER_H
