cmake_minimum_required(VERSION 3.20)

# VCPKG customization ....................................................

if (${CMAKE_TOOLCHAIN_FILE} MATCHES ".*vcpkg.cmake.*")
    message(STATUS "Building with vcpkg toolchain.")
    set(USING_VCPKG ON)
endif()


# Project setup ..........................................................

project(
    OSGEARTH
    DESCRIPTION "osgEarth SDK"
    HOMEPAGE_URL "https://github.com/gwaldron/osgearth"
    LANGUAGES CXX C)

# SDK version number
set(OSGEARTH_MAJOR_VERSION 3)
set(OSGEARTH_MINOR_VERSION 7)
set(OSGEARTH_PATCH_VERSION 3)
set(OSGEARTH_VERSION ${OSGEARTH_MAJOR_VERSION}.${OSGEARTH_MINOR_VERSION}.${OSGEARTH_PATCH_VERSION})

# Increment this each time the ABI changes
set(OSGEARTH_SOVERSION 175)

# Require C++14, don't fall back, and don't use compiler-specific extensions:
set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)
set(CMAKE_CXX_EXTENSIONS OFF)

# Detect out-of-source build. We'll need this for protobuf generated files.
if (NOT "${PROJECT_SOURCE_DIR}" STREQUAL "${PROJECT_BINARY_DIR}")
    message(STATUS "Detected an out-of-source build. Kudos.")    
    set(OSGEARTH_OUT_OF_SOURCE_BUILD TRUE)
else()
    message(STATUS "Detected a in-source build.")  
endif()

# We have some custom .cmake scripts not in the official distribution.
set(CMAKE_MODULE_PATH "${CMAKE_MODULE_PATH};${PROJECT_SOURCE_DIR}/cmake")

# Special folder for build-time generated include files
set(OSGEARTH_BUILDTIME_INCLUDE_DIR "${CMAKE_CURRENT_BINARY_DIR}/build_include")
include_directories(${OSGEARTH_BUILDTIME_INCLUDE_DIR})

# Third-party sources included in the reposotory
set(OSGEARTH_EMBEDDED_THIRD_PARTY_DIR ${PROJECT_SOURCE_DIR}/src/third_party)

# Set output directories to redist_desk for direct deployment
set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)
set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)
set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY ${PROJECT_SOURCE_DIR}/redist_desk)

# For multi-config generators (Visual Studio)
foreach(OUTPUTCONFIG ${CMAKE_CONFIGURATION_TYPES})
    string(TOUPPER ${OUTPUTCONFIG} OUTPUTCONFIG)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
    set(CMAKE_ARCHIVE_OUTPUT_DIRECTORY_${OUTPUTCONFIG} ${PROJECT_SOURCE_DIR}/redist_desk)
endforeach()

include(GNUInstallDirs)

if(BUILDING_VCPKG_PORT)
    # this path taken when vcpkg is building from the portfile.
    set(OSGEARTH_INSTALL_PLUGINSDIR "plugins")
    set(OSGEARTH_INSTALL_CMAKEDIR "${CMAKE_INSTALL_DATADIR}/osgearth")
else()
    if(WIN32)
        set(OSGEARTH_INSTALL_PLUGINSDIR "${CMAKE_INSTALL_BINDIR}" CACHE STRING "Parent folder of OSG plugins folder")
    else()
        set(OSGEARTH_INSTALL_PLUGINSDIR "${CMAKE_INSTALL_LIBDIR}" CACHE STRING "Parent folder of OSG plugins folder")
    endif()
    set(OSGEARTH_INSTALL_CMAKEDIR "${CMAKE_INSTALL_LIBDIR}/cmake/osgearth" CACHE STRING "osgEarth CMake package install directory")
endif()

set(OSGEARTH_INSTALL_DATADIR "${CMAKE_INSTALL_DATADIR}/osgearth" CACHE STRING "osgEarth data directory")

# Platform-specific settings ............................................

include(oe_ios)
include(oe_osx)
include(oe_unix)
include(oe_win32)

# Build options ..........................................................

option(OSGEARTH_BUILD_TOOLS "Build the osgEarth command-line tools" ON)
option(OSGEARTH_BUILD_EXAMPLES "Build the osgEarth example applications" ON)
option(OSGEARTH_BUILD_IMGUI_NODEKIT "Build the osgEarth ImGui nodekit and ImGui-based apps" ON)
option(OSGEARTH_BUILD_CESIUM_NODEKIT "Build the Cesium nodekit (osgEarthCesium)" OFF)
option(OSGEARTH_BUILD_TRITON_NODEKIT "Build support for SunDog Triton SDK" OFF)
option(OSGEARTH_BUILD_SILVERLINING_NODEKIT "Build support for SunDog SilverLining SDK" OFF)
option(OSGEARTH_ENABLE_GEOCODER "Enable the geocoder (requires external geocoding service)" OFF)

option(OSGEARTH_BUILD_DOCS "Include the documentation folder" ON)
mark_as_advanced(OSGEARTH_BUILD_DOCS)

option(OSGEARTH_BUILD_TESTS "Build the osgEarth unit tests" OFF)
mark_as_advanced(OSGEARTH_BUILD_TESTS)

option(OSGEARTH_BUILD_PROCEDURAL_NODEKIT "Build the procedural terrain nodekit (osgEarthProcedural)" OFF)
mark_as_advanced(OSGEARTH_BUILD_PROCEDURAL_NODEKIT)

option(OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT "Build the legacy procedural nodekit (osgEarthSplat)" OFF)
mark_as_advanced(OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT)

option(OSGEARTH_BUILD_LEGACY_CONTROLS_API "Build the legacy Controls UI API" OFF)
mark_as_advanced(OSGEARTH_BUILD_LEGACY_CONTROLS_API)

option(OSGEARTH_BUILD_ZIP_PLUGIN "Build osgEarth's zip plugin based on libzip" ON)
mark_as_advanced(OSGEARTH_BUILD_ZIP_PLUGIN)

option(OSGEARTH_ENABLE_PROFILING "Enable profiling with Tracy" OFF)
mark_as_advanced(OSGEARTH_ENABLE_PROFILING)

option(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT "Assume the use of a single GL context for all GL objects (advanced)" OFF)
mark_as_advanced(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT)

option(OSGEARTH_ASSUME_SINGLE_THREADED_OSG "Assume OSG will always be configured to run in SingleThreaded mode (advanced)" OFF)
mark_as_advanced(OSGEARTH_ASSUME_SINGLE_THREADED_OSG)

option(OSGEARTH_INSTALL_SHADERS "Whether to deploy GLSL shaders when installing (OFF=inlined shaders)" OFF)
mark_as_advanced(OSGEARTH_INSTALL_SHADERS)

if(WIN32)
    option(OSGEARTH_INSTALL_PDBS "Whether to deploy Windows .pdb files" OFF)
endif()


# Shared v. static build .................................................

option(OSGEARTH_BUILD_SHARED_LIBS "ON to build shared libraries; OFF to build static libraries." ON)
if(OSGEARTH_BUILD_SHARED_LIBS)
    set(OSGEARTH_DYNAMIC_OR_STATIC "SHARED")
else()
    set(OSGEARTH_DYNAMIC_OR_STATIC "STATIC")
endif()
message(STATUS "Building ${OSGEARTH_DYNAMIC_OR_STATIC} libraries")


# Whether to append SOVERSIONs to libraries (unix)
option(OSGEARTH_SONAMES "ON to append so-version numbers to libraries" ON)


# Dependencies ...........................................................

# Update git submodules
# https://cliutils.gitlab.io/modern-cmake/chapters/projects/submodule.html
find_package(Git QUIET)
if(GIT_FOUND AND EXISTS "${PROJECT_SOURCE_DIR}/.git")
    message(STATUS "Submodule update")
    execute_process(COMMAND ${GIT_EXECUTABLE} submodule update --init --recursive
                    WORKING_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}
                    RESULT_VARIABLE GIT_SUBMOD_RESULT)
    if(NOT GIT_SUBMOD_RESULT EQUAL "0")
        message(FATAL_ERROR "git submodule update --init --recursive failed with ${GIT_SUBMOD_RESULT}, please checkout submodules")
    endif()
endif()

# required - globally used
find_package(OpenGL REQUIRED)

# WebAssembly specific dependencies handling
message(STATUS "EMSCRIPTEN: ${EMSCRIPTEN}")
message(STATUS "OSGEARTH_WEBASSEMBLY: ${OSGEARTH_WEBASSEMBLY}")
message(STATUS "CMAKE_TOOLCHAIN_FILE: ${CMAKE_TOOLCHAIN_FILE}")
if(EMSCRIPTEN OR OSGEARTH_WEBASSEMBLY)
    message(STATUS "Using WebAssembly configuration")
    # For WebAssembly, use pre-compiled dependencies - 更新路径
    set(WASM_DEP_ROOT "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
    set(WASM_INCLUDE_DIR "${WASM_DEP_ROOT}/include")
    set(WASM_LIB_DIR "${WASM_DEP_ROOT}/lib")

    # 如果wasm_dep不存在，使用vcpkg的WebAssembly依赖
    if(NOT EXISTS "${WASM_DEP_ROOT}")
        message(STATUS "wasm_dep not found, using vcpkg WebAssembly dependencies")
        set(WASM_DEP_ROOT "C:/dev/vcpkg/installed/wasm32-emscripten")
        set(WASM_INCLUDE_DIR "${WASM_DEP_ROOT}/include")
        set(WASM_LIB_DIR "${WASM_DEP_ROOT}/lib")
    endif()
    
    # Set OpenSceneGraph variables directly
    set(OPENSCENEGRAPH_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OPENSCENEGRAPH_VERSION "3.7.0")
    set(OPENSCENEGRAPH_MAJOR_VERSION "3")
    set(OPENSCENEGRAPH_MINOR_VERSION "7")
    set(OPENSCENEGRAPH_PATCH_VERSION "0")
    
    # Set individual library variables
    set(OSG_LIBRARY "${WASM_LIB_DIR}/libosg.a")
    set(OSG_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGDB_LIBRARY "${WASM_LIB_DIR}/libosgDB.a")
    set(OSGDB_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGGA_LIBRARY "${WASM_LIB_DIR}/libosgGA.a")
    set(OSGGA_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGUTIL_LIBRARY "${WASM_LIB_DIR}/libosgUtil.a")
    set(OSGUTIL_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGVIEWER_LIBRARY "${WASM_LIB_DIR}/libosgViewer.a")
    set(OSGVIEWER_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGTEXT_LIBRARY "${WASM_LIB_DIR}/libosgText.a")
    set(OSGTEXT_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGSHADOW_LIBRARY "${WASM_LIB_DIR}/libosgShadow.a")
    set(OSGSHADOW_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGSIM_LIBRARY "${WASM_LIB_DIR}/libosgSim.a")
    set(OSGSIM_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OSGMANIPULATOR_LIBRARY "${WASM_LIB_DIR}/libosgManipulator.a")
    set(OSGMANIPULATOR_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(OPENTHREADS_LIBRARY "${WASM_LIB_DIR}/libOpenThreads.a")
    set(OPENTHREADS_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    
    # Set FOUND flags
    set(OSG_FOUND TRUE)
    set(OSGDB_FOUND TRUE)
    set(OSGGA_FOUND TRUE)
    set(OSGUTIL_FOUND TRUE)
    set(OSGVIEWER_FOUND TRUE)
    set(OSGTEXT_FOUND TRUE)
    set(OSGSHADOW_FOUND TRUE)
    set(OSGSIM_FOUND TRUE)
    set(OSGMANIPULATOR_FOUND TRUE)
    set(OPENTHREADS_FOUND TRUE)
    set(OpenSceneGraph_FOUND TRUE)
    
    # Set libraries list
    set(OPENSCENEGRAPH_LIBRARIES
        ${OSG_LIBRARY}
        ${OSGDB_LIBRARY}
        ${OSGGA_LIBRARY}
        ${OSGUTIL_LIBRARY}
        ${OSGVIEWER_LIBRARY}
        ${OSGTEXT_LIBRARY}
        ${OSGSHADOW_LIBRARY}
        ${OSGSIM_LIBRARY}
        ${OSGMANIPULATOR_LIBRARY}
        ${OPENTHREADS_LIBRARY}
    )
    
    # Add include directories
    include_directories(${WASM_INCLUDE_DIR})
    
    # Add library directories
    link_directories(${WASM_LIB_DIR})
    link_directories(${WASM_LIB_DIR}/osgPlugins-3.7.0)
    
    message(STATUS "Using WebAssembly pre-compiled dependencies from ${WASM_DEP_ROOT}")
else()
    # Normal desktop build
    find_package(OpenSceneGraph REQUIRED COMPONENTS osgManipulator osgShadow osgSim osgViewer osgGA osgUtil osgText osgDB osg OpenThreads)
endif()

# For static builds we need to link with Fontconfig directly.
# There is an unknown issue where the Fc symbols are not found when linking executables statically.
if(NOT OSGEARTH_BUILD_SHARED_LIBS)
    find_package(Fontconfig QUIET)
    if (Fontconfig_FOUND)
        list(APPEND OPENSCENEGRAPH_LIBRARIES Fontconfig::Fontconfig)
    endif()
endif()

# integrated profiling with tracy?
if(OSGEARTH_ENABLE_PROFILING)
    find_package(Tracy)
    if(Tracy_FOUND)
        message(STATUS "Found Tracy. Enabling frame profiling.")
    endif()
endif()

# Find SuperLuminalAPI
find_package(SuperluminalAPI QUIET)

# optimization option:
if(OSGEARTH_ASSUME_SINGLE_GL_CONTEXT)
    add_definitions(-DOSGEARTH_SINGLE_GL_CONTEXT)
endif()

# optimization option:
if(OSGEARTH_ASSUME_SINGLE_THREADED_OSG)
    add_definitions(-DOSGEARTH_SINGLE_THREADED_OSG)
endif()

# Bring in our utility macros that sub-projects will use to configure themselves.
include(cmake/osgearth-macros.cmake)
include(cmake/install-package-config-files.cmake)

# Detect the OSG version so we can append it to plugin DLLs just like OSG does.
detect_osg_version()

if(NOT OPENSCENEGRAPH_VERSION)
	set(OPENSCENEGRAPH_VERSION ${OPENSCENEGRAPH_MAJOR_VERSION}.${OPENSCENEGRAPH_MINOR_VERSION}.${OPENSCENEGRAPH_PATCH_VERSION})
endif()
message(STATUS "Found OSG version ${OPENSCENEGRAPH_VERSION}")

# Make the headers visible to everything
include_directories(
    ${OSGEARTH_SOURCE_DIR}/src
    ${OPENSCENEGRAPH_INCLUDE_DIR})
    
# If OSG is built with OPENGL_PROFILE=GLCORE, <osg/GL> will try to include the GLCORE ARB headers.
# Set this variable to make those headers available when building osgEarth.
set(OSGEARTH_GLCORE_INCLUDE_DIR "" CACHE PATH "Location of OpenGL CORE profile header parent folder")
mark_as_advanced(OSGEARTH_GLCORE_INCLUDE_DIR)
if(OSGEARTH_GLCORE_INCLUDE_DIR)
    include_directories(${OSGEARTH_GLCORE_INCLUDE_DIR})
endif()


# Source code ............................................................

add_subdirectory(src)

# 添加测试程序
add_executable(test_xyz_fix test_xyz_fix.cpp)
target_link_libraries(test_xyz_fix osgEarth ${OSG_LIBRARIES} ${OPENTHREADS_LIBRARIES})
target_include_directories(test_xyz_fix PRIVATE src)

# 添加LOD逻辑测试程序
add_executable(test_lod_logic test_lod_logic.cpp)
target_link_libraries(test_lod_logic osgEarth ${OSG_LIBRARIES} ${OPENTHREADS_LIBRARIES})
target_include_directories(test_lod_logic PRIVATE src)

if(OSGEARTH_BUILD_DOCS)
    add_subdirectory(docs)
endif()
    
    
# IDE configuration ......................................................

set_property(GLOBAL PROPERTY USE_FOLDERS ON)
set_property(GLOBAL PROPERTY PREDEFINED_TARGETS_FOLDER "CMake Targets")


# Uninstall target .......................................................

configure_file(
   "${CMAKE_CURRENT_SOURCE_DIR}/cmake/cmake_uninstall.cmake.in"
   "${CMAKE_CURRENT_BINARY_DIR}/uninstall.cmake"
   IMMEDIATE @ONLY
)

add_custom_target(uninstall
   "${CMAKE_COMMAND}" -P "${CMAKE_CURRENT_BINARY_DIR}/uninstall.cmake"
)
