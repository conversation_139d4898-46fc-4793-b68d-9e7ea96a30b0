#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
HTTP Server for WebAssembly Applications with CORS Support
支持跨域访问的HTTP服务器，专为WebAssembly应用设计
"""

import os
import sys
import argparse
import webbrowser
import threading
import time
from http.server import HTTPServer, SimpleHTTPRequestHandler
import mimetypes

class CORSHTTPRequestHandler(SimpleHTTPRequestHandler):
    """
    支持CORS的HTTP请求处理器
    """
    
    def __init__(self, *args, **kwargs):
        # 设置正确的MIME类型
        mimetypes.add_type('application/wasm', '.wasm')
        mimetypes.add_type('application/javascript', '.js')
        mimetypes.add_type('text/html', '.html')
        mimetypes.add_type('text/xml', '.earth')
        mimetypes.add_type('application/json', '.json')
        mimetypes.add_type('text/plain', '.txt')
        super().__init__(*args, **kwargs)
    
    def end_headers(self):
        """添加CORS头部信息"""
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, PUT, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type, Authorization, Range')
        self.send_header('Access-Control-Allow-Credentials', 'true')
        
        # 为WebAssembly添加特殊的头部
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        
        # 缓存控制
        if self.path.endswith('.wasm'):
            self.send_header('Cache-Control', 'no-cache')
        elif self.path.endswith('.js'):
            self.send_header('Cache-Control', 'no-cache')
        elif self.path.endswith('.earth'):
            self.send_header('Cache-Control', 'no-cache')
            self.send_header('Content-Type', 'text/xml; charset=utf-8')
        elif self.path.endswith('.json'):
            self.send_header('Content-Type', 'application/json; charset=utf-8')
            
        super().end_headers()
    
    def do_OPTIONS(self):
        """处理OPTIONS请求"""
        self.send_response(200, "OK")
        self.end_headers()
    
    def do_GET(self):
        """处理GET请求"""
        # 如果请求根路径，重定向到index.html
        if self.path == '/':
            self.path = '/index.html'
        
        # 记录请求
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] GET {self.path}")
        
        # 检查文件是否存在
        file_path = self.path.lstrip('/')
        if os.path.exists(file_path):
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 文件存在: {file_path}")
        else:
            print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] 文件不存在: {file_path}")
        
        return super().do_GET()
    
    def guess_type(self, path):
        """改进的文件类型推断"""
        mimetype, encoding = mimetypes.guess_type(path)
        
        # 特殊处理
        if path.endswith('.earth'):
            return 'text/xml'
        elif path.endswith('.wasm'):
            return 'application/wasm'
        elif path.endswith('.js'):
            return 'application/javascript'
        elif path.endswith('.json'):
            return 'application/json'
        
        return mimetype
    
    def log_message(self, format, *args):
        """自定义日志格式"""
        print(f"[{time.strftime('%Y-%m-%d %H:%M:%S')}] {format % args}")


def open_browser(url, delay=2):
    """延迟打开浏览器"""
    time.sleep(delay)
    print(f"正在打开浏览器: {url}")
    webbrowser.open(url)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='WebAssembly HTTP Server with CORS Support')
    parser.add_argument('--port', '-p', type=int, default=8000, help='服务器端口 (默认: 8000)')
    parser.add_argument('--host', '-H', default='localhost', help='服务器主机 (默认: localhost)')
    parser.add_argument('--no-browser', '-n', action='store_true', help='不自动打开浏览器')
    parser.add_argument('--directory', '-d', default='.', help='服务目录 (默认: 当前目录)')
    
    args = parser.parse_args()
    
    # 切换到指定目录
    os.chdir(args.directory)
    
    # 检查必要的文件是否存在
    required_files = ['index.html', 'osgearth_myviewer.js', 'osgearth_myviewer.wasm']
    missing_files = []
    
    for file in required_files:
        if not os.path.exists(file):
            missing_files.append(file)
    
    if missing_files:
        print(f"错误: 缺少必要文件: {', '.join(missing_files)}")
        print("请确保在正确的目录中运行服务器")
        sys.exit(1)
    
    # 检查配置文件
    config_files = ['myviewer_config.earth']
    for config_file in config_files:
        if os.path.exists(config_file):
            print(f"找到配置文件: {config_file}")
        else:
            print(f"警告: 配置文件不存在: {config_file}")
    
    # 创建HTTP服务器
    server_address = (args.host, args.port)
    httpd = HTTPServer(server_address, CORSHTTPRequestHandler)
    
    url = f"http://{args.host}:{args.port}"
    
    print("=" * 60)
    print(f"启动 osgEarth WebAssembly HTTP 服务器")
    print(f"服务器地址: {url}")
    print(f"服务目录: {os.path.abspath(args.directory)}")
    print("=" * 60)
    print("支持的文件类型:")
    print("  - HTML文件 (.html)")
    print("  - JavaScript文件 (.js)")
    print("  - WebAssembly文件 (.wasm)")
    print("  - 配置文件 (.earth)")
    print("  - JSON文件 (.json)")
    print("=" * 60)
    
    # 列出目录中的重要文件
    print("目录中的关键文件:")
    for file in os.listdir('.'):
        if file.endswith(('.html', '.js', '.wasm', '.earth', '.json')):
            size = os.path.getsize(file)
            print(f"  - {file} ({size:,} bytes)")
    
    print("=" * 60)
    print("按 Ctrl+C 停止服务器")
    print("=" * 60)
    
    # 自动打开浏览器
    if not args.no_browser:
        browser_thread = threading.Thread(target=open_browser, args=(url,))
        browser_thread.daemon = True
        browser_thread.start()
    
    try:
        httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n正在停止服务器...")
        httpd.shutdown()
        print("服务器已停止")


if __name__ == '__main__':
    main() 