<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>HTTP头验证测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .test-result {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .pass { color: #90ee90; }
        .fail { color: #ff6b6b; }
        .info { color: #87ceeb; }
        .test-button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 HTTP头验证测试</h1>
        
        <div id="status" class="status">状态: 准备检查HTTP头...</div>
        
        <div class="status">
            <h3>🌐 浏览器环境检查</h3>
            <div id="browser-info">检查中...</div>
        </div>
        
        <div class="status">
            <h3>🔧 SharedArrayBuffer支持检查</h3>
            <div id="sab-support">检查中...</div>
        </div>
        
        <div class="status">
            <h3>📡 HTTP头检查</h3>
            <button id="check-headers" class="test-button">检查HTTP头</button>
            <div id="header-results"></div>
        </div>
        
        <div class="test-result">
            <h3>📝 详细测试结果</h3>
            <div id="test-output"></div>
        </div>
    </div>

    <script>
        function addOutput(text, className = 'info') {
            const outputEl = document.getElementById('test-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = className;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // 检查浏览器环境
        function checkBrowserEnvironment() {
            const browserInfo = document.getElementById('browser-info');
            let html = '';
            
            // 浏览器信息
            html += `<strong>浏览器:</strong> ${navigator.userAgent}<br>`;
            html += `<strong>平台:</strong> ${navigator.platform}<br>`;
            html += `<strong>语言:</strong> ${navigator.language}<br>`;
            html += `<strong>在线状态:</strong> ${navigator.onLine ? '在线' : '离线'}<br>`;
            
            // 检查关键API
            html += `<strong>Worker支持:</strong> ${typeof Worker !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            html += `<strong>WebAssembly支持:</strong> ${typeof WebAssembly !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            
            browserInfo.innerHTML = html;
            addOutput('浏览器环境检查完成', 'info');
        }
        
        // 检查SharedArrayBuffer支持
        function checkSharedArrayBufferSupport() {
            const sabSupport = document.getElementById('sab-support');
            let html = '';
            let allPassed = true;
            
            // 检查SharedArrayBuffer
            if (typeof SharedArrayBuffer !== 'undefined') {
                html += '✅ <strong>SharedArrayBuffer:</strong> 支持<br>';
                addOutput('SharedArrayBuffer支持检查: 通过', 'pass');
            } else {
                html += '❌ <strong>SharedArrayBuffer:</strong> 不支持<br>';
                addOutput('SharedArrayBuffer支持检查: 失败', 'fail');
                allPassed = false;
            }
            
            // 检查Atomics
            if (typeof Atomics !== 'undefined') {
                html += '✅ <strong>Atomics:</strong> 支持<br>';
                addOutput('Atomics支持检查: 通过', 'pass');
            } else {
                html += '❌ <strong>Atomics:</strong> 不支持<br>';
                addOutput('Atomics支持检查: 失败', 'fail');
                allPassed = false;
            }
            
            // 尝试创建SharedArrayBuffer
            try {
                const sab = new SharedArrayBuffer(1024);
                html += '✅ <strong>SharedArrayBuffer创建:</strong> 成功<br>';
                addOutput('SharedArrayBuffer创建测试: 通过', 'pass');
            } catch (e) {
                html += `❌ <strong>SharedArrayBuffer创建:</strong> 失败 (${e.message})<br>`;
                addOutput(`SharedArrayBuffer创建测试: 失败 - ${e.message}`, 'fail');
                allPassed = false;
            }
            
            if (!allPassed) {
                html += '<br>⚠️ <strong>可能的原因:</strong><br>';
                html += '• HTTP头设置不正确<br>';
                html += '• 浏览器不支持或被禁用<br>';
                html += '• 需要HTTPS或localhost<br>';
            }
            
            sabSupport.innerHTML = html;
            
            if (allPassed) {
                updateStatus('SharedArrayBuffer支持检查: 全部通过', false, true);
            } else {
                updateStatus('SharedArrayBuffer支持检查: 存在问题', true);
            }
        }
        
        // 检查HTTP头
        async function checkHTTPHeaders() {
            const headerResults = document.getElementById('header-results');
            headerResults.innerHTML = '<div class="info">正在检查HTTP头...</div>';
            
            try {
                // 获取当前页面的响应头
                const response = await fetch(window.location.href, {
                    method: 'HEAD',
                    cache: 'no-cache'
                });
                
                let html = '<h4>📡 HTTP响应头:</h4>';
                let hasRequiredHeaders = true;
                
                // 检查关键头
                const requiredHeaders = {
                    'cross-origin-embedder-policy': 'require-corp',
                    'cross-origin-opener-policy': 'same-origin'
                };
                
                for (const [header, expectedValue] of Object.entries(requiredHeaders)) {
                    const actualValue = response.headers.get(header);
                    if (actualValue === expectedValue) {
                        html += `✅ <strong>${header}:</strong> ${actualValue}<br>`;
                        addOutput(`HTTP头检查 ${header}: 通过`, 'pass');
                    } else {
                        html += `❌ <strong>${header}:</strong> ${actualValue || '未设置'} (期望: ${expectedValue})<br>`;
                        addOutput(`HTTP头检查 ${header}: 失败 - 实际值: ${actualValue || '未设置'}`, 'fail');
                        hasRequiredHeaders = false;
                    }
                }
                
                // 显示其他相关头
                const otherHeaders = ['cross-origin-resource-policy', 'cache-control'];
                html += '<h4>📋 其他相关头:</h4>';
                for (const header of otherHeaders) {
                    const value = response.headers.get(header);
                    html += `<strong>${header}:</strong> ${value || '未设置'}<br>`;
                }
                
                // 显示所有头（调试用）
                html += '<h4>🔍 所有响应头:</h4>';
                html += '<div style="font-size: 11px; color: #ccc;">';
                for (const [key, value] of response.headers.entries()) {
                    html += `${key}: ${value}<br>`;
                }
                html += '</div>';
                
                headerResults.innerHTML = html;
                
                if (hasRequiredHeaders) {
                    updateStatus('HTTP头检查: 全部正确', false, true);
                    addOutput('HTTP头配置正确，SharedArrayBuffer应该可用', 'pass');
                } else {
                    updateStatus('HTTP头检查: 缺少必需的头', true);
                    addOutput('HTTP头配置不正确，这是SharedArrayBuffer不可用的原因', 'fail');
                }
                
            } catch (error) {
                headerResults.innerHTML = `<div class="fail">HTTP头检查失败: ${error.message}</div>`;
                addOutput(`HTTP头检查失败: ${error.message}`, 'fail');
                updateStatus('HTTP头检查失败', true);
            }
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addOutput('页面加载完成，开始HTTP头验证测试', 'info');
            
            // 检查浏览器环境
            checkBrowserEnvironment();
            
            // 检查SharedArrayBuffer支持
            checkSharedArrayBufferSupport();
            
            // 绑定按钮事件
            document.getElementById('check-headers').addEventListener('click', checkHTTPHeaders);
            
            updateStatus('HTTP头验证测试就绪');
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addOutput(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'fail');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addOutput(`未处理的Promise拒绝: ${event.reason}`, 'fail');
        });
    </script>
</body>
</html>
