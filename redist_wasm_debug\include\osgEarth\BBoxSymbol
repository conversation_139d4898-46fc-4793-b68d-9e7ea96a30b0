/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHSYMBOLOGY_BBOX_SYMBOL_H
#define OSGEARTHSYMBOLOGY_BBOX_SYMBOL_H 1

#include <osgEarth/Symbol>
#include <osgEarth/Expression>
#include <osgEarth/Fill>
#include <osgEarth/Stroke>

namespace osgEarth
{
    /**
     * Symbol that describes how to render bounding boxes around text labels.
     */
    class OSGEARTH_EXPORT BBoxSymbol : public Symbol
    {
    public:

        enum BboxGeom {
            GEOM_BOX,
            GEOM_BOX_ORIENTED
        };

        META_Object(osgEarth, BBoxSymbol);
    	
        BBoxSymbol(const BBoxSymbol& rhs,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);
        BBoxSymbol( const Config& conf =Config() );

        /** dtor */
        virtual ~BBoxSymbol() { }

        /** Bounding box fill color. */
        optional<Fill>& fill() { return _fill; }
        const optional<Fill>& fill() const { return _fill; }

        /** Bounding box border color. */
        optional<Stroke>& border() { return _border; }
        const optional<Stroke>& border() const { return _border; }

        /** Bounding box border margin. */
        optional<float>& margin() { return _margin; }
        const optional<float>& margin() const { return _margin; }

        /** Bounding box geom to draw. */
        optional<BboxGeom>& geom() { return _bboxGeom; }
        const optional<BboxGeom>& geom() const { return _bboxGeom; }

    public:
        virtual Config getConfig() const;
        virtual void mergeConfig( const Config& conf );
        static void parseSLD(const Config& c, class Style& style);

    protected:
        optional<Fill>              _fill;
        optional<Stroke>            _border;
        optional<float>             _margin;
        optional<BboxGeom>          _bboxGeom;
    };
} // namespace osgEarth

#endif // OSGEARTHSYMBOLOGY_BBOX_SYMBOL_H
