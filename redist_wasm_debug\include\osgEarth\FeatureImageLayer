/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/ImageLayer>
#include <osgEarth/LayerReference>
#include <osgEarth/FeatureSource>
#include <osgEarth/StyleSheet>
#include <osgEarth/FeatureRasterizer>

namespace osgEarth
{
    /**
     * Rasterizes feature data into an image layer.
     * Styles will render in their order of appearance in the stylesheet;
     * e.g. features matching the last style will render on top.
     */
    class OSGEARTH_EXPORT FeatureImageLayer : public osgEarth::ImageLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public ImageLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, ImageLayer::Options);
            OE_OPTION_LAYER(FeatureSource, featureSource);
            OE_OPTION_VECTOR(ConfigOptions, filters);
            OE_OPTION_LAYER(StyleSheet, styleSheet);
            OE_OPTION(Distance, bufferWidth, {});
            OE_OPTION(double, gamma);
            OE_OPTION(bool, sdf);
            OE_OPTION(bool, sdf_invert);
            virtual Config getConfig() const;
        private:
            void fromConfig( const Config& conf );
        };

    public:
        META_Layer(osgEarth, FeatureImageLayer, Options, osgEarth::ImageLayer, FeatureImage);

        //! Sets the feature source to get road data from; call either this
        //! or setFeatureSourceLayer
        void setFeatureSource(FeatureSource* source);
        inline FeatureSource* getFeatureSource() const;

        //! Style sheet to use to render feature data
        void setStyleSheet(StyleSheet* styles);
        inline StyleSheet* getStyleSheet() const;

    public: // ImageLayer

        // Opens the layer and returns a status
        virtual Status openImplementation() override;

        virtual GeoImage createImageImplementation(
            const TileKey& key, 
            ProgressCallback* progress) const override;

    protected: // Layer

        // Called by Map when it adds this layer
        virtual void addedToMap(const class Map*) override;

        // Called by Map when it removes this layer
        virtual void removedFromMap(const class Map*) override;

        // post-ctor initialization
        virtual void init() override;

        virtual ~FeatureImageLayer() { }

    private:
        optional<double> _gamma;

        void updateSession();

        void establishProfile();

        struct Isolate {
            Isolate() { }
            Isolate(const Isolate&);
            osg::ref_ptr<Session> _session;
            FeatureFilterChain _filterChain;
        };

        Isolate _global;
    };

    // template/inline impls .................................................

    FeatureSource* FeatureImageLayer::getFeatureSource() const { return options().featureSource().getLayer(); }
    StyleSheet* FeatureImageLayer::getStyleSheet() const { return options().styleSheet().getLayer(); }
} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::FeatureImageLayer::Options);
