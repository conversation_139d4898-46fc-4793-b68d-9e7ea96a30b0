@echo off
setlocal enabledelayedexpansion

echo ========================================
echo Emscripten Installation Script
echo ========================================

:: Set installation directory
set EMSDK_ROOT=C:\dev\emsdk

:: Check if already installed
if exist "%EMSDK_ROOT%" (
    echo Emscripten already exists at %EMSDK_ROOT%
    echo Updating existing installation...
    cd /d "%EMSDK_ROOT%"
    git pull
) else (
    echo Installing Emscripten to %EMSDK_ROOT%
    
    :: Create dev directory
    if not exist "C:\dev" mkdir "C:\dev"
    
    :: Clone emsdk repository
    cd /d "C:\dev"
    git clone https://github.com/emscripten-core/emsdk.git
    
    if errorlevel 1 (
        echo Error: Failed to clone emsdk repository
        echo Please ensure git is installed and available
        pause
        exit /b 1
    )
    
    cd /d "%EMSDK_ROOT%"
)

:: Install and activate latest emscripten
echo Installing latest Emscripten...
emsdk install latest

if errorlevel 1 (
    echo Error: Failed to install Emscripten
    pause
    exit /b 1
)

echo Activating Emscripten...
emsdk activate latest

if errorlevel 1 (
    echo Error: Failed to activate Emscripten
    pause
    exit /b 1
)

:: Set environment for current session
echo Setting up environment...
call emsdk_env.bat

:: Verify installation
echo Verifying installation...
emcc --version

if errorlevel 1 (
    echo Error: emcc verification failed
    pause
    exit /b 1
)

echo ========================================
echo Emscripten installation completed!
echo ========================================
echo Installation directory: %EMSDK_ROOT%
echo.
echo To use Emscripten in new command prompt sessions:
echo 1. Run: %EMSDK_ROOT%\emsdk_env.bat
echo 2. Or add %EMSDK_ROOT%\upstream\emscripten to your PATH
echo.
echo You can now run the WebAssembly build script.

pause
