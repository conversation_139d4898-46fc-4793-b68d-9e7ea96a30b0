/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/GeoCommon>
#include <osgEarth/Bounds>
#include <osgEarth/Units>
#include <osg/Referenced>

namespace osg
{
    class HeightField;
}

namespace osgEarth
{
    /**
     * An equipotential surface representing a gravitational model of the
     * planet's surface. Each value in the geoid's height field is an offset
     * from the reference ellipsoid.
     */
    class OSGEARTH_EXPORT Geoid : public osg::Referenced
    {
    public:
        Geoid();

        /** Gets the readable name of this geoid. */
        void setName( const std::string& value );
        const std::string& getName() const { return _name; }

        /**
         * Sets the heightfield representing this geoid. The heightfield must be referenced
         * as a lat/long grid (with the origin and intervals in degrees).
         */
        void setHeightField( osg::HeightField* hf );
        osg::HeightField* getHeightField() { return _hf.get(); }
        const osg::HeightField* getHeightField() const { return _hf.get(); }

        /**
         * Queries the geoid for the height offset at the specified geodetic
         * coordinates (in degrees).
         */
        float getHeight(
            double lat_deg,
            double lon_deg, 
            const RasterInterpolation& interp =INTERP_BILINEAR) const;

        /** The linear units in which height values are expressed. */
        const UnitsType& getUnits() const { return _units; }
        void setUnits( const UnitsType& value );

        /** Whether this is a valid object to use */
        bool isValid() const { return _valid; }

        /** True if two geoids are mathmatically equivalent. */
        bool isEquivalentTo( const Geoid& rhs ) const;

    private:
        std::string    _name;
        UnitsType      _units;
        bool           _valid;
        Bounds         _bounds;

        osg::ref_ptr<osg::HeightField> _hf;

        void validate();
    };
}
