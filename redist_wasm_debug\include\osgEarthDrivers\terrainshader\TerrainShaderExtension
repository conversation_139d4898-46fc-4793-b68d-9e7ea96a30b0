/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_EXT_TERRAIN_SHADER_H
#define OSGEARTH_EXT_TERRAIN_SHADER_H 1

#include "TerrainShaderOptions"
#include <osgEarth/Extension>
#include <osgEarth/MapNode>
#include <osgEarth/TerrainEffect>

namespace osgEarth { namespace TerrainShader
{
    using namespace osgEarth;

    class TerrainShaderExtension : public Extension,
                                   public ExtensionInterface<MapNode>,
                                   public TerrainShaderOptions
    {
    public:
        META_OE_Extension(osgEarth, TerrainShaderExtension, terrainshader);

        // CTORs
        TerrainShaderExtension();
        TerrainShaderExtension(const TerrainShaderOptions& options);

        // DTOR
        virtual ~TerrainShaderExtension();


    public: // Extension

        virtual void setDBOptions(const osgDB::Options* dbOptions);

        virtual const ConfigOptions& getConfigOptions() const { return *this; }

    public: // ExtensionInterface<MapNode>

        bool connect(MapNode* mapNode);

        bool disconnect(MapNode* mapNode);

    private:
        osg::ref_ptr<const osgDB::Options> _dbOptions;
        osg::ref_ptr<TerrainEffect>        _effect;
    };

} } // namespace osgEarth::TerrainShader

#endif // OSGEARTH_EXT_TERRAIN_SHADER_H
