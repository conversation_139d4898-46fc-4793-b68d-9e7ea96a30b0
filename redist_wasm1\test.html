<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>WebAssembly 文件测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .file-info {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .success { background-color: #d4edda; border-color: #c3e6cb; }
        .error { background-color: #f8d7da; border-color: #f5c6cb; }
        .info { background-color: #d1ecf1; border-color: #bee5eb; }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>osgEarth WebAssembly 文件测试</h1>
        
        <button onclick="checkFiles()">检查文件</button>
        <button onclick="testLoad()">测试加载</button>
        <button onclick="window.open('index.html', '_blank')">打开主应用</button>
        
        <div id="results"></div>
    </div>

    <script>
        async function checkFiles() {
            const files = [
                'osgearth_myviewer.js',
                'osgearth_myviewer.wasm',
                'myviewer_config.earth',
                'china_boundaries.geojson',
                'shell.html'
            ];
            
            const results = document.getElementById('results');
            results.innerHTML = '<h3>文件检查结果:</h3>';
            
            for (const file of files) {
                try {
                    const response = await fetch(file, { method: 'HEAD' });
                    const size = response.headers.get('content-length');
                    const lastModified = response.headers.get('last-modified');
                    
                    if (response.ok) {
                        results.innerHTML += `
                            <div class="file-info success">
                                <strong>✓ ${file}</strong><br>
                                大小: ${size ? formatBytes(parseInt(size)) : '未知'}<br>
                                修改时间: ${lastModified || '未知'}
                            </div>
                        `;
                    } else {
                        results.innerHTML += `
                            <div class="file-info error">
                                <strong>✗ ${file}</strong><br>
                                状态: ${response.status} ${response.statusText}
                            </div>
                        `;
                    }
                } catch (error) {
                    results.innerHTML += `
                        <div class="file-info error">
                            <strong>✗ ${file}</strong><br>
                            错误: ${error.message}
                        </div>
                    `;
                }
            }
        }
        
        function formatBytes(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }
        
        async function testLoad() {
            const results = document.getElementById('results');
            results.innerHTML += '<h3>加载测试:</h3>';
            
            try {
                // 测试JavaScript文件
                results.innerHTML += '<div class="file-info info">正在测试JavaScript文件加载...</div>';
                const jsResponse = await fetch('osgearth_myviewer.js');
                if (jsResponse.ok) {
                    const jsText = await jsResponse.text();
                    const hasModule = jsText.includes('Module');
                    const hasWasm = jsText.includes('wasm');
                    
                    results.innerHTML += `
                        <div class="file-info ${hasModule && hasWasm ? 'success' : 'error'}">
                            JavaScript文件: ${hasModule && hasWasm ? '✓ 包含必要的WebAssembly代码' : '✗ 可能不完整'}
                        </div>
                    `;
                }
                
                // 测试WebAssembly文件
                results.innerHTML += '<div class="file-info info">正在测试WebAssembly文件加载...</div>';
                const wasmResponse = await fetch('osgearth_myviewer.wasm');
                if (wasmResponse.ok) {
                    const wasmBuffer = await wasmResponse.arrayBuffer();
                    const isValidWasm = wasmBuffer.byteLength > 0 && 
                                       new Uint8Array(wasmBuffer, 0, 4).join(',') === '0,97,115,109'; // WASM magic number
                    
                    results.innerHTML += `
                        <div class="file-info ${isValidWasm ? 'success' : 'error'}">
                            WebAssembly文件: ${isValidWasm ? '✓ 有效的WASM文件' : '✗ 无效的WASM文件'}
                        </div>
                    `;
                }
                
            } catch (error) {
                results.innerHTML += `
                    <div class="file-info error">
                        加载测试失败: ${error.message}
                    </div>
                `;
            }
        }
        
        // 页面加载时自动检查文件
        window.addEventListener('load', checkFiles);
    </script>
</body>
</html>
