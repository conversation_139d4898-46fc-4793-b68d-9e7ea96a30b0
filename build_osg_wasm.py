#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSG库WebAssembly编译脚本
编译OpenSceneGraph为WebAssembly版本
"""

import os
import subprocess
import sys

def run_command(cmd, description, cwd=None):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print(f"工作目录: {cwd if cwd else os.getcwd()}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🚀 OSG库WebAssembly编译器")
    print("========================")
    print("编译OpenSceneGraph为WebAssembly版本")
    
    # 设置路径
    base_dir = "F:\\cmo-dev\\my_osgearth_web"
    osg_source_dir = f"{base_dir}\\OpenSceneGraph"
    osg_build_dir = f"{base_dir}\\osg_wasm_build"
    install_dir = f"{base_dir}\\osgearth_third_party\\wasm_dep"
    
    print(f"OSG源码目录: {osg_source_dir}")
    print(f"构建目录: {osg_build_dir}")
    print(f"安装目录: {install_dir}")
    
    # 检查源码目录
    if not os.path.exists(osg_source_dir):
        print(f"❌ OSG源码目录不存在: {osg_source_dir}")
        print("请先下载OpenSceneGraph源码")
        return False
    
    # 创建构建目录
    if not os.path.exists(osg_build_dir):
        os.makedirs(osg_build_dir)
        print(f"✅ 创建构建目录: {osg_build_dir}")
    
    # 创建安装目录
    if not os.path.exists(install_dir):
        os.makedirs(install_dir)
        print(f"✅ 创建安装目录: {install_dir}")
    
    # 检查Emscripten
    emcc_check = "C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return False
    
    # 配置CMake
    cmake_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcmake cmake "{osg_source_dir}" ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX="{install_dir}" ^
        -DOSG_GL1_AVAILABLE=OFF ^
        -DOSG_GL2_AVAILABLE=OFF ^
        -DOSG_GLES2_AVAILABLE=ON ^
        -DOSG_GL3_AVAILABLE=OFF ^
        -DOSG_GL_LIBRARY_STATIC=ON ^
        -DOSG_GL_DISPLAYLISTS_AVAILABLE=OFF ^
        -DOSG_GL_MATRICES_AVAILABLE=OFF ^
        -DOSG_GL_VERTEX_FUNCS_AVAILABLE=OFF ^
        -DOSG_GL_VERTEX_ARRAY_FUNCS_AVAILABLE=ON ^
        -DOSG_GL_FIXED_FUNCTION_AVAILABLE=OFF ^
        -DDYNAMIC_OPENSCENEGRAPH=OFF ^
        -DBUILD_OSG_APPLICATIONS=OFF ^
        -DBUILD_OSG_EXAMPLES=OFF ^
        -DBUILD_OSG_PLUGINS_BY_DEFAULT=OFF ^
        -DBUILD_OSG_PLUGIN_OSG=ON ^
        -DBUILD_OSG_PLUGIN_SERIALIZERS=ON ^
        -DBUILD_OSG_PLUGIN_IVE=ON ^
        -DBUILD_OSG_PLUGIN_RGB=ON ^
        -DBUILD_OSG_PLUGIN_JPEG=OFF ^
        -DBUILD_OSG_PLUGIN_PNG=OFF ^
        -DBUILD_OSG_PLUGIN_TIFF=OFF ^
        -DOSG_USE_FLOAT_MATRIX=ON ^
        -DOSG_USE_FLOAT_PLANE=ON ^
        -DOSG_USE_FLOAT_BOUNDINGSPHERE=ON ^
        -DOSG_USE_FLOAT_BOUNDINGBOX=ON"""
    
    if not run_command(cmake_cmd, "配置OSG CMake", osg_build_dir):
        print("❌ OSG CMake配置失败")
        return False
    
    # 编译OSG
    build_cmd = "C:\\dev\\emsdk\\emsdk_env.bat && emmake make -j4"
    if not run_command(build_cmd, "编译OSG库", osg_build_dir):
        print("❌ OSG编译失败")
        return False
    
    # 安装OSG
    install_cmd = "C:\\dev\\emsdk\\emsdk_env.bat && emmake make install"
    if not run_command(install_cmd, "安装OSG库", osg_build_dir):
        print("❌ OSG安装失败")
        return False
    
    print("\n🎉 OSG库WebAssembly编译完成！")
    print(f"安装位置: {install_dir}")
    print("\n下一步:")
    print("  1. 运行 build_osgearth_wasm.py 编译osgEarth库")
    print("  2. 然后编译基于osgEarth的数字地球应用")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
