/**
 * 多线程测试版本 - 验证pthread支持
 * 这个版本专门用于测试WebAssembly中的多线程功能
 */

#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <atomic>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <emscripten/threading.h>
#endif

// 全局原子计数器
std::atomic<int> g_counter{0};
std::atomic<bool> g_running{true};

// 工作线程函数
void worker_thread(int thread_id) {
    std::cout << "[Thread " << thread_id << "] 线程启动" << std::endl;
    
    while (g_running.load()) {
        // 执行一些工作
        int old_value = g_counter.fetch_add(1);
        
        if (old_value % 100 == 0) {
            std::cout << "[Thread " << thread_id << "] 计数器: " << old_value << std::endl;
        }
        
        // 短暂休眠
        std::this_thread::sleep_for(std::chrono::milliseconds(10));
    }
    
    std::cout << "[Thread " << thread_id << "] 线程结束" << std::endl;
}

// 测试基本的std::thread功能
bool test_basic_threading() {
    std::cout << "[test] 测试基本线程功能..." << std::endl;
    
    try {
        // 创建一个简单的线程
        bool thread_executed = false;
        
        std::thread test_thread([&thread_executed]() {
            std::cout << "[test] 测试线程执行中..." << std::endl;
            thread_executed = true;
        });
        
        // 等待线程完成
        test_thread.join();
        
        if (thread_executed) {
            std::cout << "[test] ✅ 基本线程功能正常" << std::endl;
            return true;
        } else {
            std::cout << "[test] ❌ 基本线程功能失败" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ 线程创建异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试多线程功能
bool test_multi_threading() {
    std::cout << "[test] 测试多线程功能..." << std::endl;
    
    try {
        const int num_threads = 4;
        std::vector<std::thread> threads;
        
        // 重置计数器
        g_counter.store(0);
        g_running.store(true);
        
        // 创建多个工作线程
        for (int i = 0; i < num_threads; ++i) {
            threads.emplace_back(worker_thread, i);
        }
        
        std::cout << "[test] 创建了 " << num_threads << " 个工作线程" << std::endl;
        
        // 让线程运行一段时间
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 停止线程
        g_running.store(false);
        
        // 等待所有线程完成
        for (auto& thread : threads) {
            thread.join();
        }
        
        int final_count = g_counter.load();
        std::cout << "[test] ✅ 多线程测试完成，最终计数: " << final_count << std::endl;
        
        return final_count > 0;
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ 多线程测试异常: " << e.what() << std::endl;
        return false;
    }
}

#ifdef EMSCRIPTEN
// WebAssembly主循环
void main_loop() {
    static int frame_count = 0;
    frame_count++;
    
    if (frame_count % 60 == 0) {  // 每秒输出一次
        std::cout << "[main] 帧数: " << frame_count << ", 计数器: " << g_counter.load() << std::endl;
    }
    
    // 运行5秒后停止
    if (frame_count > 300) {
        g_running.store(false);
        emscripten_cancel_main_loop();
        std::cout << "[main] 测试完成" << std::endl;
    }
}
#endif

int main() {
    std::cout << "🧵 多线程功能测试程序" << std::endl;
    std::cout << "========================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境检测" << std::endl;
    
    // 检查多线程支持
    if (emscripten_has_threading_support()) {
        std::cout << "[main] ✅ Emscripten多线程支持已启用" << std::endl;
    } else {
        std::cout << "[main] ❌ Emscripten多线程支持未启用" << std::endl;
        return 1;
    }
    
    // 检查线程池
    int pool_size = emscripten_num_logical_cores();
    std::cout << "[main] 逻辑核心数: " << pool_size << std::endl;
#endif
    
    // 测试基本线程功能
    if (!test_basic_threading()) {
        std::cout << "[main] ❌ 基本线程测试失败" << std::endl;
        return 1;
    }
    
    // 测试多线程功能
    if (!test_multi_threading()) {
        std::cout << "[main] ❌ 多线程测试失败" << std::endl;
        return 1;
    }
    
#ifdef EMSCRIPTEN
    // WebAssembly环境使用主循环
    std::cout << "[main] 启动WebAssembly主循环..." << std::endl;
    emscripten_set_main_loop(main_loop, 60, 1);
#else
    // 桌面环境直接退出
    std::cout << "[main] ✅ 所有测试通过" << std::endl;
#endif
    
    return 0;
}
