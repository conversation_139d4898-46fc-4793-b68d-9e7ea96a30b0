<map name="Google Maps with Graticule" type="geocentric" version="2">

    <!-- 谷歌卫星影像图层 - 简化配置 -->
    <XYZImage name="google_satellite" enabled="true">
        <url>https://mt.google.com/vt/lyrs=s&amp;x={x}&amp;y={y}&amp;z={z}</url>
        <profile>spherical-mercator</profile>
        <min_level>0</min_level>
        <max_level>18</max_level>
        <options>
            <proxy>127.0.0.1:10809</proxy>
        </options>
    </XYZImage>

    <!-- AWS Terrarium elevation 
    <XYZElevation name="aws_terrarium">
        <url>https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png</url>
        <profile>spherical-mercator</profile>
        <min_level>0</min_level>
        <max_level>14</max_level> 
        <format>png</format>
        <elevation_encoding>terrarium</elevation_encoding>
        <stitch_edges>false</stitch_edges> 
        <interpolation>bilinear</interpolation>
        <vdatum>egm96</vdatum>
        <options>
            <max_concurrent_requests>8</max_concurrent_requests> 
            <timeout>15</timeout>
            <connect_timeout>5</connect_timeout>
            <proxy>127.0.0.1:10809</proxy>  
            <user-agent>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</user-agent>
        </options>
    </XYZElevation> -->

    <!-- 国界线矢量图层 - 使用FeatureModelLayer确保正确渲染 -->
    <feature_model name="world_boundaries" enabled="true">
        <geosfeatures>
            <url>F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/redist_desk/china_boundaries.geojson</url>
            <format>geojson</format>
            <build_spatial_index>false</build_spatial_index>
        </geosfeatures>

        <!-- 国界线样式 - 只渲染线条，作为覆盖层 -->
        <styles>
            <style type="text/css">
                default {
                    stroke: #ff0000;
                    stroke-width: 2px;
                    stroke-opacity: 0.95;
                    fill: none;
                    altitude-clamping: terrain;
                    altitude-technique: drape;
                    altitude-offset: 1.0;
                    render-order: 100;
                    render-lighting: false;
                    render-depth-test: true;
                    render-transparent: false;
                }
            </style>
        </styles>

        <!-- 覆盖层渲染选项 -->
        <lighting>false</lighting>
        <backface_culling>false</backface_culling>
        <alpha_blending>false</alpha_blending>
    </feature_model>


    <!-- 经纬网格线 - 使用明显的颜色和配置 -->
    <GeodeticGraticule name="Geodetic" open="true">
        <color>#fff0006f</color>  <!-- 黄色，半透明 -->
        <line_width>1.0</line_width>  <!-- 粗线条 -->
        <grid_lines>10</grid_lines>  <!-- 网格数量 -->
        <resolutions>10 5 2.5 1.0 0.5</resolutions>  <!-- 多级分辨率 -->
        <grid_lines_visible>true</grid_lines_visible>
        <grid_labels_visible>true</grid_labels_visible>
        <edge_labels_visible>true</edge_labels_visible>
        <text>
            <color>#ffff00ff</color>  <!-- 黄色文字 -->
            <size>16</size>
        </text>
    </GeodeticGraticule>


   <!-- 增强的天空效果配置 -->
    <sky driver="simple">
        <!-- 大气光照效果 -->
        <atmospheric_lighting>true</atmospheric_lighting>
        <exposure>4.0</exposure>
        <daytime_ambient_boost>0.2</daytime_ambient_boost>
        <max_ambient_intensity>0.6</max_ambient_intensity>
        
        <!-- 天体可见性 -->
        <sun_visible>true</sun_visible>
        <moon_visible>true</moon_visible>
        <stars_visible>true</stars_visible>
        <atmosphere_visible>true</atmosphere_visible>
        
        <!-- 质量设置 -->
        <quality>high</quality>
        
        <!-- 时间和光照设置 -->
        <hours>12.0</hours>
        <ambient>0.05</ambient>
        
        <!-- PBR渲染 -->
        <pbr>true</pbr>
    </sky>
    

</map> 