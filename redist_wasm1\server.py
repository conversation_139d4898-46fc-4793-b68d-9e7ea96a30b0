#!/usr/bin/env python3
"""
WebAssembly HTTP服务器 - 支持SharedArrayBuffer和多线程
"""

import http.server
import socketserver
import os
import sys

class WebAssemblyHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def guess_type(self, path):
        """设置正确的MIME类型"""
        if path.endswith('.wasm'):
            return 'application/wasm', None
        elif path.endswith('.js'):
            return 'application/javascript', None
        elif path.endswith('.html'):
            return 'text/html', None
        else:
            return super().guess_type(path)
    
    def end_headers(self):
        # SharedArrayBuffer必需的HTTP头
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 禁用缓存，确保最新文件
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        print(f"[HTTP] {format % args}")

def main():
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效端口号: {sys.argv[1]}")
            sys.exit(1)
    
    print(f"🌐 WebAssembly HTTP服务器")
    print(f"端口: {port}")
    print(f"目录: {os.getcwd()}")
    print(f"支持: SharedArrayBuffer, 多线程, SDL2")
    print(f"测试地址: http://localhost:{port}/")
    print("按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", port), WebAssemblyHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
