/**
 * 简化的weejobs多线程系统测试
 * 不依赖完整的osgEarth库，只测试核心多线程功能
 */

#include <iostream>
#include <thread>
#include <vector>
#include <atomic>
#include <chrono>
#include <future>
#include <functional>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

// 简化的任务系统，模拟weejobs的核心功能
class SimpleJobPool
{
private:
    std::vector<std::thread> workers;
    std::atomic<bool> stop{false};
    std::atomic<int> task_counter{0};

public:
    SimpleJobPool(int num_threads = 2)
    {
        std::cout << "[pool] 创建线程池，线程数: " << num_threads << std::endl;

        for (int i = 0; i < num_threads; ++i)
        {
            workers.emplace_back([this, i]()
                                 {
                std::cout << "[worker" << i << "] 工作线程启动" << std::endl;
                
                while (!stop.load()) {
                    // 模拟工作
                    std::this_thread::sleep_for(std::chrono::milliseconds(100));
                }
                
                std::cout << "[worker" << i << "] 工作线程结束" << std::endl; });
        }
    }

    ~SimpleJobPool()
    {
        std::cout << "[pool] 停止线程池..." << std::endl;
        stop.store(true);

        for (auto &worker : workers)
        {
            if (worker.joinable())
            {
                worker.join();
            }
        }
        std::cout << "[pool] 线程池已停止" << std::endl;
    }

    // 提交一个简单任务
    std::future<int> submit_task()
    {
        return std::async(std::launch::async, [this]()
                          {
            int task_id = task_counter.fetch_add(1);
            std::cout << "[task] 执行任务 " << task_id << std::endl;
            
            // 模拟任务工作
            std::this_thread::sleep_for(std::chrono::milliseconds(200));
            
            std::cout << "[task] 任务 " << task_id << " 完成" << std::endl;
            return task_id; });
    }

    int get_worker_count() const
    {
        return workers.size();
    }
};

// 测试基础线程创建
bool test_basic_threading()
{
    std::cout << "\n=== 测试1: 基础线程创建 ===" << std::endl;

    try
    {
        std::atomic<bool> thread_executed{false};

        std::thread test_thread([&thread_executed]()
                                {
            std::cout << "[test1] 测试线程执行中..." << std::endl;
            thread_executed.store(true); });

        test_thread.join();

        if (thread_executed.load())
        {
            std::cout << "[test1] ✅ 基础线程创建成功" << std::endl;
            return true;
        }
        else
        {
            std::cout << "[test1] ❌ 基础线程创建失败" << std::endl;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "[test1] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试std::async
bool test_async_tasks()
{
    std::cout << "\n=== 测试2: std::async任务 ===" << std::endl;

    try
    {
        auto future1 = std::async(std::launch::async, []()
                                  {
            std::cout << "[test2] async任务1执行中..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(100));
            return 42; });

        auto future2 = std::async(std::launch::async, []()
                                  {
            std::cout << "[test2] async任务2执行中..." << std::endl;
            std::this_thread::sleep_for(std::chrono::milliseconds(150));
            return 84; });

        int result1 = future1.get();
        int result2 = future2.get();

        std::cout << "[test2] 任务结果: " << result1 << ", " << result2 << std::endl;

        if (result1 == 42 && result2 == 84)
        {
            std::cout << "[test2] ✅ std::async任务成功" << std::endl;
            return true;
        }
        else
        {
            std::cout << "[test2] ❌ std::async任务失败" << std::endl;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "[test2] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试简化的线程池
bool test_simple_job_pool()
{
    std::cout << "\n=== 测试3: 简化线程池 ===" << std::endl;

    try
    {
        SimpleJobPool pool(2);
        std::cout << "[test3] 线程池工作线程数: " << pool.get_worker_count() << std::endl;

        // 提交几个任务
        std::vector<std::future<int>> futures;

        for (int i = 0; i < 3; ++i)
        {
            futures.push_back(pool.submit_task());
        }

        // 等待所有任务完成
        std::vector<int> results;
        for (auto &future : futures)
        {
            results.push_back(future.get());
        }

        std::cout << "[test3] 任务结果: ";
        for (int result : results)
        {
            std::cout << result << " ";
        }
        std::cout << std::endl;

        if (results.size() == 3)
        {
            std::cout << "[test3] ✅ 简化线程池测试成功" << std::endl;
            return true;
        }
        else
        {
            std::cout << "[test3] ❌ 简化线程池测试失败" << std::endl;
            return false;
        }
    }
    catch (const std::exception &e)
    {
        std::cout << "[test3] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

int main()
{
    std::cout << "🧵 简化weejobs多线程系统测试" << std::endl;
    std::cout << "=============================" << std::endl;

#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;

    // 检查多线程支持
    if (emscripten_has_threading_support())
    {
        std::cout << "[main] ✅ Emscripten多线程支持已启用" << std::endl;

        int cores = emscripten_num_logical_cores();
        std::cout << "[main] 逻辑核心数: " << cores << std::endl;
    }
    else
    {
        std::cout << "[main] ❌ Emscripten多线程支持未启用" << std::endl;
        std::cout << "[main] 将尝试运行测试，但可能会失败" << std::endl;
    }
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif

    bool all_passed = true;

    // 运行所有测试
    if (!test_basic_threading())
    {
        all_passed = false;
    }

    if (!test_async_tasks())
    {
        all_passed = false;
    }

    if (!test_simple_job_pool())
    {
        all_passed = false;
    }

    std::cout << "\n=== 测试总结 ===" << std::endl;
    if (all_passed)
    {
        std::cout << "🎉 所有多线程测试通过！" << std::endl;
        std::cout << "WebAssembly多线程环境工作正常" << std::endl;
    }
    else
    {
        std::cout << "❌ 部分多线程测试失败" << std::endl;
        std::cout << "WebAssembly多线程环境存在问题" << std::endl;
    }

#ifdef EMSCRIPTEN
    // 强制退出，避免异步操作警告
    emscripten_force_exit(all_passed ? 0 : 1);
#endif

    return all_passed ? 0 : 1;
}
