/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHSYMBOLOGY_BILLBOARD_SYMBOL_H
#define OSGEARTHSYMBOLOGY_BILLBOARD_SYMBOL_H 1

#include <climits>

#include <osgEarth/Common>
#include <osgEarth/InstanceSymbol>
#include <osgEarth/Expression>

namespace osgEarth
{
    namespace Util {
        class InstanceResource;
    }

    /**
     * Represents a 2D camera-facing image
     */
    class OSGEARTH_EXPORT BillboardSymbol : public InstanceSymbol
    {
    public:
        META_Object(osgEarth, BillboardSymbol);

        BillboardSymbol(const BillboardSymbol& rhs,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);
        BillboardSymbol( const Config& conf =Config() );

        /** dtor */
        virtual ~BillboardSymbol() { }

    public: // properties

        //! URL or the top image
        OE_OPTION(Expression<URI>, topURL);

        //! Width of the billboard image in meters
        OE_OPTION(float, width, 15.0f);

        //! Height of the billboard image in meters
        OE_OPTION(float, height, 10.0f);

        //! Percentage by which to vary width and height [0..1] default = 0
        OE_OPTION(float, sizeVariation, 0.0f);

        //! Weighting of this symbol versus others in the same group.
        //! Should be an integer >= 1. Default = 1.
        OE_OPTION(unsigned, selectionWeight, 1);
        
    public: // non-serialized properties (for programmatic use only)

        void setSideImage(osg::Image* value) { _sideImage = value; }
        osg::Image* getSideImage() const { return _sideImage.get(); }

        void setTopImage(osg::Image* value) { _topImage = value; }
        osg::Image* getTopImage() const { return _topImage.get(); }

    public:
        Config getConfig() const override;
        void mergeConfig(const Config& conf) override;
        static void parseSLD(const Config& c, class Style& style);
    
    public: // InstanceSymbol

        //! Creates a new (empty) resource appropriate for this symbol
        InstanceResource* createResource() const override;

    protected:
        osg::ref_ptr<osg::Image> _sideImage;
        osg::ref_ptr<osg::Image> _topImage;
    };
} // namespace osgEarth

#endif
