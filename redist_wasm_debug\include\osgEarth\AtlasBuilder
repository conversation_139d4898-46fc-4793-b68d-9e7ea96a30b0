/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTHUTIL_ATLAS_BUILDER_H
#define OSGEARTHUTIL_ATLAS_BUILDER_H

#include <osgEarth/Common>
#include <osgEarth/ResourceLibrary>
#include <osg/Vec4f>
#include <vector>

namespace osgDB {
    class Options;
}

namespace osgEarth { namespace Contrib
{
    using namespace osgEarth;

    /**
     * Compiles a resource library into a texture atlas.
     */
    class OSGEARTH_EXPORT AtlasBuilder
    {
    public:
        struct Atlas
        {
            // resulting atlas images. The first one is the master atlas image;
            // any following ones correspond to images created based on auxiliary
            // patterns added with addAuxFilePattern.
            std::vector<osg::ref_ptr<osg::Image> > _images;

            // resulting resource library for the atlas
            osg::ref_ptr<ResourceLibrary> _lib;
        };

    public:
        /** Construct a new atlas builder */
        AtlasBuilder(const osgDB::Options* options =0L);

        /** Sets the maximum size of each layer in the output atlas */
        void setSize(unsigned width, unsigned height);

        /** Adds an auxiliary file pattern that should be built, along with the
            default RGBA to use when the aux file doesn't exist. */
        void addAuxFilePattern(const std::string& pattern, const osg::Vec4f& defRGBA);

        /** List of aux file patterns */
        const std::vector<std::string>& auxFilePatterns() const { return _auxPatterns; }
        const std::vector<osg::Vec4f>& auxDefaultValues() const { return _auxDefaults; }

        /**
         * Whether to generate an RGB texture.  By default RGBA textures are created.
         */
        bool getRGB() const { return _rgb; }
        void setRGB( bool rgb ) { _rgb = rgb; }

        /** Builds an atlas. */
        bool build(
            const ResourceLibrary* input,
            const std::string& newAtlasURI,
            Atlas& output) const;


    protected:
        unsigned _width;
        unsigned _height;
        osg::ref_ptr<const osgDB::Options> _options;
        std::vector<std::string> _auxPatterns;
        std::vector<osg::Vec4f>  _auxDefaults;
        bool _debug;
        bool _rgb;
    };

} }

#endif // OSGEARTHUTIL_ATLAS_BUILDER_H
