/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTH_ANNOTATION_BBOXDRAWABLE_H
#define OSGEARTH_ANNOTATION_BBOXDRAWABLE_H 1

#include <osgEarth/BBoxSymbol>
#include <osgEarth/Common>

#include <osg/Geometry>
#include <osg/BoundingBox>

namespace osgEarth { namespace Util
{
    /**
     * Drawable for rendering bounding box around label
     */
    class OSGEARTH_EXPORT BboxDrawable : public osg::Geometry
    {
    public:
        META_Node(osgEarth, BboxDrawable);
        BboxDrawable( const osg::BoundingBox& box, const BBoxSymbol& boxSymbol );
    protected:        
        BboxDrawable() { }
        BboxDrawable(const BboxDrawable& rhs, const osg::CopyOp& op) { }
    };

} }

#endif // OSGEARTH_ANNOTATION_BBOXDRAWABLE_H
