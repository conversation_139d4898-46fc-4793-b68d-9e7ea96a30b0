/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_ELEVATIONLOD_H
#define OSGEARTH_ELEVATIONLOD_H 1

#include <osgEarth/Common>
#include <osgEarth/SpatialReference>
#include <osg/Group>

namespace osgEarth
{
    /**
     * Decorator node that will only display it's children when the
     * camera is within a given elevation range and/or within a
     * given camera range.
     */
    class OSGEARTH_EXPORT ElevationLOD : public osg::Group
    {
    public:
        META_Node(osgEarth, ElevationLOD);

        ElevationLOD();
        ElevationLOD(const ElevationLOD& rhs, const osg::CopyOp& op);
        ElevationLOD(const SpatialReference* srs);
        ElevationLOD(const SpatialReference* srs, double minElevation, double maxElevation);

        double getMinElevation() const;
        void setMinElevation( double minElevation );

        double getMaxElevation() const;
        void setMaxElevation(double maxElevation );

        void setElevations( double minElevation, double maxElevation );

        float getMinRange() const;
        void setMinRange(float minRange);

        float getMaxRange() const;
        void setMaxRange(float maxRange);

        void setRanges( float minRange, float maxRange );


    public: // osg::Node

        virtual void traverse( osg::NodeVisitor& nv);

    protected:

        virtual ~ElevationLOD();

    private:
        osg::ref_ptr< const SpatialReference > _srs;
        optional<double> _minElevation;
        optional<double> _maxElevation;
        optional<float>  _minRange;
        optional<float>  _maxRange;
    };

    // AltitudeLOD is a better name.
    typedef ElevationLOD AltitudeLOD;

} // namespace osgEarth

#endif // OSGEARTH_ELEVATIONLOD_H
