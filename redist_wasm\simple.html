<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>osgEarth WebAssembly - Simple Mode</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #000;
      font-family: Arial, sans-serif;
      overflow: hidden;
    }

    #canvas {
      display: block;
      width: 100vw;
      height: 100vh;
      background-color: #222;
    }

    #info {
      position: absolute;
      top: 10px;
      left: 10px;
      color: white;
      background: rgba(0, 0, 0, 0.7);
      padding: 10px;
      border-radius: 5px;
      font-size: 12px;
      z-index: 1000;
    }
  </style>
</head>

<body>
  <div id="info">
    <h3>osgEarth WebAssembly - Simple Mode</h3>
    <p>No configuration file - using minimal defaults</p>
    <p><strong>Mouse:</strong> Drag to rotate, scroll to zoom</p>
  </div>

  <canvas id="canvas"></canvas>

  <script>
    console.log('Starting osgEarth WebAssembly in simple mode...');

    // 配置WebAssembly模块 - 超级简化版本
    var Module = {
      canvas: document.getElementById('canvas'),

      preRun: [
        function () {
          console.log('Simple mode - no config file needed');

          // 最基本的WebGL设置
          Module.canvas.webglContextAttributes = {
            alpha: false,
            depth: true
          };
        }
      ],

      onRuntimeInitialized: function () {
        console.log('Simple mode initialized');
      },

      print: function (text) {
        console.log('stdout:', text);
      },

      printErr: function (text) {
        console.log('stderr:', text);
        // 简单模式下不显示任何错误
      },

      onAbort: function (what) {
        console.log('Aborted:', what);
        // 简单模式下忽略abort
      },

      // 最小内存配置
      INITIAL_MEMORY: 32 * 1024 * 1024, // 32MB
      ALLOW_MEMORY_GROWTH: true,

      // 最少环境变量
      ENV: {
        'OSG_NOTIFY_LEVEL': 'NEVER',
        'OSGEARTH_NOTIFY_LEVEL': 'NEVER'
      },

      // 无参数 - 使用内置默认值
      arguments: [],

      noInitialRun: false,
      noExitRuntime: true
    };

    // 加载WebAssembly模块
    var script = document.createElement('script');
    script.src = 'osgearth_myviewer.js';
    script.onload = function () {
      console.log('Simple mode script loaded');
    };
    script.onerror = function () {
      console.error('Failed to load script');
    };
    document.body.appendChild(script);
  </script>
</body>

</html>