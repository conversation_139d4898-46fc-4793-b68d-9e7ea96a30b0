/* osgEarth
 * Copyright 2008-2014 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_JOIN_FEATUREFILTER_OPTIONS
#define OSGEARTH_DRIVER_JOIN_FEATUREFILTER_OPTIONS 1

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/LayerReference>

namespace osgEarth { namespace Drivers
{
    using namespace osgEarth;

    class JoinFeatureFilterOptions : public ConfigOptions // NO EXPORT; header only
    {
    public:
        JoinFeatureFilterOptions( const ConfigOptions& opt =ConfigOptions() ) : ConfigOptions( opt )
        {
            _conf.set("driver", "join");            
            fromConfig( _conf );
        }

        /** Features to load and use as boundary geometries */
        OE_OPTION_LAYER(FeatureSource, featureSource);
        /*
        * Whether to do a "rough" join, meaning all features within the extent of a feature context
        * will take the first feature that intersects the context rather than checking the
        * intersection for each feature.  This is useful for the common use case of joining
        * a country boundary file to classify buildings.  There are lots of buildings, but usually only
        * one country so we don't need to check an intersection against each building so we can get a performance boost.
        */
        OE_OPTION(bool, rough, false);

    public:
        Config getConfig() const {
            Config conf = ConfigOptions::getConfig();
            conf.key() = "join";
            featureSource().set(conf, "features");
            conf.set("rough", rough());
            return conf;
        }

    protected:
        void mergeConfig( const Config& conf ) {
            ConfigOptions::mergeConfig( conf );
            fromConfig( conf );
        }

    private:
        void fromConfig( const Config& conf ) {
            featureSource().get(conf, "features");
            conf.get("rough", rough());
        }
    };

} } // namespace osgEarth::Drivers

#endif // OSGEARTH_DRIVER_JOIN_FEATUREFILTER_OPTIONS
