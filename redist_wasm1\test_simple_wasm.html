<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebAssembly测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #f0f0f0;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background-color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            border-left: 4px solid #007acc;
            background-color: #f8f9fa;
        }
        .error {
            border-left-color: #dc3545;
            background-color: #f8d7da;
            color: #721c24;
        }
        .success {
            border-left-color: #28a745;
            background-color: #d4edda;
            color: #155724;
        }
        .log {
            background-color: #f8f9fa;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px;
        }
        .log-info { color: #007acc; }
        .log-error { color: #dc3545; }
        .log-success { color: #28a745; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌐 简单WebAssembly测试</h1>
        
        <div id="status" class="status">状态: 准备测试...</div>
        
        <div class="status">
            <h3>📋 环境信息</h3>
            <div id="env-info">检查中...</div>
        </div>
        
        <div class="log">
            <h3>📝 测试日志</h3>
            <div id="log-output"></div>
        </div>
        
        <div class="status">
            <h3>🔗 其他测试</h3>
            <p>如果基础测试成功，可以尝试：</p>
            <ul>
                <li><a href="test_simple_pthread.html" target="_blank">pthread测试</a></li>
                <li><a href="test_minimal.html" target="_blank">最小化osgEarth</a></li>
                <li><a href="test_multithreaded.html" target="_blank">完整osgEarth</a></li>
            </ul>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // 检查环境
        function checkEnvironment() {
            const envInfo = document.getElementById('env-info');
            let html = '';
            
            html += `<strong>浏览器:</strong> ${navigator.userAgent}<br>`;
            html += `<strong>平台:</strong> ${navigator.platform}<br>`;
            html += `<strong>语言:</strong> ${navigator.language}<br>`;
            html += `<strong>在线状态:</strong> ${navigator.onLine ? '在线' : '离线'}<br>`;
            html += `<strong>WebAssembly支持:</strong> ${typeof WebAssembly !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            html += `<strong>Worker支持:</strong> ${typeof Worker !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            html += `<strong>SharedArrayBuffer:</strong> ${typeof SharedArrayBuffer !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            html += `<strong>Atomics:</strong> ${typeof Atomics !== 'undefined' ? '✅ 支持' : '❌ 不支持'}<br>`;
            
            envInfo.innerHTML = html;
            addLog('环境检查完成', 'success');
        }
        
        // Module配置
        var Module = {
            print: function(text) {
                console.log('WASM:', text);
                addLog('输出: ' + text, 'success');
            },
            
            printErr: function(text) {
                console.error('WASM Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('WebAssembly模块初始化完成');
                addLog('WebAssembly模块初始化完成', 'success');
                updateStatus('WebAssembly模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('WebAssembly模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                updateStatus('程序退出，状态码: ' + status, status !== 0, status === 0);
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成', 'info');
            
            // 检查环境
            checkEnvironment();
            
            updateStatus('加载WebAssembly模块...');
            addLog('开始加载WebAssembly模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'hello_wasm.js';
            script.onload = function() {
                addLog('WebAssembly脚本加载成功', 'success');
                updateStatus('WebAssembly脚本已加载');
            };
            script.onerror = function() {
                addLog('WebAssembly脚本加载失败', 'error');
                updateStatus('WebAssembly脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
