/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHSYMBOLOGY_BILLBOARD_RESOURCE_H
#define OSGEARTHSYMBOLOGY_BILLBOARD_RESOURCE_H 1

#include <osgEarth/Common>
#include <osgEarth/InstanceResource>

namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    /**
     * A resource that represents a real-world-sized image that can be used as
     * a billboard (camera-facing 2D object)
     */
    class OSGEARTH_EXPORT BillboardResource : public InstanceResource
    {
    public:
        /** Constructs a new resource. */
        BillboardResource( const Config& conf =Config() );

        /** dtor */
        virtual ~BillboardResource() { }      

        bool is2D() const { return false; }

    public: // properties

        /** Width of the billboard image in meters */
        optional<float>& width() { return _width; }
        const optional<float>& width() const { return _width; }

        /** Height of the billboard image in meters */
        optional<float>& height() { return _height; }
        const optional<float>& height() const { return _height; }

    public: // serialization methods

        virtual Config getConfig() const;
        void mergeConfig( const Config& conf );

    protected: // InstanceResource

        virtual osg::Node* createNodeFromURI(const URI& uri, const osgDB::Options* dbOptions) const;

        optional<float> _width;
        optional<float> _height;
    };

} }

#endif // OSGEARTHSYMBOLOGY_BILLBOARD_RESOURCE_H
