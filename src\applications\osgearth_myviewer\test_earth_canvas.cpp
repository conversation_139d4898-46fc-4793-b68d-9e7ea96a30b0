/**
 * Canvas 2D渲染的简化数字地球测试程序
 * 使用SDL2的2D渲染器，避免OpenGL兼容性问题
 */

#include <iostream>
#include <cmath>
#include <vector>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#else
#include <SDL.h>
#endif

// 全局变量
static SDL_Window* g_window = nullptr;
static SDL_Renderer* g_renderer = nullptr;
static bool g_running = true;
static float g_rotation = 0.0f;
static double g_zoom = 1.0;
static double g_center_lon = 116.3; // 北京经度
static double g_center_lat = 39.9;  // 北京纬度

// 地球点结构
struct EarthPoint {
    double longitude;
    double latitude;
    int screen_x;
    int screen_y;
};

// 简化的地球渲染器
class SimpleEarthRenderer {
private:
    std::vector<EarthPoint> grid_points;
    int earth_radius;
    int center_x, center_y;
    
public:
    SimpleEarthRenderer() {
        earth_radius = 200;
        center_x = 400;
        center_y = 300;
        
        std::cout << "[earth] 初始化简化地球渲染器" << std::endl;
        
        // 创建经纬网格点
        for (int lat = -90; lat <= 90; lat += 15) {
            for (int lng = -180; lng <= 180; lng += 15) {
                EarthPoint point;
                point.longitude = lng;
                point.latitude = lat;
                grid_points.push_back(point);
            }
        }
        
        std::cout << "[earth] 创建了 " << grid_points.size() << " 个网格点" << std::endl;
    }
    
    void render() {
        // 清除屏幕（深蓝色，模拟太空）
        SDL_SetRenderDrawColor(g_renderer, 10, 20, 40, 255);
        SDL_RenderClear(g_renderer);
        
        // 渲染地球背景
        renderEarthBackground();
        
        // 渲染经纬网格
        renderGrid();
        
        // 渲染大陆轮廓
        renderContinents();
        
        // 渲染信息
        renderInfo();
    }
    
private:
    void renderEarthBackground() {
        // 绘制地球圆形背景
        SDL_SetRenderDrawColor(g_renderer, 50, 100, 200, 255); // 蓝色海洋
        
        // 用多个圆环模拟圆形
        for (int r = 0; r < earth_radius; r += 2) {
            for (int angle = 0; angle < 360; angle += 5) {
                double rad = (angle + g_rotation) * M_PI / 180.0;
                int x = center_x + (int)(r * cos(rad));
                int y = center_y + (int)(r * sin(rad));
                
                SDL_Rect pixel = {x, y, 2, 2};
                SDL_RenderFillRect(g_renderer, &pixel);
            }
        }
    }
    
    void renderGrid() {
        SDL_SetRenderDrawColor(g_renderer, 100, 100, 100, 255); // 灰色网格线
        
        // 绘制经线
        for (int lng = -180; lng <= 180; lng += 30) {
            for (int lat = -90; lat < 90; lat += 5) {
                auto p1 = projectToScreen(lng, lat);
                auto p2 = projectToScreen(lng, lat + 5);
                
                if (isVisible(p1) && isVisible(p2)) {
                    SDL_RenderDrawLine(g_renderer, p1.screen_x, p1.screen_y, p2.screen_x, p2.screen_y);
                }
            }
        }
        
        // 绘制纬线
        for (int lat = -90; lat <= 90; lat += 30) {
            for (int lng = -180; lng < 180; lng += 5) {
                auto p1 = projectToScreen(lng, lat);
                auto p2 = projectToScreen(lng + 5, lat);
                
                if (isVisible(p1) && isVisible(p2)) {
                    SDL_RenderDrawLine(g_renderer, p1.screen_x, p1.screen_y, p2.screen_x, p2.screen_y);
                }
            }
        }
    }
    
    void renderContinents() {
        SDL_SetRenderDrawColor(g_renderer, 100, 200, 100, 255); // 绿色陆地
        
        // 简化的亚洲轮廓
        std::vector<std::pair<double, double>> asia = {
            {60, 70}, {120, 70}, {140, 50}, {120, 20}, {100, 10}, {80, 20}, {60, 40}
        };
        
        renderContinent(asia);
        
        // 简化的欧洲轮廓
        std::vector<std::pair<double, double>> europe = {
            {-10, 70}, {40, 70}, {30, 40}, {10, 35}, {-10, 40}
        };
        
        renderContinent(europe);
        
        // 简化的北美洲轮廓
        std::vector<std::pair<double, double>> north_america = {
            {-170, 70}, {-60, 70}, {-80, 25}, {-120, 30}, {-150, 60}
        };
        
        renderContinent(north_america);
    }
    
    void renderContinent(const std::vector<std::pair<double, double>>& points) {
        for (size_t i = 0; i < points.size(); ++i) {
            auto p1 = projectToScreen(points[i].first, points[i].second);
            auto p2 = projectToScreen(points[(i + 1) % points.size()].first, points[(i + 1) % points.size()].second);
            
            if (isVisible(p1) && isVisible(p2)) {
                SDL_RenderDrawLine(g_renderer, p1.screen_x, p1.screen_y, p2.screen_x, p2.screen_y);
            }
        }
    }
    
    void renderInfo() {
        // 渲染坐标轴
        SDL_SetRenderDrawColor(g_renderer, 255, 255, 255, 255); // 白色
        
        // X轴
        SDL_RenderDrawLine(g_renderer, center_x, center_y, center_x + 50, center_y);
        // Y轴
        SDL_RenderDrawLine(g_renderer, center_x, center_y, center_x, center_y - 50);
        
        // 渲染中心点
        SDL_Rect center_point = {center_x - 2, center_y - 2, 4, 4};
        SDL_SetRenderDrawColor(g_renderer, 255, 0, 0, 255); // 红色中心点
        SDL_RenderFillRect(g_renderer, &center_point);
    }
    
    EarthPoint projectToScreen(double longitude, double latitude) {
        EarthPoint point;
        point.longitude = longitude;
        point.latitude = latitude;
        
        // 简化的球面投影到屏幕坐标
        double lat_rad = latitude * M_PI / 180.0;
        double lng_rad = (longitude + g_rotation) * M_PI / 180.0;
        
        // 正交投影
        double x = cos(lat_rad) * cos(lng_rad);
        double y = cos(lat_rad) * sin(lng_rad);
        double z = sin(lat_rad);
        
        // 只显示正面（z > 0的部分）
        if (z > 0) {
            point.screen_x = center_x + (int)(x * earth_radius * g_zoom);
            point.screen_y = center_y - (int)(y * earth_radius * g_zoom);
        } else {
            point.screen_x = -1000; // 标记为不可见
            point.screen_y = -1000;
        }
        
        return point;
    }
    
    bool isVisible(const EarthPoint& point) {
        return point.screen_x >= 0 && point.screen_x < 800 && 
               point.screen_y >= 0 && point.screen_y < 600;
    }
};

// 全局地球渲染器
static SimpleEarthRenderer* g_earth_renderer = nullptr;

// 处理事件
void handle_events() {
    SDL_Event event;
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                g_running = false;
                std::cout << "[event] 收到退出事件" << std::endl;
                break;
                
            case SDL_KEYDOWN:
                std::cout << "[event] 按键: " << SDL_GetKeyName(event.key.keysym.sym) << std::endl;
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    g_running = false;
                } else if (event.key.keysym.sym == SDLK_PLUS || event.key.keysym.sym == SDLK_EQUALS) {
                    g_zoom *= 1.2;
                    std::cout << "[earth] 放大，缩放级别: " << g_zoom << std::endl;
                } else if (event.key.keysym.sym == SDLK_MINUS) {
                    g_zoom /= 1.2;
                    std::cout << "[earth] 缩小，缩放级别: " << g_zoom << std::endl;
                }
                break;
                
            case SDL_MOUSEBUTTONDOWN:
                std::cout << "[event] 鼠标点击: (" << event.button.x << ", " << event.button.y << ")" << std::endl;
                break;
                
            case SDL_MOUSEWHEEL:
                if (event.wheel.y > 0) {
                    g_zoom *= 1.1;
                    std::cout << "[earth] 滚轮放大，缩放级别: " << g_zoom << std::endl;
                } else if (event.wheel.y < 0) {
                    g_zoom /= 1.1;
                    std::cout << "[earth] 滚轮缩小，缩放级别: " << g_zoom << std::endl;
                }
                break;
        }
    }
}

// 主循环函数
void main_loop() {
    if (!g_running) {
        return;
    }
    
    handle_events();
    
    if (g_earth_renderer) {
        g_earth_renderer->render();
    }
    
    // 更新旋转
    g_rotation += 0.5f;
    if (g_rotation >= 360.0f) {
        g_rotation = 0.0f;
    }
    
    SDL_RenderPresent(g_renderer);
}

int main(int argc, char* argv[]) {
    std::cout << "🌍 Canvas 2D数字地球测试程序" << std::endl;
    std::cout << "============================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;
    
    // 创建窗口
    g_window = SDL_CreateWindow(
        "Canvas 2D Digital Earth",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_SHOWN
    );
    
    if (!g_window) {
        std::cout << "[main] ❌ 窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 窗口创建成功: 800x600" << std::endl;
    
    // 创建渲染器
    g_renderer = SDL_CreateRenderer(g_window, -1, SDL_RENDERER_ACCELERATED | SDL_RENDERER_PRESENTVSYNC);
    if (!g_renderer) {
        std::cout << "[main] ❌ 渲染器创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(g_window);
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 渲染器创建成功" << std::endl;
    
    // 创建地球渲染器
    g_earth_renderer = new SimpleEarthRenderer();
    
    std::cout << "[main] ✅ Canvas 2D数字地球初始化完成" << std::endl;
    std::cout << "[main] 控制说明:" << std::endl;
    std::cout << "[main] - 滚轮: 缩放" << std::endl;
    std::cout << "[main] - +/-键: 缩放" << std::endl;
    std::cout << "[main] - ESC键: 退出" << std::endl;
    std::cout << "[main] - 地球会自动旋转，显示经纬网格和大陆轮廓" << std::endl;
    
#ifdef EMSCRIPTEN
    // WebAssembly主循环
    emscripten_set_main_loop(main_loop, 60, 1);
#else
    // 桌面主循环
    while (g_running) {
        main_loop();
        SDL_Delay(16);
    }
#endif
    
    // 清理资源
    delete g_earth_renderer;
    if (g_renderer) {
        SDL_DestroyRenderer(g_renderer);
    }
    if (g_window) {
        SDL_DestroyWindow(g_window);
    }
    SDL_Quit();
    
    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
