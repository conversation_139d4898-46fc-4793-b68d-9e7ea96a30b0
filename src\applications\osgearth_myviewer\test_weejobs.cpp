/**
 * weejobs多线程系统专项测试
 * 测试osgEarth的weejobs线程池系统在WebAssembly中的工作情况
 */

#include <iostream>
#include <chrono>
#include <atomic>
#include <osgEarth/Threading>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

using namespace osgEarth;

// 全局计数器，用于测试任务执行
std::atomic<int> g_task_counter{0};
std::atomic<int> g_completed_tasks{0};

// 简单的测试任务
bool simple_task(Cancelable& cancelable) {
    if (cancelable.canceled()) {
        std::cout << "[task] 任务被取消" << std::endl;
        return false;
    }
    
    int task_id = g_task_counter.fetch_add(1);
    std::cout << "[task] 执行任务 " << task_id << std::endl;
    
    // 模拟一些工作
    std::this_thread::sleep_for(std::chrono::milliseconds(100));
    
    g_completed_tasks.fetch_add(1);
    std::cout << "[task] 任务 " << task_id << " 完成" << std::endl;
    return true;
}

// 测试基础线程池功能
bool test_basic_jobpool() {
    std::cout << "\n=== 测试1: 基础线程池功能 ===" << std::endl;
    
    try {
        // 创建一个小的线程池
        auto pool = jobs::get_pool("test_basic", 1);
        if (!pool) {
            std::cout << "[test1] ❌ 线程池创建失败" << std::endl;
            return false;
        }
        
        std::cout << "[test1] ✅ 线程池创建成功: " << pool->name() << std::endl;
        std::cout << "[test1] 并发数: " << pool->concurrency() << std::endl;
        
        // 获取指标
        auto metrics = pool->metrics();
        if (metrics) {
            std::cout << "[test1] ✅ 指标获取成功" << std::endl;
            std::cout << "[test1] 待处理: " << metrics->pending.load() << std::endl;
            std::cout << "[test1] 运行中: " << metrics->running.load() << std::endl;
        }
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "[test1] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试任务分发
bool test_job_dispatch() {
    std::cout << "\n=== 测试2: 任务分发 ===" << std::endl;
    
    try {
        auto pool = jobs::get_pool("test_dispatch", 1);
        
        // 重置计数器
        g_task_counter.store(0);
        g_completed_tasks.store(0);
        
        // 分发一个简单任务
        jobs::context ctx;
        ctx.pool = pool;
        
        std::cout << "[test2] 分发任务..." << std::endl;
        auto future = jobs::dispatch(simple_task, ctx);
        
        std::cout << "[test2] 等待任务完成..." << std::endl;
        bool result = future.get();
        
        if (result && g_completed_tasks.load() > 0) {
            std::cout << "[test2] ✅ 任务执行成功" << std::endl;
            std::cout << "[test2] 完成任务数: " << g_completed_tasks.load() << std::endl;
            return true;
        } else {
            std::cout << "[test2] ❌ 任务执行失败" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cout << "[test2] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试多任务并发
bool test_multiple_jobs() {
    std::cout << "\n=== 测试3: 多任务并发 ===" << std::endl;
    
    try {
        auto pool = jobs::get_pool("test_multi", 2); // 2个线程
        
        // 重置计数器
        g_task_counter.store(0);
        g_completed_tasks.store(0);
        
        const int num_tasks = 5;
        std::vector<jobs::future<bool>> futures;
        
        jobs::context ctx;
        ctx.pool = pool;
        
        std::cout << "[test3] 分发 " << num_tasks << " 个任务..." << std::endl;
        
        for (int i = 0; i < num_tasks; ++i) {
            auto future = jobs::dispatch(simple_task, ctx);
            futures.push_back(std::move(future));
        }
        
        std::cout << "[test3] 等待所有任务完成..." << std::endl;
        
        int successful_tasks = 0;
        for (auto& future : futures) {
            if (future.get()) {
                successful_tasks++;
            }
        }
        
        std::cout << "[test3] 成功任务数: " << successful_tasks << "/" << num_tasks << std::endl;
        std::cout << "[test3] 完成计数: " << g_completed_tasks.load() << std::endl;
        
        if (successful_tasks == num_tasks && g_completed_tasks.load() == num_tasks) {
            std::cout << "[test3] ✅ 多任务并发测试成功" << std::endl;
            return true;
        } else {
            std::cout << "[test3] ❌ 多任务并发测试失败" << std::endl;
            return false;
        }
    } catch (const std::exception& e) {
        std::cout << "[test3] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

// 测试PagingManager使用的线程池
bool test_paging_manager_pool() {
    std::cout << "\n=== 测试4: PagingManager线程池 ===" << std::endl;
    
    try {
        // 使用PagingManager的默认线程池名称
        const std::string PAGING_POOL_NAME = "oe.pagingmanager";
        
        auto pool = jobs::get_pool(PAGING_POOL_NAME, 1);
        if (!pool) {
            std::cout << "[test4] ❌ PagingManager线程池创建失败" << std::endl;
            return false;
        }
        
        std::cout << "[test4] ✅ PagingManager线程池创建成功" << std::endl;
        std::cout << "[test4] 线程池名称: " << pool->name() << std::endl;
        
        // 模拟PagingManager的操作
        pool->set_concurrency(1);
        std::cout << "[test4] ✅ 并发数设置成功: " << pool->concurrency() << std::endl;
        
        return true;
    } catch (const std::exception& e) {
        std::cout << "[test4] ❌ 异常: " << e.what() << std::endl;
        return false;
    }
}

int main() {
    std::cout << "🧵 weejobs多线程系统测试" << std::endl;
    std::cout << "=========================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
    
    // 检查多线程支持
    if (!emscripten_has_threading_support()) {
        std::cout << "[main] ❌ Emscripten多线程支持未启用" << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ Emscripten多线程支持已启用" << std::endl;
    
    // 设置环境变量减少线程数
    setenv("OSGEARTH_NODEPAGER_CONCURRENCY", "1", 1);
    setenv("OSGEARTH_TERRAIN_CONCURRENCY", "1", 1);
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    // 检查jobs系统
    if (!jobs::alive()) {
        std::cout << "[main] ❌ jobs系统未激活" << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ jobs系统已激活" << std::endl;
    
    bool all_passed = true;
    
    // 运行所有测试
    if (!test_basic_jobpool()) {
        all_passed = false;
    }
    
    if (!test_job_dispatch()) {
        all_passed = false;
    }
    
    if (!test_multiple_jobs()) {
        all_passed = false;
    }
    
    if (!test_paging_manager_pool()) {
        all_passed = false;
    }
    
    std::cout << "\n=== 测试总结 ===" << std::endl;
    if (all_passed) {
        std::cout << "🎉 所有weejobs测试通过！" << std::endl;
        std::cout << "weejobs多线程系统在WebAssembly中工作正常" << std::endl;
    } else {
        std::cout << "❌ 部分weejobs测试失败" << std::endl;
        std::cout << "需要进一步调试weejobs系统" << std::endl;
    }
    
    return all_passed ? 0 : 1;
}
