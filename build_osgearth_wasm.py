#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
osgEarth库WebAssembly编译脚本
编译osgEarth为WebAssembly版本
"""

import os
import subprocess
import sys

def run_command(cmd, description, cwd=None):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print(f"工作目录: {cwd if cwd else os.getcwd()}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🌍 osgEarth库WebAssembly编译器")
    print("=============================")
    print("编译osgEarth为WebAssembly版本")
    
    # 设置路径
    current_dir = os.getcwd()
    osgearth_source_dir = current_dir  # 使用当前项目目录作为osgEarth源码
    osgearth_build_dir = f"{current_dir}\\build_wasm"
    install_dir = f"F:\\cmo-dev\\my_osgearth_web\\osgearth_third_party\\wasm_dep"
    
    print(f"osgEarth源码目录: {osgearth_source_dir}")
    print(f"构建目录: {osgearth_build_dir}")
    print(f"安装目录: {install_dir}")
    
    # 检查OSG是否已安装
    osg_include = f"{install_dir}\\include\\osg"
    if not os.path.exists(osg_include):
        print(f"❌ OSG库未安装: {osg_include}")
        print("请先运行 build_osg_wasm.py 编译OSG库")
        return False
    
    print("✅ 检测到OSG库已安装")
    
    # 检查源码目录
    if not os.path.exists(osgearth_source_dir):
        print(f"❌ osgEarth源码目录不存在: {osgearth_source_dir}")
        print("请先下载osgEarth源码")
        return False
    
    # 创建构建目录
    if not os.path.exists(osgearth_build_dir):
        os.makedirs(osgearth_build_dir)
        print(f"✅ 创建构建目录: {osgearth_build_dir}")
    
    # 检查Emscripten
    emcc_check = "C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return False
    
    # 配置CMake
    cmake_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcmake cmake "{osgearth_source_dir}" ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX="{install_dir}" ^
        -DOSG_DIR="{install_dir}" ^
        -DOpenThreads_DIR="{install_dir}" ^
        -DOSGEARTH_BUILD_EXAMPLES=OFF ^
        -DOSGEARTH_BUILD_APPLICATIONS=OFF ^
        -DOSGEARTH_BUILD_TESTS=OFF ^
        -DOSGEARTH_BUILD_TOOLS=OFF ^
        -DOSGEARTH_BUILD_PROCEDURAL_NODEKIT=OFF ^
        -DOSGEARTH_BUILD_TRITON_NODEKIT=OFF ^
        -DOSGEARTH_BUILD_SILVERLINING_NODEKIT=OFF ^
        -DDYNAMIC_OSGEARTH=OFF ^
        -DOSGEARTH_USE_PROTOBUF=OFF ^
        -DOSGEARTH_ENABLE_GEOCODER=OFF ^
        -DOSGEARTH_BUILD_SHARED_LIBS=OFF ^
        -DCMAKE_CXX_FLAGS="-DOSG_GL1_AVAILABLE=0 -DOSG_GL2_AVAILABLE=0 -DOSG_GLES2_AVAILABLE=1 -DOSG_GL3_AVAILABLE=0" ^
        -DCMAKE_C_FLAGS="-DOSG_GL1_AVAILABLE=0 -DOSG_GL2_AVAILABLE=0 -DOSG_GLES2_AVAILABLE=1 -DOSG_GL3_AVAILABLE=0"""
    
    if not run_command(cmake_cmd, "配置osgEarth CMake", osgearth_build_dir):
        print("❌ osgEarth CMake配置失败")
        return False
    
    # 编译osgEarth (使用Ninja)
    build_cmd = "C:\\dev\\emsdk\\emsdk_env.bat && ninja"
    if not run_command(build_cmd, "编译osgEarth库", osgearth_build_dir):
        print("❌ osgEarth编译失败")
        return False

    # 安装osgEarth (使用Ninja)
    install_cmd = "C:\\dev\\emsdk\\emsdk_env.bat && ninja install"
    if not run_command(install_cmd, "安装osgEarth库", osgearth_build_dir):
        print("❌ osgEarth安装失败")
        return False
    
    print("\n🎉 osgEarth库WebAssembly编译完成！")
    print(f"安装位置: {install_dir}")
    print("\n库文件结构:")
    print(f"  头文件: {install_dir}\\include")
    print(f"  库文件: {install_dir}\\lib")
    print("\n下一步:")
    print("  现在可以编译基于osgEarth的数字地球应用了")
    print("  运行: python redist_wasm1/compile_earth_basic.py")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
