/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTHANNOTATION_GARS_GRATICLE
#define OSGEARTHANNOTATION_GARS_GRATICLE

#include <osgEarth/VisibleLayer>
#include <osgEarth/Common>
#include <osgEarth/Style>


namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    /**
     * GARS (Global Area Reference System) Graticuler map layer.
     * http://earth-info.nga.mil/GandG/coordsys/grids/gars.html
     */
    class OSGEARTH_EXPORT GARSGraticule : public VisibleLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public VisibleLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, VisibleLayer::Options);
            OE_OPTION(Style, style);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, GARSGraticule, Options, VisibleLayer, GARSGraticule);

        //! Style for rendering the graticule
        void setStyle(const Style& value);
        const Style& getStyle() const;

        //! Call to refresh after setting an option
        void dirty();

    public: // Layer

        virtual void addedToMap(const Map* map);

        virtual void removedFromMap(const Map* map);
        
        virtual osg::Node* getNode() const;

        virtual void init();

    protected:

        /** dtor */
        virtual ~GARSGraticule() { }        

    private:

        void rebuild();
        void build30MinCells();

        UID _uid;
        osg::ref_ptr<const Profile> _profile;
        osg::ref_ptr<osg::Group> _root;
    };  
} } // namespace osgEarth::Util

#endif // OSGEARTHANNOTATION_GARS_GRATICLE
