/**
 * 检测WebAssembly多线程支持的最简单程序
 */

#include <iostream>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

int main() {
    std::cout << "🔍 WebAssembly多线程支持检测" << std::endl;
    std::cout << "============================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[check] 运行在Emscripten环境" << std::endl;
    
    // 检查编译时多线程支持
    std::cout << "[check] 检查编译时多线程支持..." << std::endl;
    
#ifdef __EMSCRIPTEN_PTHREADS__
    std::cout << "[check] ✅ 编译时启用了pthread支持 (__EMSCRIPTEN_PTHREADS__)" << std::endl;
#else
    std::cout << "[check] ❌ 编译时未启用pthread支持" << std::endl;
    return 1;
#endif
    
    // 检查运行时多线程支持
    std::cout << "[check] 检查运行时多线程支持..." << std::endl;
    
    if (emscripten_has_threading_support()) {
        std::cout << "[check] ✅ 运行时多线程支持已启用" << std::endl;
    } else {
        std::cout << "[check] ❌ 运行时多线程支持未启用" << std::endl;
        std::cout << "[check] 可能的原因:" << std::endl;
        std::cout << "[check] 1. HTTP头设置不正确" << std::endl;
        std::cout << "[check] 2. 浏览器不支持SharedArrayBuffer" << std::endl;
        std::cout << "[check] 3. 编译配置问题" << std::endl;
        return 1;
    }
    
    // 检查逻辑核心数
    int cores = emscripten_num_logical_cores();
    std::cout << "[check] 逻辑核心数: " << cores << std::endl;
    
    // 检查是否在主线程
    if (emscripten_is_main_browser_thread()) {
        std::cout << "[check] ✅ 运行在主浏览器线程" << std::endl;
    } else {
        std::cout << "[check] ⚠️ 不在主浏览器线程" << std::endl;
    }
    
    std::cout << "[check] 🎉 所有多线程支持检查通过！" << std::endl;
    
#else
    std::cout << "[check] 运行在桌面环境" << std::endl;
#endif
    
    std::cout << "[check] 程序正常结束" << std::endl;
    return 0;
}
