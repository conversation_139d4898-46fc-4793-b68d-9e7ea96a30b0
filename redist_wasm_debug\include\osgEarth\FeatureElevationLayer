/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_FEATURE_ELEVATION_LAYER
#define OSGEARTH_FEATURE_ELEVATION_LAYER 1

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/ElevationLayer>
#include <osgEarth/LayerReference>

namespace osgEarth
{
    /**
     * Elevation layer that sets elevation based on feature data
     */
    class OSGEARTH_EXPORT FeatureElevationLayer : public ElevationLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public ElevationLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, ElevationLayer::Options);
            OE_OPTION(std::string, attr);
            OE_OPTION(double, offset);
            OE_OPTION_LAYER(FeatureSource, featureSource);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, FeatureElevationLayer, Options, ElevationLayer, FeatureElevation);

    public: // ElevationLayer

        // opens the layer and returns the status
        virtual Status openImplementation();

        virtual void init();

        virtual Config getConfig() const;

    protected: // Layer

        // called by the map when this layer is added/removed
        virtual void addedToMap(const class Map*);

        virtual void removedFromMap(const class Map*);

        virtual GeoHeightField createHeightFieldImplementation(
            const TileKey& key,
            ProgressCallback* progress) const;

    protected:

        virtual ~FeatureElevationLayer();

        GeoExtent _extent;

        bool intersects(const TileKey&) const;
    };
} // namespace osgEarth

#endif // OSGEARTH_FEATURE_ELEVATION_LAYER
