/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHFEATURES_BUFFER_FILTER_H
#define OSGEARTHFEATURES_BUFFER_FILTER_H 1

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Filter>
#include <osgEarth/Style>
#include <osg/Geode>

namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    /**
     * The buffer geometry operation takes each input vector shape and applies
     * morphological dilation or erosion to it.
     * (see http://en.wikipedia.org/wiki/Morphological_image_processing)
     *
     * The buffer distance determines which operation to use:
     *
     * A positive distance dilates the shape (expands it).
     * A negative distance erodes the shape (shrinks it). 
     *
     * Note: buffering always converts the input vector data into polygons. 
     */
    class OSGEARTH_EXPORT BufferFilter : public FeatureFilter
    {
    public:
        // Call this determine whether this filter is available. This filter uses
        // the GEOS library, so you must compile osgEarth against GEOS for this 
        // filter to be available.
        static bool isSupported();

    public:
        BufferFilter();
        BufferFilter( const BufferFilter& rhs );

        BufferFilter( const Config& conf );

        /**
         * Serialize this FeatureFilter
         */
        virtual Config getConfig() const;


        virtual ~BufferFilter() { }

    public:
        // how far to buffer; positive to dialate, negative to erode
        optional<double>& distance() { return _distance; }
        const optional<double>& distance() const { return _distance; }

        // for ENDCAP_ROUND, controls the tessellation detail for corners, 
        // measured in the number of segments to create per 90 degrees
        int numQuadrantSegments() const { return _numQuadSegs; }
        int& numQuadrantSegments() { return _numQuadSegs; }

        // tessellation style of buffered corners and line ends
        const Stroke::LineCapStyle& capStyle() const { return _capStyle; }
        Stroke::LineCapStyle& capStyle() { return _capStyle; }

    public:
        virtual FilterContext push( FeatureList& input, FilterContext& context );

    protected:
        optional<double>     _distance;
        int                  _numQuadSegs;
        Stroke::LineCapStyle _capStyle;
    };

} }

#endif // OSGEARTHFEATURES_BUFFER_FILTER_H
