/**
 * SDL2基础测试程序
 * 测试WebAssembly中的SDL2图形功能
 */

#include <iostream>
#include <cmath>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#include <GLES2/gl2.h>
#else
#include <SDL.h>
#include <SDL_opengl.h>
#endif

// 全局变量
static SDL_Window *g_window = nullptr;
static SDL_GLContext g_gl_context = nullptr;
static bool g_running = true;
static float g_rotation = 0.0f;

// 简单的OpenGL渲染
void render()
{
    // 清除屏幕
    glClearColor(0.1f, 0.2f, 0.3f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT);

    // 绘制一个旋转的三角形
    glLoadIdentity();
    glRotatef(g_rotation, 0.0f, 0.0f, 1.0f);

    glBegin(GL_TRIANGLES);
    glColor3f(1.0f, 0.0f, 0.0f);
    glVertex2f(0.0f, 0.5f);
    glColor3f(0.0f, 1.0f, 0.0f);
    glVertex2f(-0.5f, -0.5f);
    glColor3f(0.0f, 0.0f, 1.0f);
    glVertex2f(0.5f, -0.5f);
    glEnd();

    // 更新旋转角度
    g_rotation += 1.0f;
    if (g_rotation >= 360.0f)
    {
        g_rotation = 0.0f;
    }

    SDL_GL_SwapWindow(g_window);
}

// 处理事件
void handle_events()
{
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        switch (event.type)
        {
        case SDL_QUIT:
            g_running = false;
            std::cout << "[event] 收到退出事件" << std::endl;
            break;

        case SDL_KEYDOWN:
            std::cout << "[event] 按键按下: " << SDL_GetKeyName(event.key.keysym.sym) << std::endl;
            if (event.key.keysym.sym == SDLK_ESCAPE)
            {
                g_running = false;
            }
            break;

        case SDL_MOUSEBUTTONDOWN:
            std::cout << "[event] 鼠标按下: (" << event.button.x << ", " << event.button.y << ")" << std::endl;
            break;
        }
    }
}

// 主循环函数
void main_loop()
{
    if (!g_running)
    {
        return;
    }

    handle_events();
    render();
}

int main(int argc, char *argv[])
{
    std::cout << "🎮 SDL2基础测试程序" << std::endl;
    std::cout << "===================" << std::endl;

#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif

    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;

    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 24);

    // 创建窗口
    g_window = SDL_CreateWindow(
        "SDL2 + WebAssembly Test",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_window)
    {
        std::cout << "[main] ❌ 窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 窗口创建成功: 800x600" << std::endl;

    // 创建OpenGL上下文
    g_gl_context = SDL_GL_CreateContext(g_window);
    if (!g_gl_context)
    {
        std::cout << "[main] ❌ OpenGL上下文创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(g_window);
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ OpenGL上下文创建成功" << std::endl;

    // 设置垂直同步
    SDL_GL_SetSwapInterval(1);

    // 获取OpenGL信息
    const char *gl_vendor = (const char *)glGetString(GL_VENDOR);
    const char *gl_renderer = (const char *)glGetString(GL_RENDERER);
    const char *gl_version = (const char *)glGetString(GL_VERSION);

    std::cout << "[gl] 供应商: " << (gl_vendor ? gl_vendor : "未知") << std::endl;
    std::cout << "[gl] 渲染器: " << (gl_renderer ? gl_renderer : "未知") << std::endl;
    std::cout << "[gl] 版本: " << (gl_version ? gl_version : "未知") << std::endl;

    // 设置视口
    glViewport(0, 0, 800, 600);
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    glOrtho(-1.0, 1.0, -1.0, 1.0, -1.0, 1.0);
    glMatrixMode(GL_MODELVIEW);

    std::cout << "[main] ✅ OpenGL设置完成" << std::endl;
    std::cout << "[main] 开始渲染循环..." << std::endl;
    std::cout << "[main] 按ESC键或关闭窗口退出" << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly主循环
    emscripten_set_main_loop(main_loop, 60, 1); // 60 FPS
#else
    // 桌面主循环
    while (g_running)
    {
        main_loop();
        SDL_Delay(16); // ~60 FPS
    }
#endif

    // 清理资源
    if (g_gl_context)
    {
        SDL_GL_DeleteContext(g_gl_context);
    }
    if (g_window)
    {
        SDL_DestroyWindow(g_window);
    }
    SDL_Quit();

    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
