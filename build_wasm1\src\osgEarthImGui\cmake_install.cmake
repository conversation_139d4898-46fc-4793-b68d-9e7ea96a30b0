# Install script for directory: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui

# Set the install prefix
if(NOT DEFINED CMAKE_INSTALL_PREFIX)
  set(CMAKE_INSTALL_PREFIX "C:/dev/emsdk/upstream/emscripten/cache/sysroot")
endif()
string(REGEX REPLACE "/$" "" CMAKE_INSTALL_PREFIX "${CMAKE_INSTALL_PREFIX}")

# Set the install configuration name.
if(NOT DEFINED CMAKE_INSTALL_CONFIG_NAME)
  if(BUILD_TYPE)
    string(REGEX REPLACE "^[^A-Za-z0-9_]+" ""
           CMAKE_INSTALL_CONFIG_NAME "${BUILD_TYPE}")
  else()
    set(CMAKE_INSTALL_CONFIG_NAME "Debug")
  endif()
  message(STATUS "Install configuration: \"${CMAKE_INSTALL_CONFIG_NAME}\"")
endif()

# Set the component getting installed.
if(NOT CMAKE_INSTALL_COMPONENT)
  if(COMPONENT)
    message(STATUS "Install component: \"${COMPONENT}\"")
    set(CMAKE_INSTALL_COMPONENT "${COMPONENT}")
  else()
    set(CMAKE_INSTALL_COMPONENT)
  endif()
endif()

# Is this installation the result of a crosscompile?
if(NOT DEFINED CMAKE_CROSSCOMPILING)
  set(CMAKE_CROSSCOMPILING "TRUE")
endif()

# Set default install directory permissions.
if(NOT DEFINED CMAKE_OBJDUMP)
  set(CMAKE_OBJDUMP "C:/Program Files/LLVM/bin/llvm-objdump.exe")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib" TYPE STATIC_LIBRARY FILES "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/libosgEarthImGui.a")
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/include/osgEarthImGui" TYPE FILE FILES
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/Common"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ImGuiEventHandler"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ImGuiApp"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ImGuiPanel"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/imgui.h"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/imgui_internal.h"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/imgui_impl_opengl3.h"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/imconfig.h"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/imnodes.h"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/AnnotationsGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/CameraGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ContentBrowserGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/EnvironmentGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/LayersGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/NetworkMonitorGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/NotifyGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/OpenEarthFileGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/LiveCamerasGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/NodeGraphGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/PickerGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/RenderingGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/SceneGraphGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/SearchGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ShaderGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/SystemGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/TerrainGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/TextureInspectorGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/ViewpointsGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/FeatureEditGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/LifeMapLayerGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/TerrainEditGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/TextureSplattingLayerGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/VegetationLayerGUI"
    "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/src/osgEarthImGui/CesiumIonGUI"
    )
endif()

if(CMAKE_INSTALL_COMPONENT STREQUAL "Unspecified" OR NOT CMAKE_INSTALL_COMPONENT)
  if(EXISTS "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth/osgEarthImGui-targets.cmake")
    file(DIFFERENT _cmake_export_file_changed FILES
         "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth/osgEarthImGui-targets.cmake"
         "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/src/osgEarthImGui/CMakeFiles/Export/3090bd26eefb85cb002564afc4aabd76/osgEarthImGui-targets.cmake")
    if(_cmake_export_file_changed)
      file(GLOB _cmake_old_config_files "$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth/osgEarthImGui-targets-*.cmake")
      if(_cmake_old_config_files)
        string(REPLACE ";" ", " _cmake_old_config_files_text "${_cmake_old_config_files}")
        message(STATUS "Old export file \"$ENV{DESTDIR}${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth/osgEarthImGui-targets.cmake\" will be replaced.  Removing files [${_cmake_old_config_files_text}].")
        unset(_cmake_old_config_files_text)
        file(REMOVE ${_cmake_old_config_files})
      endif()
      unset(_cmake_old_config_files)
    endif()
    unset(_cmake_export_file_changed)
  endif()
  file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/src/osgEarthImGui/CMakeFiles/Export/3090bd26eefb85cb002564afc4aabd76/osgEarthImGui-targets.cmake")
  if(CMAKE_INSTALL_CONFIG_NAME MATCHES "^([Dd][Ee][Bb][Uu][Gg])$")
    file(INSTALL DESTINATION "${CMAKE_INSTALL_PREFIX}/lib/cmake/osgearth" TYPE FILE FILES "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/src/osgEarthImGui/CMakeFiles/Export/3090bd26eefb85cb002564afc4aabd76/osgEarthImGui-targets-debug.cmake")
  endif()
endif()

