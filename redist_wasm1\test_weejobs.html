<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>weejobs多线程系统测试</title>
    <style>
        body {
            font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧵 weejobs多线程系统测试</h1>
        
        <div id="status" class="status">状态: 准备测试weejobs多线程系统...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>测试简化的weejobs多线程系统在WebAssembly中的工作情况：</p>
            <ul>
                <li>基础线程创建和执行</li>
                <li>std::async任务分发</li>
                <li>简化线程池功能</li>
                <li>多线程环境兼容性</li>
            </ul>
        </div>
        
        <div class="log">
            <h3>📝 测试日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // Module配置
        var Module = {
            print: function(text) {
                console.log('weejobs Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 检查成功和失败标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                } else if (text.includes('❌')) {
                    addLog(text, 'error');
                } else if (text.includes('⚠️')) {
                    addLog(text, 'warn');
                }
            },
            
            printErr: function(text) {
                console.error('weejobs Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('weejobs多线程系统测试模块初始化完成');
                addLog('weejobs多线程系统测试模块初始化完成', 'success');
                updateStatus('weejobs多线程系统测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('weejobs多线程系统测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                
                if (status === 0) {
                    updateStatus('🎉 weejobs多线程系统测试完成！', false, true);
                } else {
                    updateStatus('❌ weejobs多线程系统测试失败', true);
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始weejobs多线程系统测试', 'info');
            
            updateStatus('加载weejobs多线程系统测试模块...');
            addLog('开始加载weejobs多线程系统测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_weejobs.js';
            script.onload = function() {
                addLog('weejobs多线程系统测试脚本加载成功', 'success');
                updateStatus('weejobs多线程系统测试脚本已加载');
            };
            script.onerror = function() {
                addLog('weejobs多线程系统测试脚本加载失败', 'error');
                updateStatus('weejobs多线程系统测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
