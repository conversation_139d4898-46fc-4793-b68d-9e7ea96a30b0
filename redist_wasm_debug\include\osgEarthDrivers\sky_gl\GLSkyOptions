/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_GL_SKY_OPTIONS
#define OSGEARTH_DRIVER_GL_SKY_OPTIONS 1

#include <osgEarth/Sky>

namespace osgEarth { namespace GLSky
{
    using namespace osgEarth;

    /**
     * Options for creating a sky with GL-like phong shading.
     */
    class GLSkyOptions : public SkyOptions
    {
    public:
        GLSkyOptions(const ConfigOptions& options =ConfigOptions()) :
          SkyOptions(options)
        {
            setDriver( "gl" );
            fromConfig( _conf );
        }
        virtual ~GLSkyOptions() { }

    public: // properties

    public:
        Config getConfig() const {
            Config conf = SkyOptions::getConfig();
            // adds
            return conf;
        }

    protected:
        void mergeConfig( const Config& conf ) {
            SkyOptions::mergeConfig( conf );
            fromConfig(conf);
        }

    private:
        void fromConfig( const Config& conf ) {
            // gets
        }
    };

} } // namespace osgEarth::Drivers::GLSky

#endif // OSGEARTH_DRIVER_GL_SKY_OPTIONS
