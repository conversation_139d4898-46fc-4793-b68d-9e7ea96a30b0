/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_DETAIL_OPTIONS
#define OSGEARTH_DRIVER_DETAIL_OPTIONS 1

#include <osgEarth/Common>
#include <osgEarth/URI>

namespace osgEarth { namespace Detail
{
    using namespace osgEarth;

    class DetailOptions : public DriverConfigOptions // NO EXPORT; header only
    {
    public:
        // The lod that the detail texture is displayed at.
        optional<unsigned>& lod() { return _lod; }
        const optional<unsigned>& size() const { return _lod; }

        // detail texture.
        optional<URI>& imageURI() { return _imageURI; }
        const optional<URI>& imageURI() const { return _imageURI; }

        // The alpha of the detail texture.
        optional<float>& alpha() { return _alpha; }
        const optional<float>& alpha() const { return _alpha; }

        // The max range of the detail texture.
        optional<float>& maxRange() { return _maxRange; }
        const optional<float>& maxRange() const { return _maxRange; }

        // The attenutation distance of the detail texture.
        optional<float>& attenDist() { return _attenDist; }
        const optional<float>& attenDist() const { return _attenDist; }

    public:
        DetailOptions( const ConfigOptions& opt =ConfigOptions() ) : DriverConfigOptions( opt )
        {
            setDriver( "detail" );
            _lod.init( 23u );            
            _alpha.init( 0.5 ); 
            _maxRange.init(6000.0);
            _attenDist.init(2000.0f);
            fromConfig( _conf );
        }

        virtual ~DetailOptions() { }

    public:
        Config getConfig() const {
            Config conf = DriverConfigOptions::getConfig();
            conf.set("image", _imageURI);
            conf.set("lod", _lod);
            conf.set("alpha", _alpha);
            conf.set("max_range", _maxRange);
            conf.set("attenuation", _attenDist);
            return conf;
        }

    protected:
        void mergeConfig( const Config& conf ) {
            DriverConfigOptions::mergeConfig( conf );
            fromConfig( conf );
        }

    private:
        void fromConfig( const Config& conf ) {
            conf.get( "image", _imageURI );
            conf.get( "lod", _lod );
            conf.get( "alpha", _alpha );
            conf.get( "max_range", _maxRange );
            conf.get( "attenuation", _attenDist);
        }

        optional<URI> _imageURI;
        optional<unsigned int> _lod;
        optional<float> _alpha;
        optional<float> _maxRange;
        optional<float> _attenDist;
    };

} } // namespace osgEarth::Detail

#endif // OSGEARTH_DRIVER_DETAIL_OPTIONS

