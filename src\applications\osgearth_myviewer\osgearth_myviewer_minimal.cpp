#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/EarthManipulator>
#include <osgEarth/GLUtils>
#include <osgEarth/XYZImageLayer>
#include <osgEarth/XYZElevationLayer>
#include <osgEarth/GeodeticGraticule>
#include <osgEarth/Sky>
#include <osgEarth/Profile>
#include <osgEarth/URI>
#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgGA/StateSetManipulator>
#include <iostream>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include "WebAssemblyHTTPClient.h"
#include "WebAssemblyInputHandler.h"
#endif

using namespace osgEarth;
using namespace osgEarth::Util;

// 创建完整的数字地球地图
osg::ref_ptr<Map> createEarthMap()
{
    std::cout << "[earth] 创建完整数字地球地图..." << std::endl;

    auto map = new Map();

    try
    {
#ifdef EMSCRIPTEN
        // WebAssembly环境：使用OpenStreetMap避免网络问题
        std::cout << "[earth] WebAssembly环境：添加OpenStreetMap图像层" << std::endl;

        auto osmImagery = new XYZImageLayer();
        osmImagery->setName("OpenStreetMap");
        osmImagery->setURL("https://a.tile.openstreetmap.org/{z}/{x}/{y}.png");
        osmImagery->setFormat("png");

        // 使用spherical-mercator配置文件
        const Profile *profile = Profile::create("spherical-mercator");
        osmImagery->setProfile(profile);
        osmImagery->setMinLevel(0);
        osmImagery->setMaxLevel(10); // 降低最大级别减少网络负载
        osmImagery->setEnabled(true);
        osmImagery->setVisible(true);

        map->addLayer(osmImagery);
        std::cout << "[earth] ✅ OpenStreetMap图像层添加成功" << std::endl;
#else
        // 桌面环境：使用Google卫星图像
        std::cout << "[earth] 桌面环境：添加Google卫星图像层" << std::endl;

        auto googleImagery = new XYZImageLayer();
        googleImagery->setName("Google Satellite");
        googleImagery->setURL("https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}");
        googleImagery->setFormat("jpg");

        const Profile *profile = Profile::create("spherical-mercator");
        googleImagery->setProfile(profile);
        googleImagery->setMinLevel(0);
        googleImagery->setMaxLevel(18);
        googleImagery->setEnabled(true);
        googleImagery->setVisible(true);

        map->addLayer(googleImagery);
        std::cout << "[earth] ✅ Google卫星图像层添加成功" << std::endl;

        // 添加AWS地形高程层
        std::cout << "[earth] 添加AWS地形高程层" << std::endl;

        auto awsElevation = new XYZElevationLayer();
        awsElevation->setName("AWS Terrarium");
        awsElevation->setURL("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png");
        awsElevation->setProfile(profile);
        awsElevation->setMinLevel(0);
        awsElevation->setMaxLevel(15);
        awsElevation->setEnabled(true);
        awsElevation->setVisible(true);

        map->addLayer(awsElevation);
        std::cout << "[earth] ✅ AWS地形高程层添加成功" << std::endl;
#endif

        // 添加经纬度网格 - 对所有版本都使用
        std::cout << "[earth] 添加经纬度网格..." << std::endl;

        auto graticule = new GeodeticGraticule();
        graticule->setName("Geodetic Graticule");
        graticule->setColor(Color(0.8f, 0.8f, 0.0f, 0.8f)); // 半透明黄色
        graticule->setLineWidth(1.0f);                      // 细线
        graticule->setGridLinesVisible(true);
        graticule->setGridLabelsVisible(true);
        graticule->setEdgeLabelsVisible(true);
        graticule->setVisible(true);
        graticule->setEnabled(true);

        map->addLayer(graticule);
        std::cout << "[earth] ✅ 经纬度网格添加成功" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cerr << "[earth] ❌ 地图创建异常: " << e.what() << std::endl;
        return nullptr;
    }

    std::cout << "[earth] ✅ 完整数字地球地图创建成功" << std::endl;
    return map;
}

// 添加天空效果
void addSkyEffects(MapNode *mapNode)
{
    std::cout << "[earth] 添加天空效果..." << std::endl;

    try
    {
        // 创建简单天空节点
        auto sky = SkyNode::create();
        sky->setName("Sky");
        sky->setDateTime(DateTime(2024, 6, 21, 12.0)); // 夏至正午
        sky->attach(&mapNode->getViewer());

        // 添加到地图节点
        mapNode->addChild(sky);

        std::cout << "[earth] ✅ 天空效果添加成功" << std::endl;
    }
    catch (const std::exception &e)
    {
        std::cout << "[earth] ⚠️ 天空效果添加失败: " << e.what() << std::endl;
    }
}

// 自定义事件处理器
class EarthEventHandler : public osgGA::GUIEventHandler
{
public:
    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        switch (ea.getEventType())
        {
        case osgGA::GUIEventAdapter::KEYDOWN:
            std::cout << "[event] 按键按下: " << ea.getKey() << std::endl;

            switch (ea.getKey())
            {
            case osgGA::GUIEventAdapter::KEY_Escape:
                std::cout << "[event] ESC键，准备退出" << std::endl;
                return true;

            case osgGA::GUIEventAdapter::KEY_Space:
                std::cout << "[event] 空格键，重置视角到地球" << std::endl;
                {
                    EarthManipulator *manip =
                        dynamic_cast<EarthManipulator *>(aa.getCameraManipulator());
                    if (manip)
                    {
                        manip->setViewpoint(Viewpoint("Earth", 0.0, 0.0, 0.0, 0.0, -90.0, 15000000.0));
                    }
                }
                return true;

            case osgGA::GUIEventAdapter::KEY_1:
                std::cout << "[event] 1键，飞到北京" << std::endl;
                {
                    EarthManipulator *manip =
                        dynamic_cast<EarthManipulator *>(aa.getCameraManipulator());
                    if (manip)
                    {
                        manip->setViewpoint(Viewpoint("Beijing", 116.3, 39.9, 0.0, 0.0, -30.0, 50000.0));
                    }
                }
                return true;

            case osgGA::GUIEventAdapter::KEY_2:
                std::cout << "[event] 2键，飞到纽约" << std::endl;
                {
                    EarthManipulator *manip =
                        dynamic_cast<EarthManipulator *>(aa.getCameraManipulator());
                    if (manip)
                    {
                        manip->setViewpoint(Viewpoint("New York", -74.0, 40.7, 0.0, 0.0, -30.0, 50000.0));
                    }
                }
                return true;
            }
            break;

        case osgGA::GUIEventAdapter::PUSH:
            std::cout << "[event] 鼠标按下: (" << ea.getX() << ", " << ea.getY() << ") 按钮: " << ea.getButton() << std::endl;
            return false;

        case osgGA::GUIEventAdapter::SCROLL:
            std::cout << "[event] 鼠标滚轮: " << ea.getScrollingMotion() << std::endl;
            return false;
        }

        return false;
    }
};

int main(int argc, char **argv)
{
    std::cout << "[minimal] 启动最小化osgEarth WebAssembly应用" << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly环境：设置环境变量减少线程使用
    std::cout << "[minimal] WebAssembly环境：设置最小线程并发" << std::endl;
    setenv("OSGEARTH_NODEPAGER_CONCURRENCY", "1", 1);
    setenv("OSGEARTH_TERRAIN_CONCURRENCY", "1", 1);
#endif

    try
    {
        // 初始化osgEarth
        osg::ArgumentParser arguments(&argc, argv);
        osgEarth::initialize(arguments);
        std::cout << "[minimal] osgEarth初始化完成" << std::endl;

        // 创建viewer
        osgViewer::Viewer viewer;
        std::cout << "[minimal] Viewer创建完成" << std::endl;

        // 应用全局默认设置
        GLUtils::setGlobalDefaults(viewer.getCamera()->getOrCreateStateSet());
        std::cout << "[minimal] GL默认设置完成" << std::endl;

#ifdef EMSCRIPTEN
        // WebAssembly平台配置
        std::cout << "[minimal] 配置WebAssembly环境" << std::endl;

        // 设置视口
        int canvasWidth = 800;
        int canvasHeight = 600;
        viewer.getCamera()->setViewport(new osg::Viewport(0, 0, canvasWidth, canvasHeight));

        // 设置投影矩阵
        double fovy = 45.0;
        double aspectRatio = double(canvasWidth) / double(canvasHeight);
        double zNear = 1.0;
        double zFar = 1e12;
        viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        viewer.getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);

        std::cout << "[minimal] WebAssembly渲染环境配置完成" << std::endl;
#endif

        // 设置相机操作器
        auto manip = new EarthManipulator();
        viewer.setCameraManipulator(manip);
        std::cout << "[minimal] 相机操作器设置完成" << std::endl;

        // 创建完整数字地球地图
        osg::ref_ptr<Map> map = createEarthMap();
        if (!map.valid())
        {
            std::cerr << "[earth] 错误：无法创建地图" << std::endl;
            return 1;
        }
        std::cout << "[earth] 地图验证通过" << std::endl;

        // 创建地图节点
        std::cout << "[earth] 创建地图节点..." << std::endl;
        osg::ref_ptr<MapNode> mapNode = new MapNode(map.get());
        if (!mapNode.valid())
        {
            std::cerr << "[earth] 错误：无法创建地图节点" << std::endl;
            return 1;
        }
        std::cout << "[earth] 地图节点创建成功" << std::endl;

        // 设置场景数据
        std::cout << "[earth] 设置场景数据..." << std::endl;
        viewer.setSceneData(mapNode.get());
        std::cout << "[earth] 场景数据设置完成" << std::endl;

        // 添加天空效果
        addSkyEffects(mapNode.get());

        // 添加事件处理器
        std::cout << "[earth] 添加事件处理器..." << std::endl;
        viewer.addEventHandler(new EarthEventHandler());
        viewer.addEventHandler(new osgGA::StateSetManipulator(viewer.getCamera()->getOrCreateStateSet()));
        viewer.addEventHandler(new osgViewer::StatsHandler());
        viewer.addEventHandler(new osgViewer::WindowSizeHandler());
        std::cout << "[earth] ✅ 事件处理器添加成功" << std::endl;

        // 设置默认视点（地球全景）
        std::cout << "[earth] 设置默认视点..." << std::endl;
        Viewpoint vp;
        vp.focalPoint() = GeoPoint(
            mapNode->getMapSRS(),
            0.0, 0.0, 0.0, // 经度0，纬度0（非洲西海岸）
            ALTMODE_ABSOLUTE);
        vp.range() = Distance(15000000.0, Units::METERS); // 15000公里高度
        vp.heading() = Angle(0.0, Units::DEGREES);
        vp.pitch() = Angle(-90.0, Units::DEGREES); // 俯视角度

        EarthManipulator *earthManip = dynamic_cast<EarthManipulator *>(viewer.getCameraManipulator());
        if (earthManip)
        {
            earthManip->setViewpoint(vp);
            std::cout << "[earth] 视点设置完成" << std::endl;
        }

        std::cout << "[earth] ✅ 完整数字地球应用初始化完成" << std::endl;
        std::cout << "[earth] 控制说明:" << std::endl;
        std::cout << "[earth] - 鼠标左键拖拽: 旋转地球" << std::endl;
        std::cout << "[earth] - 鼠标右键拖拽: 缩放" << std::endl;
        std::cout << "[earth] - 鼠标中键拖拽: 平移" << std::endl;
        std::cout << "[earth] - 空格键: 重置视角" << std::endl;
        std::cout << "[earth] - 1键: 飞到北京" << std::endl;
        std::cout << "[earth] - 2键: 飞到纽约" << std::endl;
        std::cout << "[earth] - ESC键: 退出" << std::endl;

        // 运行viewer
        std::cout << "[earth] 开始运行数字地球..." << std::endl;

#ifdef EMSCRIPTEN
        // WebAssembly环境使用emscripten_set_main_loop
        std::cout << "[earth] 使用WebAssembly渲染循环" << std::endl;

        // 设置帧率为60fps
        viewer.setUpViewerAsEmbeddedInWindow(0, 0, 800, 600);

        // 创建全局viewer指针供回调函数使用
        static osgViewer::Viewer *g_viewer = &viewer;

        // 定义渲染回调函数
        auto main_loop = []()
        {
            if (g_viewer && !g_viewer->done())
            {
                g_viewer->frame();
            }
        };

        // 设置主循环
        emscripten_set_main_loop(main_loop, 60, 1);

        return 0;
#else
        // 桌面环境使用标准viewer.run()
        return viewer.run();
#endif
    }
    catch (const std::exception &e)
    {
        std::cerr << "[minimal] 异常: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "[minimal] 未知异常" << std::endl;
        return 1;
    }
}
