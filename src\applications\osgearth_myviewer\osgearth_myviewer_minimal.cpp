#include <osgEarth/MapNode>
#include <osgEarth/Map>
#include <osgEarth/EarthManipulator>
#include <osgEarth/GLUtils>
#include <osgViewer/Viewer>
#include <iostream>

#ifdef EMSCRIPTEN
#include "WebAssemblyHTTPClient.h"
#include "WebAssemblyInputHandler.h"
#endif

using namespace osgEarth;
using namespace osgEarth::Util;

// 简化的地图创建函数
osg::ref_ptr<Map> createMinimalMap()
{
    std::cout << "[minimal] 创建最小化地图" << std::endl;

    auto map = new Map();

    // 创建一个空的地图，不添加任何图层
    std::cout << "[minimal] 地图创建完成（空地图）" << std::endl;
    return map;
}

int main(int argc, char **argv)
{
    std::cout << "[minimal] 启动最小化osgEarth WebAssembly应用" << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly环境：设置环境变量减少线程使用
    std::cout << "[minimal] WebAssembly环境：设置最小线程并发" << std::endl;
    setenv("OSGEARTH_NODEPAGER_CONCURRENCY", "1", 1);
    setenv("OSGEARTH_TERRAIN_CONCURRENCY", "1", 1);
#endif

    try
    {
        // 初始化osgEarth
        osg::ArgumentParser arguments(&argc, argv);
        osgEarth::initialize(arguments);
        std::cout << "[minimal] osgEarth初始化完成" << std::endl;

        // 创建viewer
        osgViewer::Viewer viewer;
        std::cout << "[minimal] Viewer创建完成" << std::endl;

        // 应用全局默认设置
        GLUtils::setGlobalDefaults(viewer.getCamera()->getOrCreateStateSet());
        std::cout << "[minimal] GL默认设置完成" << std::endl;

#ifdef EMSCRIPTEN
        // WebAssembly平台配置
        std::cout << "[minimal] 配置WebAssembly环境" << std::endl;

        // 设置视口
        int canvasWidth = 800;
        int canvasHeight = 600;
        viewer.getCamera()->setViewport(new osg::Viewport(0, 0, canvasWidth, canvasHeight));

        // 设置投影矩阵
        double fovy = 45.0;
        double aspectRatio = double(canvasWidth) / double(canvasHeight);
        double zNear = 1.0;
        double zFar = 1e12;
        viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
        viewer.getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);

        std::cout << "[minimal] WebAssembly渲染环境配置完成" << std::endl;
#endif

        // 设置相机操作器
        auto manip = new EarthManipulator();
        viewer.setCameraManipulator(manip);
        std::cout << "[minimal] 相机操作器设置完成" << std::endl;

        // 创建最小化地图
        osg::ref_ptr<Map> map = createMinimalMap();
        if (!map.valid())
        {
            std::cerr << "[minimal] 错误：无法创建地图" << std::endl;
            return 1;
        }
        std::cout << "[minimal] 地图验证通过" << std::endl;

        // 创建地图节点
        std::cout << "[minimal] 创建地图节点..." << std::endl;
        osg::ref_ptr<MapNode> mapNode = new MapNode(map.get());
        if (!mapNode.valid())
        {
            std::cerr << "[minimal] 错误：无法创建地图节点" << std::endl;
            return 1;
        }
        std::cout << "[minimal] 地图节点创建成功" << std::endl;

        // 设置场景数据
        std::cout << "[minimal] 设置场景数据..." << std::endl;
        viewer.setSceneData(mapNode.get());
        std::cout << "[minimal] 场景数据设置完成" << std::endl;

        // 设置默认视点
        std::cout << "[minimal] 设置默认视点..." << std::endl;
        Viewpoint vp;
        vp.focalPoint() = GeoPoint(
            mapNode->getMapSRS(),
            104.0, 35.5, 0.0,
            ALTMODE_ABSOLUTE);
        vp.range() = Distance(15000000.0, Units::METERS);
        vp.heading() = Angle(0.0, Units::DEGREES);
        vp.pitch() = Angle(-90.0, Units::DEGREES);

        EarthManipulator *earthManip = dynamic_cast<EarthManipulator *>(viewer.getCameraManipulator());
        if (earthManip)
        {
            earthManip->setViewpoint(vp);
            std::cout << "[minimal] 视点设置完成" << std::endl;
        }

        std::cout << "[minimal] 最小化osgEarth应用初始化完成" << std::endl;

        // 运行viewer
        std::cout << "[minimal] 开始运行viewer..." << std::endl;
        return viewer.run();
    }
    catch (const std::exception &e)
    {
        std::cerr << "[minimal] 异常: " << e.what() << std::endl;
        return 1;
    }
    catch (...)
    {
        std::cerr << "[minimal] 未知异常" << std::endl;
        return 1;
    }
}
