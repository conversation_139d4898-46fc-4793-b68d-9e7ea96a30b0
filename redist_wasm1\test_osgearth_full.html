<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth完整数字地球测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #0a0a1a, #1a1a2e);
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .earth-container {
            background: linear-gradient(135deg, #0a0a2e, #1a1a3e);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            border: 3px solid #444;
            box-shadow: 0 0 30px rgba(0, 212, 255, 0.4);
        }
        .controls {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .control-group {
            text-align: center;
        }
        .control-group h4 {
            margin: 0 0 10px 0;
            color: #00d4ff;
        }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
        .log-thread { color: #ff9500; }
        .log-earth { color: #00ff88; }
        canvas {
            border: 3px solid #444;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
        .feature-highlight {
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 <span class="feature-highlight">osgEarth完整数字地球测试</span></h1>
        
        <div id="status" class="status">状态: 准备加载osgEarth完整数字地球...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>测试osgEarth库在WebAssembly中的完整功能：</p>
            <ul>
                <li><strong>Google卫星图像层</strong> - 真实卫星影像</li>
                <li><strong>AWS地形高程数据</strong> - 3D地形渲染</li>
                <li><strong>EarthManipulator</strong> - 专业地球导航</li>
                <li><strong>weejobs线程池</strong> - 多线程支持</li>
                <li><strong>完整交互控制</strong> - 鼠标键盘响应</li>
            </ul>
        </div>
        
        <div class="earth-container">
            <h3>🌍 osgEarth数字地球视窗</h3>
            <p>完整的osgEarth数字地球，包含真实卫星影像和地形数据</p>
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h4>🖱️ 地球导航</h4>
                <p>• 左键拖拽: 旋转地球</p>
                <p>• 右键拖拽: 缩放</p>
                <p>• 中键拖拽: 平移</p>
                <p>• 滚轮: 高度调整</p>
            </div>
            
            <div class="control-group">
                <h4>⌨️ 快捷键</h4>
                <p>• 空格键: 重置到地球视角</p>
                <p>• 1键: 飞到北京</p>
                <p>• 2键: 飞到纽约</p>
                <p>• T键: 测试线程池</p>
                <p>• ESC键: 退出</p>
            </div>
            
            <div class="control-group">
                <h4>🗺️ 数据层</h4>
                <p>• Google卫星影像</p>
                <p>• AWS Terrarium高程</p>
                <p>• 球面墨卡托投影</p>
                <p>• 多级细节(LOD)</p>
            </div>
            
            <div class="control-group">
                <h4>🧵 线程支持</h4>
                <p>• weejobs线程池</p>
                <p>• 4个工作线程</p>
                <p>• 异步任务处理</p>
                <p>• 按T键测试</p>
            </div>
        </div>
        
        <div class="log">
            <h3>📝 系统日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // Module配置
        var Module = {
            canvas: document.getElementById('canvas'),
            
            print: function(text) {
                console.log('osgEarth Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 检查成功和失败标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                } else if (text.includes('❌')) {
                    addLog(text, 'error');
                } else if (text.includes('⚠️')) {
                    addLog(text, 'warn');
                }
                
                // 检查特定事件类型
                if (text.includes('[event]')) {
                    addLog(text, 'info');
                } else if (text.includes('[earth]')) {
                    addLog(text, 'earth');
                } else if (text.includes('[thread]')) {
                    addLog(text, 'thread');
                }
            },
            
            printErr: function(text) {
                console.error('osgEarth Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('osgEarth完整数字地球测试模块初始化完成');
                addLog('osgEarth完整数字地球测试模块初始化完成', 'success');
                updateStatus('osgEarth完整数字地球测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('osgEarth完整数字地球测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                
                if (status === 0) {
                    updateStatus('🎉 osgEarth完整数字地球测试完成！', false, true);
                } else {
                    updateStatus('❌ osgEarth完整数字地球测试失败', true);
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始osgEarth完整数字地球测试', 'info');
            
            updateStatus('加载osgEarth完整数字地球测试模块...');
            addLog('开始加载osgEarth完整数字地球测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_osgearth_full.js';
            script.onload = function() {
                addLog('osgEarth完整数字地球测试脚本加载成功', 'success');
                updateStatus('osgEarth完整数字地球测试脚本已加载');
            };
            script.onerror = function() {
                addLog('osgEarth完整数字地球测试脚本加载失败', 'error');
                updateStatus('osgEarth完整数字地球测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
