# CMAKE generated file: DO NOT EDIT!
# Generated by "Ninja" Generator, CMake Version 3.26

# This file contains all the rules used to get the outputs files
# built from the input files.
# It is included in the main 'build.ninja'.

# =============================================================================
# Project: OSGEARTH
# Configurations: Debug
# =============================================================================
# =============================================================================

#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__test_xyz_fix_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__test_xyz_fix_Debug
  command = cmd.exe /C "$PRE_LINK && C:\dev\emsdk\upstream\emscripten\em++.bat $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__test_lod_logic_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__test_lod_logic_Debug
  command = cmd.exe /C "$PRE_LINK && C:\dev\emsdk\upstream\emscripten\em++.bat $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for running custom commands.

rule CUSTOM_COMMAND
  command = $COMMAND
  description = $DESC


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgEarthImGui_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgEarthImGui_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_bumpmap_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_bumpmap_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_cache_filesystem_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_cache_filesystem_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_colorramp_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_colorramp_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_detail_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_detail_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_earth_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_earth_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_engine_rex_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_engine_rex_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_featurefilter_intersect_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_featurefilter_intersect_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_featurefilter_join_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_featurefilter_join_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_kml_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_kml_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling C files.

rule C_COMPILER__osgdb_osgearth_scriptengine_javascript_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\emcc.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building C object $out


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_scriptengine_javascript_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_scriptengine_javascript_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_sky_gl_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_sky_gl_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_sky_simple_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_sky_simple_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_template_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_template_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_terrainshader_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_terrainshader_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_vdatum_egm2008_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_vdatum_egm2008_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_vdatum_egm84_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_vdatum_egm84_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_vdatum_egm96_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_vdatum_egm96_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_osgearth_viewpoints_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_osgearth_viewpoints_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgdb_gltf_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgdb_gltf_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS $in && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgearth_myviewer_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__osgearth_myviewer_Debug
  command = cmd.exe /C "$PRE_LINK && C:\dev\emsdk\upstream\emscripten\em++.bat $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgearth_myviewer_minimal_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX executable.

rule CXX_EXECUTABLE_LINKER__osgearth_myviewer_minimal_Debug
  command = cmd.exe /C "$PRE_LINK && C:\dev\emsdk\upstream\emscripten\em++.bat $FLAGS $LINK_FLAGS $in -o $TARGET_FILE $LINK_PATH $LINK_LIBRARIES && $POST_BUILD"
  description = Linking CXX executable $TARGET_FILE
  restat = $RESTAT


#############################################
# Rule for compiling CXX files.

rule CXX_COMPILER__osgEarth_unscanned_Debug
  depfile = $DEP_FILE
  deps = gcc
  command = C:\dev\emsdk\upstream\emscripten\em++.bat $DEFINES $INCLUDES $FLAGS -MD -MT $out -MF $DEP_FILE -o $out -c $in
  description = Building CXX object $out


#############################################
# Rule for linking CXX static library.

rule CXX_STATIC_LIBRARY_LINKER__osgEarth_Debug
  command = cmd.exe /C "$PRE_LINK && "C:\Program Files\CMake\bin\cmake.exe" -E rm -f $TARGET_FILE && C:\dev\emsdk\upstream\emscripten\emar.bat qc $TARGET_FILE $LINK_FLAGS @$RSP_FILE && C:\dev\emsdk\upstream\emscripten\emranlib.bat $TARGET_FILE && $POST_BUILD"
  description = Linking CXX static library $TARGET_FILE
  rspfile = $RSP_FILE
  rspfile_content = $in $LINK_PATH $LINK_LIBRARIES
  restat = $RESTAT


#############################################
# Rule for re-running cmake.

rule RERUN_CMAKE
  command = "C:\Program Files\CMake\bin\cmake.exe" --regenerate-during-build -SF:\cmo-dev\my_osgearth_web\osgearth_simple2\myosgearth -BF:\cmo-dev\my_osgearth_web\osgearth_simple2\myosgearth\build_wasm1
  description = Re-running CMake...
  generator = 1


#############################################
# Rule for cleaning all built files.

rule CLEAN
  command = C:\Windows\System32\ninja.exe $FILE_ARG -t clean $TARGETS
  description = Cleaning all built files...


#############################################
# Rule for printing all primary targets available.

rule HELP
  command = C:\Windows\System32\ninja.exe -t targets
  description = All primary targets available:

