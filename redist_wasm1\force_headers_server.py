#!/usr/bin/env python3
"""
强制设置SharedArrayBuffer HTTP头的服务器
"""

import http.server
import socketserver
import os
import sys

class ForceHeadersHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 强制设置所有必需的HTTP头
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 额外的安全头
        self.send_header('Cross-Origin-Allowlist-Policy', '*')
        
        # 强制禁用缓存
        self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
        self.send_header('Pragma', 'no-cache')
        self.send_header('Expires', '0')
        
        # 设置正确的MIME类型
        if self.path.endswith('.wasm'):
            self.send_header('Content-Type', 'application/wasm')
        elif self.path.endswith('.js'):
            self.send_header('Content-Type', 'application/javascript')
        elif self.path.endswith('.html'):
            self.send_header('Content-Type', 'text/html; charset=utf-8')
        
        super().end_headers()
    
    def log_message(self, format, *args):
        # 显示详细的请求信息
        print(f"[HTTP] {format % args}")
        if self.path.endswith(('.wasm', '.js', '.html')):
            print(f"  -> 文件: {self.path}")
            print(f"  -> COEP: require-corp")
            print(f"  -> COOP: same-origin")

def main():
    port = 8086
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效端口号: {sys.argv[1]}")
            sys.exit(1)
    
    print(f"🔒 强制HTTP头服务器")
    print(f"端口: {port}")
    print(f"目录: {os.getcwd()}")
    print(f"强制设置SharedArrayBuffer支持的HTTP头")
    print("-" * 50)
    
    # 检查文件
    wasm_files = [f for f in os.listdir('.') if f.endswith('.wasm')]
    js_files = [f for f in os.listdir('.') if f.endswith('.js') and not f.endswith('_server.py')]
    
    if wasm_files:
        print(f"WebAssembly文件: {len(wasm_files)} 个")
        for f in wasm_files[:3]:  # 只显示前3个
            print(f"  • {f}")
    
    print(f"测试地址:")
    print(f"  • std::thread测试: http://localhost:{port}/test_std_thread.html")
    print(f"  • osgEarth线程池: http://localhost:{port}/test_osgearth_jobs.html")
    print(f"按 Ctrl+C 停止服务器")
    print("-" * 50)
    
    try:
        with socketserver.TCPServer(("", port), ForceHeadersHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    main()
