<map name="MyViewer Custom Map" type="geocentric" version="2">

    <!-- Google Maps satellite imagery -->
    <XYZImage name="google_satellite">
        <url>http://mt1.google.com/vt/lyrs=s&amp;x={x}&amp;y={y}&amp;z={z}</url>
        <min_level>0</min_level> 
        <max_level>20</max_level>
        <min_range>0</min_range>
        <max_range>1e10</max_range>
        <lod_blending>true</lod_blending>
        <min_filter>LINEAR_MIPMAP_LINEAR</min_filter>
        <mag_filter>LINEAR</mag_filter>
        <tile_size>256</tile_size>
        <options>
            <user-agent>Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36</user-agent>
            <max_concurrent_requests>64</max_concurrent_requests>
            <timeout>30</timeout>
            <connect_timeout>10</connect_timeout>
            <cache_policy>cache_first</cache_policy>
            <cache_max_age>86400</cache_max_age>
        </options>
    </XYZImage>

    <!-- AWS Terrarium elevation -->
    <XYZElevation name="aws_terrarium">
        <url>https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png</url>
        <min_level>0</min_level>
        <max_level>18</max_level>
        <tile_size>256</tile_size>
        <elevation_encoding>terrarium</elevation_encoding>
        <stitch_edges>true</stitch_edges>
        <interpolation>bilinear</interpolation>
        <vdatum>egm96</vdatum>
        <options>
            <max_concurrent_requests>64</max_concurrent_requests>
            <timeout>30</timeout>
            <connect_timeout>10</connect_timeout>
        </options>
    </XYZElevation>

    <!-- China boundaries vector layer -->
    <feature_model name="china_boundaries" enabled="true">
        <OGRFeatures>
            <url>china_boundaries.geojson</url>
            <build_spatial_index>true</build_spatial_index>
        </OGRFeatures>
        
        <styles>
            <style type="text/css">
                default {
                    stroke: #ff0000;           /* Red boundary lines */
                    stroke-width: 3px;         /* Line width */
                    stroke-opacity: 0.9;       /* Opacity */
                    fill: none;                /* No fill */
                    altitude-clamping: terrain; /* Clamp to terrain */
                }
            </style>
        </styles>
        
        <lighting>false</lighting>
        <render_order>2</render_order>
    </feature_model>

    <!-- Geodetic graticule -->
    <GeodeticGraticule name="Geodetic" open="true">
        <color>#ffff007f</color>  <!-- Yellow, semi-transparent -->
        <line_width>2.0</line_width>
        <grid_lines>10</grid_lines>
        <resolutions>10 5 2.5 1.0 0.5</resolutions>
        <grid_lines_visible>true</grid_lines_visible>
        <grid_labels_visible>true</grid_labels_visible>
        <edge_labels_visible>true</edge_labels_visible>
        <text>
            <color>#ffff00ff</color>  <!-- Yellow text -->
            <size>16</size>
        </text>
    </GeodeticGraticule>

    <!-- Rex terrain engine options -->
    <options>
        <terrain>
            <concurrency>64</concurrency>
            <normalize_edges>true</normalize_edges>
            <vertical_scale>2.0</vertical_scale>
            <merges_per_frame>32</merges_per_frame>
        </terrain>
    </options>

    <!-- Sky effects -->
    <sky driver="simple">
        <atmospheric_lighting>true</atmospheric_lighting>
        <exposure>4.0</exposure>
        <daytime_ambient_boost>0.2</daytime_ambient_boost>
        <max_ambient_intensity>0.6</max_ambient_intensity>
        <sun_visible>true</sun_visible>
        <moon_visible>true</moon_visible>
        <stars_visible>true</stars_visible>
        <atmosphere_visible>true</atmosphere_visible>
        <quality>high</quality>
        <hours>12.0</hours>
        <ambient>0.05</ambient>
        <pbr>true</pbr>
    </sky>

</map>
