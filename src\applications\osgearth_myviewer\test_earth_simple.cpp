/**
 * 简化的数字地球测试程序
 * 不依赖完整的osgEarth库，模拟数字地球的核心功能
 */

#include <iostream>
#include <cmath>
#include <vector>
#include <string>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#include <GLES2/gl2.h>
#else
#include <SDL.h>
#include <SDL_opengl.h>
#endif

// 简化的地球数据结构
struct EarthPoint
{
    double longitude;
    double latitude;
    double elevation;
};

struct EarthTile
{
    int x, y, z; // 瓦片坐标
    std::string url;
    bool loaded;
};

// 全局变量
static SDL_Window *g_window = nullptr;
static SDL_GLContext g_gl_context = nullptr;
static bool g_running = true;
static float g_rotation = 0.0f;
static double g_zoom = 1.0;
static double g_center_lon = 116.3; // 北京经度
static double g_center_lat = 39.9;  // 北京纬度

// 简化的地球渲染器
class SimpleEarthRenderer
{
private:
    std::vector<EarthTile> tiles;

public:
    SimpleEarthRenderer()
    {
        std::cout << "[earth] 初始化简化地球渲染器" << std::endl;

        // 创建一些模拟的瓦片
        for (int z = 0; z < 3; ++z)
        {
            for (int x = 0; x < (1 << z); ++x)
            {
                for (int y = 0; y < (1 << z); ++y)
                {
                    EarthTile tile;
                    tile.x = x;
                    tile.y = y;
                    tile.z = z;
                    tile.url = "https://mt1.google.com/vt/lyrs=s&x=" + std::to_string(x) +
                               "&y=" + std::to_string(y) + "&z=" + std::to_string(z);
                    tile.loaded = false;
                    tiles.push_back(tile);
                }
            }
        }

        std::cout << "[earth] 创建了 " << tiles.size() << " 个瓦片" << std::endl;
    }

    void render()
    {
        // 清除屏幕（深蓝色，模拟太空）
        glClearColor(0.05f, 0.1f, 0.2f, 1.0f);
        glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);

        // 启用深度测试
        glEnable(GL_DEPTH_TEST);

        // 设置投影矩阵
        glMatrixMode(GL_PROJECTION);
        glLoadIdentity();

        // 设置透视投影（手动实现，因为WebAssembly可能没有gluPerspective）
        float aspect = 800.0f / 600.0f;
        float fovy = 45.0f * M_PI / 180.0f;
        float f = 1.0f / tan(fovy / 2.0f);
        float near_plane = 0.1f;
        float far_plane = 100.0f;

        float projection[16] = {
            f / aspect, 0.0f, 0.0f, 0.0f,
            0.0f, f, 0.0f, 0.0f,
            0.0f, 0.0f, (far_plane + near_plane) / (near_plane - far_plane), -1.0f,
            0.0f, 0.0f, (2.0f * far_plane * near_plane) / (near_plane - far_plane), 0.0f};
        glLoadMatrixf(projection);

        // 设置模型视图矩阵
        glMatrixMode(GL_MODELVIEW);
        glLoadIdentity();

        // 手动设置相机变换（替代gluLookAt）
        glTranslatef(0.0f, 0.0f, -3.0f);

        // 应用旋转
        glRotatef(g_rotation, 0.0f, 1.0f, 0.0f);

        // 渲染地球
        renderEarth();

        // 渲染瓦片网格
        renderTileGrid();

        // 渲染坐标轴
        renderAxes();
    }

private:
    void renderEarth()
    {
        // 渲染一个简化的3D地球球体
        std::cout << "[render] 渲染地球球体..." << std::endl;

        // 设置地球材质（蓝绿色）
        glColor3f(0.2f, 0.6f, 0.8f);

        // 渲染球体（使用简化的方法）
        renderSphere(1.0f, 32, 16);

        // 渲染一些大陆轮廓（简化）
        renderContinents();
    }

    void renderSphere(float radius, int slices, int stacks)
    {
        // 使用三角形条带渲染球体
        for (int i = 0; i < stacks; ++i)
        {
            float lat0 = M_PI * (-0.5f + (float)i / stacks);
            float z0 = sin(lat0);
            float zr0 = cos(lat0);

            float lat1 = M_PI * (-0.5f + (float)(i + 1) / stacks);
            float z1 = sin(lat1);
            float zr1 = cos(lat1);

            glBegin(GL_TRIANGLE_STRIP);
            for (int j = 0; j <= slices; ++j)
            {
                float lng = 2 * M_PI * (float)j / slices;
                float x = cos(lng);
                float y = sin(lng);

                glNormal3f(x * zr0, y * zr0, z0);
                glVertex3f(radius * x * zr0, radius * y * zr0, radius * z0);

                glNormal3f(x * zr1, y * zr1, z1);
                glVertex3f(radius * x * zr1, radius * y * zr1, radius * z1);
            }
            glEnd();
        }
    }

    void renderContinents()
    {
        // 渲染简化的大陆轮廓
        glColor3f(0.0f, 0.8f, 0.2f); // 绿色陆地

        // 简化的亚洲轮廓
        glBegin(GL_TRIANGLES);
        glVertex3f(0.3f, 0.2f, 0.9f);
        glVertex3f(0.5f, 0.4f, 0.8f);
        glVertex3f(0.2f, 0.6f, 0.7f);
        glEnd();

        // 简化的欧洲轮廓
        glBegin(GL_TRIANGLES);
        glVertex3f(-0.2f, 0.3f, 0.9f);
        glVertex3f(-0.1f, 0.5f, 0.8f);
        glVertex3f(-0.4f, 0.4f, 0.8f);
        glEnd();
    }

    void renderTileGrid()
    {
        // 渲染瓦片网格线
        glColor3f(0.5f, 0.5f, 0.5f);
        glLineWidth(1.0f);

        // 经线
        for (int lng = -180; lng <= 180; lng += 30)
        {
            glBegin(GL_LINE_STRIP);
            for (int lat = -90; lat <= 90; lat += 10)
            {
                float x = cos(lat * M_PI / 180.0f) * cos(lng * M_PI / 180.0f);
                float y = cos(lat * M_PI / 180.0f) * sin(lng * M_PI / 180.0f);
                float z = sin(lat * M_PI / 180.0f);
                glVertex3f(1.01f * x, 1.01f * y, 1.01f * z);
            }
            glEnd();
        }

        // 纬线
        for (int lat = -90; lat <= 90; lat += 30)
        {
            glBegin(GL_LINE_STRIP);
            for (int lng = -180; lng <= 180; lng += 10)
            {
                float x = cos(lat * M_PI / 180.0f) * cos(lng * M_PI / 180.0f);
                float y = cos(lat * M_PI / 180.0f) * sin(lng * M_PI / 180.0f);
                float z = sin(lat * M_PI / 180.0f);
                glVertex3f(1.01f * x, 1.01f * y, 1.01f * z);
            }
            glEnd();
        }
    }

    void renderAxes()
    {
        // 渲染坐标轴
        glLineWidth(3.0f);

        glBegin(GL_LINES);
        // X轴 - 红色
        glColor3f(1.0f, 0.0f, 0.0f);
        glVertex3f(0.0f, 0.0f, 0.0f);
        glVertex3f(1.5f, 0.0f, 0.0f);

        // Y轴 - 绿色
        glColor3f(0.0f, 1.0f, 0.0f);
        glVertex3f(0.0f, 0.0f, 0.0f);
        glVertex3f(0.0f, 1.5f, 0.0f);

        // Z轴 - 蓝色
        glColor3f(0.0f, 0.0f, 1.0f);
        glVertex3f(0.0f, 0.0f, 0.0f);
        glVertex3f(0.0f, 0.0f, 1.5f);
        glEnd();
    }
};

// 全局地球渲染器
static SimpleEarthRenderer *g_earth_renderer = nullptr;

// 处理事件
void handle_events()
{
    SDL_Event event;
    while (SDL_PollEvent(&event))
    {
        switch (event.type)
        {
        case SDL_QUIT:
            g_running = false;
            std::cout << "[event] 收到退出事件" << std::endl;
            break;

        case SDL_KEYDOWN:
            std::cout << "[event] 按键: " << SDL_GetKeyName(event.key.keysym.sym) << std::endl;
            if (event.key.keysym.sym == SDLK_ESCAPE)
            {
                g_running = false;
            }
            else if (event.key.keysym.sym == SDLK_PLUS || event.key.keysym.sym == SDLK_EQUALS)
            {
                g_zoom *= 1.2;
                std::cout << "[earth] 放大，缩放级别: " << g_zoom << std::endl;
            }
            else if (event.key.keysym.sym == SDLK_MINUS)
            {
                g_zoom /= 1.2;
                std::cout << "[earth] 缩小，缩放级别: " << g_zoom << std::endl;
            }
            break;

        case SDL_MOUSEBUTTONDOWN:
            std::cout << "[event] 鼠标点击: (" << event.button.x << ", " << event.button.y << ")" << std::endl;
            break;

        case SDL_MOUSEWHEEL:
            if (event.wheel.y > 0)
            {
                g_zoom *= 1.1;
                std::cout << "[earth] 滚轮放大，缩放级别: " << g_zoom << std::endl;
            }
            else if (event.wheel.y < 0)
            {
                g_zoom /= 1.1;
                std::cout << "[earth] 滚轮缩小，缩放级别: " << g_zoom << std::endl;
            }
            break;

        case SDL_MOUSEMOTION:
            if (event.motion.state & SDL_BUTTON_LMASK)
            {
                // 模拟地球拖拽
                g_center_lon += event.motion.xrel * 0.1 / g_zoom;
                g_center_lat -= event.motion.yrel * 0.1 / g_zoom;
                std::cout << "[earth] 拖拽到: (" << g_center_lon << ", " << g_center_lat << ")" << std::endl;
            }
            break;
        }
    }
}

// 主循环函数
void main_loop()
{
    if (!g_running)
    {
        return;
    }

    handle_events();

    if (g_earth_renderer)
    {
        g_earth_renderer->render();
    }

    // 更新旋转
    g_rotation += 0.5f;
    if (g_rotation >= 360.0f)
    {
        g_rotation = 0.0f;
    }

    SDL_GL_SwapWindow(g_window);
}

int main(int argc, char *argv[])
{
    std::cout << "🌍 简化数字地球测试程序" << std::endl;
    std::cout << "========================" << std::endl;

#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif

    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0)
    {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;

    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);

    // 创建窗口
    g_window = SDL_CreateWindow(
        "Simple Digital Earth",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN);

    if (!g_window)
    {
        std::cout << "[main] ❌ 窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 窗口创建成功: 800x600" << std::endl;

    // 创建OpenGL上下文
    g_gl_context = SDL_GL_CreateContext(g_window);
    if (!g_gl_context)
    {
        std::cout << "[main] ❌ OpenGL上下文创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(g_window);
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ OpenGL上下文创建成功" << std::endl;

    // 设置视口
    glViewport(0, 0, 800, 600);

    // 创建地球渲染器
    g_earth_renderer = new SimpleEarthRenderer();

    std::cout << "[main] ✅ 简化数字地球初始化完成" << std::endl;
    std::cout << "[main] 控制说明:" << std::endl;
    std::cout << "[main] - 鼠标拖拽: 移动地球" << std::endl;
    std::cout << "[main] - 滚轮: 缩放" << std::endl;
    std::cout << "[main] - +/-键: 缩放" << std::endl;
    std::cout << "[main] - ESC键: 退出" << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly主循环
    emscripten_set_main_loop(main_loop, 60, 1);
#else
    // 桌面主循环
    while (g_running)
    {
        main_loop();
        SDL_Delay(16);
    }
#endif

    // 清理资源
    delete g_earth_renderer;
    if (g_gl_context)
    {
        SDL_GL_DeleteContext(g_gl_context);
    }
    if (g_window)
    {
        SDL_DestroyWindow(g_window);
    }
    SDL_Quit();

    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
