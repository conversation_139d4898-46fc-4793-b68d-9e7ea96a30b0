/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Export>
#include <osgEarth/Notify>
#include <osgEarth/BuildConfig>
#include <osg/ref_ptr>
#include <osg/Referenced>
#include <memory>

namespace osg {
    class ArgumentParser;
}

#define OE_COMMENT(STR)

/** osgEarth core */
namespace osgEarth
{
    // application-wide unique ID.
    using UID = int;

    //! Global initializer for osgEarth - call this if you are setting up
    //! windows and views prior to using osgEarth
    extern OSGEARTH_EXPORT void initialize();

    extern OSGEARTH_EXPORT void initialize(osg::ArgumentParser&);

    //! Generate an application-wide unique identifier.
    extern OSGEARTH_EXPORT UID createUID();
}

#ifdef _MSC_VER
// VS ignores
#pragma warning (disable: 4224)
#pragma warning (disable: 4180)
#endif

// Sringification
#define OE_STRINGIFY_0(x) #x
#define OE_STRINGIFY(x) OE_STRINGIFY_0(x)

#if __cplusplus >= 201402L || _MSVC_LANG >= 201402L
    #define OE_DEPRECATED(MESSAGE) [[deprecated(MESSAGE)]]
#else
    #define OE_DEPRECATED(MESSAGE)
#endif

// MACRO for disabling the compiler warning for a single [[deprecated]] function call;
// used for internal polyfill only.
#ifdef _MSC_VER
    #define OE_START_DEPRECATED_FUNCTION_CALLS \
        __pragma(warning(push)) \
        __pragma(warning(disable:4996))
    #define OE_END_DEPRECATED_FUNCTION_CALLS \
        __pragma(warning(pop))
#elif defined(__GNUC__) || defined(__clang__)
    #define OE_START_DEPRECATED_FUNCTION_CALLS \
        _Pragma("GCC diagnostic push") \
        _Pragma("GCC diagnostic ignored \"-Wdeprecated-declarations\"")
    #define OE_END_DEPRECATED_FUNCTION_CALLS \
         _Pragma("GCC diagnostic pop")
#else
    #define OE_START_DEPRECATED_FUNCTION_CALLS
    #define OE_END_DEPRECATED_FUNCTION_CALLS
#endif

#define OE_CALL_DEPRECATED(FUNC) \
    OE_START_DEPRECATED_FUNCTION_CALLS \
    FUNC; \
    OE_END_DEPRECATED_FUNCTION_CALLS

