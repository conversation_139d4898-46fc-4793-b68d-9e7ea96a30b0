@echo off
setlocal enabledelayedexpansion

echo ========================================
echo osgEarth WebAssembly Build Script (build_wasm1)
echo ========================================
echo.

REM Check if we're in the correct directory
if not exist "CMakeLists.txt" (
    echo Error: No CMakeLists.txt found in current directory
    echo Please run this script from the osgEarth root directory
    pause
    exit /b 1
)

REM Setup Emscripten environment
set EMSDK_PATH=C:\dev\emsdk
if exist "%EMSDK_PATH%\emsdk_env.bat" (
    echo Activating Emscripten environment...
    call "%EMSDK_PATH%\emsdk_env.bat"

    REM Set additional environment variables
    set EMSDK=%EMSDK_PATH%
    set EM_CONFIG=%EMSDK_PATH%\.emscripten
    set EMSCRIPTEN=%EMSDK_PATH%\upstream\emscripten

    REM Fix STL version compatibility
    set CFLAGS=-D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH
    set CXXFLAGS=-D_ALLOW_COMPILER_AND_STL_VERSION_MISMATCH

    echo EMSDK: %EMSDK%
    echo EMSCRIPTEN: %EMSCRIPTEN%
) else (
    echo Error: Emscripten not found at: %EMSDK_PATH%
    echo Please install Emscripten first or run install_emscripten.bat
    pause
    exit /b 1
)

REM Create build directory
if not exist "build_wasm1" mkdir "build_wasm1"

REM Go to build directory
cd build_wasm1

echo.
echo ================================
echo Configuring CMake...
echo ================================

REM Configure the project
cmake .. -G "Ninja" ^
    -DCMAKE_TOOLCHAIN_FILE=../cmake/emscripten.cmake ^
    -DCMAKE_BUILD_TYPE=Release ^
    -DEMSCRIPTEN=ON ^
    -DOSGEARTH_WEBASSEMBLY=ON ^
    -DOSGEARTH_BUILD_EXAMPLES=ON ^
    -DOSGEARTH_BUILD_TOOLS=ON ^
    -DOSGEARTH_BUILD_TESTS=OFF ^
    -DOSGEARTH_BUILD_DOCS=OFF

if %errorlevel% neq 0 (
    echo Error: CMake configuration failed
    pause
    exit /b 1
)

echo.
echo ================================
echo Building...
echo ================================

ninja

if %errorlevel% neq 0 (
    echo Warning: Build had errors, but continuing...
    echo Checking what was built...

    REM Show built files
    echo.
    echo Built files:
    for /r %%f in (*.js *.wasm *.html) do (
        echo Found: %%f
    )
    echo.
)

echo.
echo ================================
echo Installing...
echo ================================

ninja install

echo.
echo ================================
echo Creating deployment package...
echo ================================

REM Create output directory
if not exist "..\redist_wasm1" mkdir "..\redist_wasm1"

REM Copy WebAssembly files
echo Copying WebAssembly artifacts...
for /r . %%f in (*.js) do copy "%%f" "..\redist_wasm1\" >nul 2>&1
for /r . %%f in (*.wasm) do copy "%%f" "..\redist_wasm1\" >nul 2>&1
for /r . %%f in (*.html) do copy "%%f" "..\redist_wasm1\" >nul 2>&1
for /r . %%f in (*.data) do copy "%%f" "..\redist_wasm1\" >nul 2>&1

REM Copy shell template
if exist "..\html\shell.html" copy "..\html\shell.html" "..\redist_wasm1\" >nul 2>&1

REM Copy configuration files
if exist "..\myviewer_config.earth" copy "..\myviewer_config.earth" "..\redist_wasm1\" >nul 2>&1
if exist "..\china_boundaries.geojson" copy "..\china_boundaries.geojson" "..\redist_wasm1\" >nul 2>&1

echo.
echo ================================
echo Build completed!
echo ================================
echo.
echo Output directory: redist_wasm1
echo.

REM Show final results
echo Files created:
dir "..\redist_wasm1" /b 2>nul

echo.
echo To test: cd redist_wasm1 and run a local HTTP server
echo Example: python -m http.server 8000
echo.

pause
