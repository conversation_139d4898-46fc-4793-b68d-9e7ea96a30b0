<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth WebAssembly 调试测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 12px;
        }
        .container {
            max-width: 1400px;
            margin: 0 auto;
        }
        #canvas {
            border: 2px solid #4a4a4a;
            background-color: #000000;
            display: block;
            margin: 20px auto;
        }
        .debug-panel {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error-panel {
            background-color: #4a1a1a;
            border-left: 4px solid #ff4444;
        }
        .success-panel {
            background-color: #1a4a1a;
            border-left: 4px solid #44ff44;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
            border-radius: 2px;
        }
        .log-info { background-color: rgba(0, 122, 204, 0.1); }
        .log-warn { background-color: rgba(255, 193, 7, 0.1); color: #ffc107; }
        .log-error { background-color: rgba(220, 53, 69, 0.1); color: #dc3545; }
        .log-debug { background-color: rgba(108, 117, 125, 0.1); color: #6c757d; }
        #console-output {
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 11px;
        }
        .stack-trace {
            background-color: #3a3a3a;
            padding: 10px;
            border-radius: 3px;
            margin: 5px 0;
            white-space: pre-wrap;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 osgEarth WebAssembly 调试测试</h1>
        
        <div id="status" class="debug-panel">状态: 初始化中...</div>
        
        <canvas id="canvas" width="800" height="600"></canvas>
        
        <div class="debug-panel">
            <h3>📊 实时调试信息</h3>
            <div id="debug-info">等待WebAssembly模块加载...</div>
        </div>
        
        <div class="debug-panel">
            <h3>📝 控制台日志</h3>
            <div id="console-output"></div>
        </div>
        
        <div id="error-details" class="error-panel" style="display: none;">
            <h3>❌ 错误详情</h3>
            <div id="error-content"></div>
        </div>
    </div>

    <script>
        // 全局调试状态
        let debugInfo = {
            startTime: Date.now(),
            moduleLoaded: false,
            wasmInitialized: false,
            errors: [],
            warnings: [],
            lastError: null
        };

        // 获取Canvas并配置WebGL上下文
        function setupCanvas() {
            const canvas = document.getElementById('canvas');
            
            // 尝试获取WebGL2上下文
            let gl = canvas.getContext('webgl2', {
                alpha: false,
                depth: true,
                stencil: true,
                antialias: true,
                premultipliedAlpha: false,
                preserveDrawingBuffer: false,
                powerPreference: "high-performance",
                failIfMajorPerformanceCaveat: false
            });
            
            if (!gl) {
                // 回退到WebGL1
                gl = canvas.getContext('webgl', {
                    alpha: false,
                    depth: true,
                    stencil: true,
                    antialias: true,
                    premultipliedAlpha: false,
                    preserveDrawingBuffer: false,
                    powerPreference: "high-performance",
                    failIfMajorPerformanceCaveat: false
                });
            }
            
            if (!gl) {
                logMessage('错误: 无法创建WebGL上下文', 'error');
                updateStatus('错误: 浏览器不支持WebGL', true);
                return null;
            }
            
            const glVersion = gl instanceof WebGL2RenderingContext ? 'WebGL2' : 'WebGL1';
            logMessage(`WebGL上下文创建成功: ${glVersion}`, 'info');
            logMessage(`GL版本: ${gl.getParameter(gl.VERSION)}`, 'debug');
            logMessage(`GL渲染器: ${gl.getParameter(gl.RENDERER)}`, 'debug');
            logMessage(`GL供应商: ${gl.getParameter(gl.VENDOR)}`, 'debug');
            
            return canvas;
        }

        // Module配置 - 增强调试功能
        var Module = {
            canvas: null,
            
            print: function(text) {
                console.log('osgEarth:', text);
                logMessage(`[INFO] ${text}`, 'info');
                updateDebugInfo('lastMessage', text);
            },
            
            printErr: function(text) {
                console.error('osgEarth Error:', text);
                logMessage(`[ERROR] ${text}`, 'error');
                debugInfo.errors.push({
                    time: Date.now(),
                    message: text,
                    stack: new Error().stack
                });
                debugInfo.lastError = text;
                updateDebugInfo('lastError', text);
                showErrorDetails(text);
            },
            
            onRuntimeInitialized: function() {
                console.log('WebAssembly模块运行时初始化完成');
                logMessage('[SYSTEM] WebAssembly运行时初始化完成', 'info');
                debugInfo.wasmInitialized = true;
                updateStatus('WebAssembly运行时就绪', false, true);
                updateDebugInfo('wasmInitialized', true);
            },
            
            onAbort: function(what) {
                console.error('WebAssembly模块中止:', what);
                logMessage(`[ABORT] 模块中止: ${what}`, 'error');
                debugInfo.errors.push({
                    time: Date.now(),
                    message: `模块中止: ${what}`,
                    stack: new Error().stack
                });
                updateStatus(`模块中止: ${what}`, true);
                showErrorDetails(`模块中止: ${what}`);
                
                // 尝试获取更多调试信息
                if (Module.stackTrace) {
                    logMessage(`[STACK] ${Module.stackTrace()}`, 'error');
                }
            },
            
            // 添加更多调试回调
            onExit: function(status) {
                logMessage(`[EXIT] 程序退出，状态码: ${status}`, status === 0 ? 'info' : 'error');
            },
            
            preRun: [],
            postRun: [],
            
            // 内存和性能监控
            monitorRunDependencies: function(left) {
                logMessage(`[DEPS] 剩余依赖: ${left}`, 'debug');
                if (left === 0) {
                    logMessage('[DEPS] 所有依赖加载完成', 'info');
                }
            }
        };
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'debug-panel';
            if (isError) statusEl.className += ' error-panel';
            if (isSuccess) statusEl.className += ' success-panel';
        }
        
        function logMessage(text, level = 'info') {
            const outputEl = document.getElementById('console-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">${timestamp}</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateDebugInfo(key, value) {
            debugInfo[key] = value;
            const debugEl = document.getElementById('debug-info');
            const runtime = ((Date.now() - debugInfo.startTime) / 1000).toFixed(1);
            debugEl.innerHTML = `
                <strong>运行时间:</strong> ${runtime}s<br>
                <strong>模块加载:</strong> ${debugInfo.moduleLoaded ? '✅' : '❌'}<br>
                <strong>WASM初始化:</strong> ${debugInfo.wasmInitialized ? '✅' : '❌'}<br>
                <strong>错误数量:</strong> ${debugInfo.errors.length}<br>
                <strong>最后消息:</strong> ${debugInfo.lastMessage || 'N/A'}<br>
                <strong>最后错误:</strong> ${debugInfo.lastError || 'N/A'}
            `;
        }
        
        function showErrorDetails(error) {
            const errorEl = document.getElementById('error-details');
            const contentEl = document.getElementById('error-content');
            
            let details = `<strong>错误信息:</strong> ${error}<br>`;
            details += `<strong>时间:</strong> ${new Date().toLocaleString()}<br>`;
            
            if (debugInfo.errors.length > 0) {
                details += `<strong>错误历史:</strong><br>`;
                debugInfo.errors.slice(-5).forEach((err, i) => {
                    details += `${i + 1}. ${new Date(err.time).toLocaleTimeString()} - ${err.message}<br>`;
                });
            }
            
            contentEl.innerHTML = details;
            errorEl.style.display = 'block';
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            logMessage('[SYSTEM] 页面加载完成，开始初始化', 'info');
            updateStatus('初始化WebGL上下文...');
            
            // 设置Canvas和WebGL上下文
            const canvas = setupCanvas();
            if (!canvas) {
                return;
            }
            
            Module.canvas = canvas;
            updateDebugInfo('canvasReady', true);
            
            updateStatus('加载WebAssembly模块...');
            logMessage('[SYSTEM] 开始加载WebAssembly模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本 (调试版本)
            const script = document.createElement('script');
            script.src = 'osgearth_myviewer.js';
            script.onload = function() {
                logMessage('[SYSTEM] WebAssembly脚本加载成功', 'info');
                debugInfo.moduleLoaded = true;
                updateDebugInfo('moduleLoaded', true);
                updateStatus('WebAssembly脚本已加载，等待初始化...');
            };
            script.onerror = function() {
                logMessage('[SYSTEM] WebAssembly脚本加载失败', 'error');
                updateStatus('WebAssembly脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            logMessage(`[GLOBAL ERROR] ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            logMessage(`[UNHANDLED PROMISE] ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
