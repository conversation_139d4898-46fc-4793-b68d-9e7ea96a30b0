
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags:  
      
      The output was:
      1
      em++: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.js"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.wasm"
      
      The CXX compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.js
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.wasm
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags:  
      
      The output was:
      1
      emcc: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.js"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.wasm"
      
      The C compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.js
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.wasm
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_fae9c && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE  -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I    -v -MD -MT CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_fae9c.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_fae9c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_fae9c 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_fae9c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:49 (try_compile)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Check for working CXX compiler: C:/dev/emsdk/upstream/emscripten/em++.bat"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_70776 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/GLES2 -IC:/dev/emsdk/upstream/emscripten/system/include/GLES3 -IC:/dev/vcpkg/installed/wasm32-emscripten/include -IC:/dev/vcpkg/installed/wasm32-emscripten/include/geos -IC:/dev/vcpkg/installed/wasm32-emscripten/include/GeographicLib -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd/cmake -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -MD -MT CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -MF CMakeFiles\\cmTC_70776.dir\\testCXXCompiler.cxx.obj.d -o CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd/testCXXCompiler.cxx
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MT CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MF CMakeFiles\\cmTC_70776.dir\\testCXXCompiler.cxx.obj.d' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -o cmTC_70776 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_70776 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -o cmTC_70776 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...
