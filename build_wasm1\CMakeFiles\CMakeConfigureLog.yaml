
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags:  
      
      The output was:
      1
      em++: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.js"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.wasm"
      
      The CXX compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.js
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.wasm
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags:  
      
      The output was:
      1
      emcc: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.js"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.wasm"
      
      The C compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.js
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.wasm
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o85t85
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_fae9c && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE  -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I    -v -MD -MT CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_fae9c.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_fae9c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_fae9c 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_fae9c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_fae9c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:49 (try_compile)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Check for working CXX compiler: C:/dev/emsdk/upstream/emscripten/em++.bat"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_70776 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/GLES2 -IC:/dev/emsdk/upstream/emscripten/system/include/GLES3 -IC:/dev/vcpkg/installed/wasm32-emscripten/include -IC:/dev/vcpkg/installed/wasm32-emscripten/include/geos -IC:/dev/vcpkg/installed/wasm32-emscripten/include/GeographicLib -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd/cmake -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -MD -MT CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -MF CMakeFiles\\cmTC_70776.dir\\testCXXCompiler.cxx.obj.d -o CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-9oj6bd/testCXXCompiler.cxx
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MT CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MF CMakeFiles\\cmTC_70776.dir\\testCXXCompiler.cxx.obj.d' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -o cmTC_70776 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_70776 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_70776.dir/testCXXCompiler.cxx.obj -o cmTC_70776 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags:  
      
      The output was:
      1
      em++: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.js"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.wasm"
      
      The CXX compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.js
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.wasm
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags:  
      
      The output was:
      1
      emcc: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.js"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.wasm"
      
      The C compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.js
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.wasm
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-gihmd4"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-gihmd4"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-gihmd4
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_a3ce5 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE  -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I    -v -MD -MT CMakeFiles/cmTC_a3ce5.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_a3ce5.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_a3ce5.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_a3ce5.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_a3ce5 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_a3ce5 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_a3ce5.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_a3ce5 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:49 (try_compile)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Check for working CXX compiler: C:/dev/emsdk/upstream/emscripten/em++.bat"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-e7fq65"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-e7fq65"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-e7fq65
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_85a84 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/GLES2 -IC:/dev/emsdk/upstream/emscripten/system/include/GLES3 -IC:/dev/vcpkg/installed/wasm32-emscripten/include -IC:/dev/vcpkg/installed/wasm32-emscripten/include/geos -IC:/dev/vcpkg/installed/wasm32-emscripten/include/GeographicLib -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-e7fq65/cmake -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -MD -MT CMakeFiles/cmTC_85a84.dir/testCXXCompiler.cxx.obj -MF CMakeFiles\\cmTC_85a84.dir\\testCXXCompiler.cxx.obj.d -o CMakeFiles/cmTC_85a84.dir/testCXXCompiler.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-e7fq65/testCXXCompiler.cxx
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MT CMakeFiles/cmTC_85a84.dir/testCXXCompiler.cxx.obj' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MF CMakeFiles\\cmTC_85a84.dir\\testCXXCompiler.cxx.obj.d' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_85a84.dir/testCXXCompiler.cxx.obj -o cmTC_85a84 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_85a84 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_85a84.dir/testCXXCompiler.cxx.obj -o cmTC_85a84 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags:  
      
      The output was:
      1
      em++: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/em++.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-std=c++17;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-Wno-inconsistent-missing-override;-Wno-reinterpret-base-class;-Wno-overloaded-virtual;-Wno-unused-variable;-Wno-unused-function;-Wno-unused-local-typedef;-Wno-unused-const-variable;-Wno-missing-field-initializers;-fno-exceptions;-frtti;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-include;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.js"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.out.wasm"
      
      The CXX compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.js
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.out.wasm
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" failed.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags:  
      
      The output was:
      1
      emcc: error: no input files
      
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/dev/emsdk/upstream/emscripten/emcc.bat 
      Build flags: -O3;-s;USE_SDL=2;-s;USE_WEBGL2=1;-s;FULL_ES3=1;-D__EMSCRIPTEN__;-Wno-deprecated-declarations;-nostdinc++;-IC:/dev/emsdk/upstream/emscripten/system/include;-IC:/dev/emsdk/upstream/emscripten/system/include/libcxx;-IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake;-I
      Id flags: -c 
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.js"
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.out.wasm"
      
      The C compiler identification could not be found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.js
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.out.wasm
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-s0blqn"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-s0blqn"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-s0blqn
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_4621c && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE  -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I    -v -MD -MT CMakeFiles/cmTC_4621c.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_4621c.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_4621c.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_4621c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_4621c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_4621c 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html -v CMakeFiles/cmTC_4621c.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_4621c -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:49 (try_compile)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Check for working CXX compiler: C:/dev/emsdk/upstream/emscripten/em++.bat"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-biy8jl"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-biy8jl"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "-O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I"
      CMAKE_CXX_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html"
    buildResult:
      variable: "CMAKE_CXX_COMPILER_WORKS"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-biy8jl
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_de3f9 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -DEMSCRIPTEN -DOSGEARTH_DISABLE_FASTDXT -DOSGEARTH_NO_GDAL -DOSGEARTH_NO_PROJ -DOSGEARTH_NO_THREADING -DOSGEARTH_USE_WEBASSEMBLY_HTTP -DOSGEARTH_WEBASSEMBLY -DOSG_GLES2_AVAILABLE -DOSG_GLES3_AVAILABLE -DOSG_GL_MATRICES_AVAILABLE -DUSE_SDL2 -DUSE_WEBGL -DUSE_WEBGL_COMPATIBILITY -DWEBGL_AVAILABLE -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/GLES2 -IC:/dev/emsdk/upstream/emscripten/system/include/GLES3 -IC:/dev/vcpkg/installed/wasm32-emscripten/include -IC:/dev/vcpkg/installed/wasm32-emscripten/include/geos -IC:/dev/vcpkg/installed/wasm32-emscripten/include/GeographicLib -IF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/include -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-biy8jl/cmake -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -MD -MT CMakeFiles/cmTC_de3f9.dir/testCXXCompiler.cxx.obj -MF CMakeFiles\\cmTC_de3f9.dir\\testCXXCompiler.cxx.obj.d -o CMakeFiles/cmTC_de3f9.dir/testCXXCompiler.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-biy8jl/testCXXCompiler.cxx
        em++: warning: linker setting ignored during compilation: 'USE_WEBGL2' [-Wunused-command-line-argument]
        em++: warning: linker setting ignored during compilation: 'FULL_ES3' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MT CMakeFiles/cmTC_de3f9.dir/testCXXCompiler.cxx.obj' [-Wunused-command-line-argument]
        clang++: warning: argument unused during compilation: '-MF CMakeFiles\\cmTC_de3f9.dir\\testCXXCompiler.cxx.obj.d' [-Wunused-command-line-argument]
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_de3f9.dir/testCXXCompiler.cxx.obj -o cmTC_de3f9 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        FAILED: cmTC_de3f9 
        cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\em++.bat -O3 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -Wno-inconsistent-missing-override -Wno-reinterpret-base-class -Wno-overloaded-virtual -Wno-unused-variable -Wno-unused-function -Wno-unused-local-typedef -Wno-unused-const-variable -Wno-missing-field-initializers -fno-exceptions -frtti -nostdinc++ -IC:/dev/emsdk/upstream/emscripten/system/include -IC:/dev/emsdk/upstream/emscripten/system/include/libcxx -IF:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake -include F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake/webgl_compatibility.h -I -s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/html/shell.html CMakeFiles/cmTC_de3f9.dir/testCXXCompiler.cxx.obj -o cmTC_de3f9 -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib   -LC:/dev/vcpkg/installed/wasm32-emscripten/lib   -LF:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep/lib/osgPlugins-3.7.0  && cd ."
        em++: error: invalid command line setting `-sDEMANGLE_SUPPORT=1`: No longer supported
        ninja: build stopped: subcommand failed.
        
      exitCode: 1
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:204 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The system is: Windows - 10.0.26058 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang++.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "a.exe"
      
      The CXX compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdCXX/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Detecting CXX compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Compiling the C compiler identification source file "CMakeCCompilerId.c" succeeded.
      Compiler: C:/Program Files/LLVM/bin/clang.exe 
      Build flags: 
      Id flags:  
      
      The output was:
      0
      
      
      Compilation of the C compiler identification source "CMakeCCompilerId.c" produced "a.exe"
      
      The C compiler identification is Clang, found in:
        F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/3.26.4/CompilerIdC/a.exe
      
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:1153 (message)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerId.cmake:221 (CMAKE_DETERMINE_MSVC_SHOWINCLUDES_PREFIX)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCCompiler.cmake:123 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:13 (project)"
    message: |
      Detecting C compiler /showIncludes prefix:
        
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-daxnmp"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-daxnmp"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-daxnmp
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_e5fbf && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_e5fbf.dir/CMakeCXXCompilerABI.cpp.obj -MF CMakeFiles\\cmTC_e5fbf.dir\\CMakeCXXCompilerABI.cpp.obj.d -o CMakeFiles/cmTC_e5fbf.dir/CMakeCXXCompilerABI.cpp.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCXXCompilerABI.cpp"
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_e5fbf.dir/CMakeCXXCompilerABI.cpp.obj -o cmTC_e5fbf.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_e5fbf.lib -Xlinker /pdb:cmTC_e5fbf.pdb -Xlinker /version:0.0     && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake:121 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o99ilo"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o99ilo"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o99ilo
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_88fa3 && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_88fa3.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_88fa3.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_88fa3.dir/feature_tests.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-o99ilo/feature_tests.cxx
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_88fa3.dir/feature_tests.cxx.obj -o cmTC_88fa3.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_88fa3.lib -Xlinker /pdb:cmTC_88fa3.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:34 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/FeatureTesting.cmake:83 (_record_compiler_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Compiler/CMakeCommonCompilerMacros.cmake:129 (_record_compiler_features_cxx)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompileFeatures.cmake:70 (cmake_record_cxx_compile_features)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCXXCompiler.cmake:70 (CMAKE_DETERMINE_COMPILE_FEATURES)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting CXX compile features"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-0r1d7j"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-0r1d7j"
    cmakeVariables:
      CMAKE_CXX_FLAGS: ""
      CMAKE_CXX_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_CXX_FEATURE_TEST"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-0r1d7j
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_d60d8 && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang++.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd   -std=c++14 -MD -MT CMakeFiles/cmTC_d60d8.dir/feature_tests.cxx.obj -MF CMakeFiles\\cmTC_d60d8.dir\\feature_tests.cxx.obj.d -o CMakeFiles/cmTC_d60d8.dir/feature_tests.cxx.obj -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-0r1d7j/feature_tests.cxx
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang++.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_d60d8.dir/feature_tests.cxx.obj -o cmTC_d60d8.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_d60d8.lib -Xlinker /pdb:cmTC_d60d8.pdb -Xlinker /version:0.0   -lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames  && cd ."
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineCompilerABI.cmake:57 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeTestCCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:13 (project)"
    checks:
      - "Detecting C compiler ABI info"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-afw1po"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-afw1po"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-O0 -g -Xclang -gcodeview"
      CMAKE_EXE_LINKER_FLAGS: ""
    buildResult:
      variable: "CMAKE_C_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-afw1po
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_159b1 && [1/2] C:\\PROGRA~1\\LLVM\\bin\\clang.exe   -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -MD -MT CMakeFiles/cmTC_159b1.dir/CMakeCCompilerABI.c.obj -MF CMakeFiles\\cmTC_159b1.dir\\CMakeCCompilerABI.c.obj.d -o CMakeFiles/cmTC_159b1.dir/CMakeCCompilerABI.c.obj -c "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeCCompilerABI.c"
        [2/2] cmd.exe /C "cd . && C:\\PROGRA~1\\LLVM\\bin\\clang.exe -fuse-ld=lld-link -nostartfiles -nostdlib -O0 -g -Xclang -gcodeview -D_DEBUG -D_DLL -D_MT -Xclang --dependent-lib=msvcrtd -Xlinker /subsystem:console CMakeFiles/cmTC_159b1.dir/CMakeCCompilerABI.c.obj -o cmTC_159b1.exe -Xlinker /MANIFEST:EMBED -Xlinker /implib:cmTC_159b1.lib -Xlinker /pdb:cmTC_159b1.pdb -Xlinker /version:0.0     && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:85 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-rw6mqz"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-rw6mqz"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-rw6mqz
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_9c68c && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_9c68c.dir/src.c.o -MF CMakeFiles\\cmTC_9c68c.dir\\src.c.o.d -o CMakeFiles/cmTC_9c68c.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-rw6mqz/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_9c68c.dir/src.c.o -o cmTC_9c68c.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:101 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-c5mz2d"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-c5mz2d"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-c5mz2d
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_bf3a1 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_bf3a1.dir/src.c.o -MF CMakeFiles\\cmTC_bf3a1.dir\\src.c.o.d -o CMakeFiles/cmTC_bf3a1.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-c5mz2d/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_bf3a1.dir/src.c.o -o cmTC_bf3a1.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:101 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-sahq95"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-sahq95"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-sahq95
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_1ba18 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_1ba18.dir/src.c.o -MF CMakeFiles\\cmTC_1ba18.dir\\src.c.o.d -o CMakeFiles/cmTC_1ba18.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-sahq95/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_1ba18.dir/src.c.o -o cmTC_1ba18.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:101 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3qapb2"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3qapb2"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3qapb2
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_65091 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_65091.dir/src.c.o -MF CMakeFiles\\cmTC_65091.dir\\src.c.o.d -o CMakeFiles/cmTC_65091.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3qapb2/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_65091.dir/src.c.o -o cmTC_65091.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:101 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-ff5v0r"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-ff5v0r"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-ff5v0r
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_88734 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_88734.dir/src.c.o -MF CMakeFiles\\cmTC_88734.dir\\src.c.o.d -o CMakeFiles/cmTC_88734.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-ff5v0r/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_88734.dir/src.c.o -o cmTC_88734.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:101 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3zizfg"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3zizfg"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3zizfg
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_e8515 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_e8515.dir/src.c.o -MF CMakeFiles\\cmTC_e8515.dir\\src.c.o.d -o CMakeFiles/cmTC_e8515.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/CMakeScratch/TryCompile-3zizfg/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_e8515.dir/src.c.o -o cmTC_e8515.js   && cd ."
        
      exitCode: 0
...
