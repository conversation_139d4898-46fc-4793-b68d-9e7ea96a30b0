/**
 * OSG球体测试程序
 * 测试WebAssembly环境下OSG库的基础功能
 */

#include <iostream>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Material>
#include <osg/StateSet>
#include <osg/Texture2D>
#include <osg/ShapeDrawable>
#include <osg/PositionAttitudeTransform>
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgViewer/ViewerEventHandlers>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

// 全局变量
static osgViewer::Viewer* g_viewer = nullptr;
static osg::ref_ptr<osg::PositionAttitudeTransform> g_sphere_transform = nullptr;
static bool g_running = true;
static double g_rotation_speed = 1.0;

// 创建球体几何体
osg::ref_ptr<osg::Node> createSphere() {
    std::cout << "[osg] 创建球体几何体..." << std::endl;
    
    // 创建球体形状
    osg::ref_ptr<osg::Sphere> sphere = new osg::Sphere(osg::Vec3(0.0f, 0.0f, 0.0f), 1.0f);
    
    // 创建球体绘制对象
    osg::ref_ptr<osg::ShapeDrawable> sphereDrawable = new osg::ShapeDrawable(sphere);
    
    // 设置球体颜色（蓝绿色，模拟地球）
    sphereDrawable->setColor(osg::Vec4(0.2f, 0.6f, 0.8f, 1.0f));
    
    // 创建几何节点
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    geode->addDrawable(sphereDrawable);
    
    // 设置材质
    osg::ref_ptr<osg::Material> material = new osg::Material();
    material->setDiffuse(osg::Material::FRONT_AND_BACK, osg::Vec4(0.2f, 0.6f, 0.8f, 1.0f));
    material->setSpecular(osg::Material::FRONT_AND_BACK, osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));
    material->setShininess(osg::Material::FRONT_AND_BACK, 64.0f);
    
    osg::ref_ptr<osg::StateSet> stateSet = geode->getOrCreateStateSet();
    stateSet->setAttributeAndModes(material, osg::StateAttribute::ON);
    
    // 启用光照
    stateSet->setMode(GL_LIGHTING, osg::StateAttribute::ON);
    
    std::cout << "[osg] ✅ 球体几何体创建成功" << std::endl;
    return geode;
}

// 创建坐标轴
osg::ref_ptr<osg::Node> createAxes() {
    std::cout << "[osg] 创建坐标轴..." << std::endl;
    
    osg::ref_ptr<osg::Geode> geode = new osg::Geode();
    
    // X轴 - 红色
    osg::ref_ptr<osg::Geometry> xAxis = new osg::Geometry();
    osg::ref_ptr<osg::Vec3Array> xVertices = new osg::Vec3Array();
    xVertices->push_back(osg::Vec3(0.0f, 0.0f, 0.0f));
    xVertices->push_back(osg::Vec3(2.0f, 0.0f, 0.0f));
    xAxis->setVertexArray(xVertices);
    
    osg::ref_ptr<osg::Vec4Array> xColors = new osg::Vec4Array();
    xColors->push_back(osg::Vec4(1.0f, 0.0f, 0.0f, 1.0f));
    xAxis->setColorArray(xColors);
    xAxis->setColorBinding(osg::Geometry::BIND_OVERALL);
    
    xAxis->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINES, 0, 2));
    geode->addDrawable(xAxis);
    
    // Y轴 - 绿色
    osg::ref_ptr<osg::Geometry> yAxis = new osg::Geometry();
    osg::ref_ptr<osg::Vec3Array> yVertices = new osg::Vec3Array();
    yVertices->push_back(osg::Vec3(0.0f, 0.0f, 0.0f));
    yVertices->push_back(osg::Vec3(0.0f, 2.0f, 0.0f));
    yAxis->setVertexArray(yVertices);
    
    osg::ref_ptr<osg::Vec4Array> yColors = new osg::Vec4Array();
    yColors->push_back(osg::Vec4(0.0f, 1.0f, 0.0f, 1.0f));
    yAxis->setColorArray(yColors);
    yAxis->setColorBinding(osg::Geometry::BIND_OVERALL);
    
    yAxis->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINES, 0, 2));
    geode->addDrawable(yAxis);
    
    // Z轴 - 蓝色
    osg::ref_ptr<osg::Geometry> zAxis = new osg::Geometry();
    osg::ref_ptr<osg::Vec3Array> zVertices = new osg::Vec3Array();
    zVertices->push_back(osg::Vec3(0.0f, 0.0f, 0.0f));
    zVertices->push_back(osg::Vec3(0.0f, 0.0f, 2.0f));
    zAxis->setVertexArray(zVertices);
    
    osg::ref_ptr<osg::Vec4Array> zColors = new osg::Vec4Array();
    zColors->push_back(osg::Vec4(0.0f, 0.0f, 1.0f, 1.0f));
    zAxis->setColorArray(zColors);
    zAxis->setColorBinding(osg::Geometry::BIND_OVERALL);
    
    zAxis->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINES, 0, 2));
    geode->addDrawable(zAxis);
    
    // 禁用光照（坐标轴使用固定颜色）
    osg::ref_ptr<osg::StateSet> stateSet = geode->getOrCreateStateSet();
    stateSet->setMode(GL_LIGHTING, osg::StateAttribute::OFF);
    
    std::cout << "[osg] ✅ 坐标轴创建成功" << std::endl;
    return geode;
}

// 创建场景
osg::ref_ptr<osg::Group> createScene() {
    std::cout << "[osg] 创建场景..." << std::endl;
    
    osg::ref_ptr<osg::Group> root = new osg::Group();
    
    // 创建球体变换节点
    g_sphere_transform = new osg::PositionAttitudeTransform();
    g_sphere_transform->addChild(createSphere());
    
    // 添加到场景
    root->addChild(g_sphere_transform);
    root->addChild(createAxes());
    
    std::cout << "[osg] ✅ 场景创建成功" << std::endl;
    return root;
}

// 自定义事件处理器
class CustomEventHandler : public osgGA::GUIEventHandler {
public:
    virtual bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa) {
        switch (ea.getEventType()) {
            case osgGA::GUIEventAdapter::KEYDOWN:
                std::cout << "[event] 按键按下: " << ea.getKey() << std::endl;
                
                switch (ea.getKey()) {
                    case osgGA::GUIEventAdapter::KEY_Escape:
                        std::cout << "[event] ESC键，准备退出" << std::endl;
                        g_running = false;
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_Space:
                        std::cout << "[event] 空格键，重置视角" << std::endl;
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_Plus:
                    case osgGA::GUIEventAdapter::KEY_Equals:
                        g_rotation_speed *= 1.5;
                        std::cout << "[event] 加速旋转: " << g_rotation_speed << std::endl;
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_Minus:
                        g_rotation_speed /= 1.5;
                        std::cout << "[event] 减速旋转: " << g_rotation_speed << std::endl;
                        return true;
                }
                break;
                
            case osgGA::GUIEventAdapter::PUSH:
                std::cout << "[event] 鼠标按下: (" << ea.getX() << ", " << ea.getY() << ") 按钮: " << ea.getButton() << std::endl;
                return false;
                
            case osgGA::GUIEventAdapter::RELEASE:
                std::cout << "[event] 鼠标释放: (" << ea.getX() << ", " << ea.getY() << ") 按钮: " << ea.getButton() << std::endl;
                return false;
                
            case osgGA::GUIEventAdapter::SCROLL:
                std::cout << "[event] 鼠标滚轮: " << ea.getScrollingMotion() << std::endl;
                return false;
        }
        
        return false;
    }
};

// 更新回调
class UpdateCallback : public osg::NodeCallback {
public:
    virtual void operator()(osg::Node* node, osg::NodeVisitor* nv) {
        if (g_sphere_transform.valid()) {
            static double time = 0.0;
            time += 0.01 * g_rotation_speed;
            
            // 旋转球体
            osg::Quat rotation(time, osg::Vec3(0.0f, 0.0f, 1.0f));
            g_sphere_transform->setAttitude(rotation);
        }
        
        traverse(node, nv);
    }
};

// 主循环函数
void main_loop() {
    if (!g_running || !g_viewer) {
        return;
    }
    
    // 渲染一帧
    g_viewer->frame();
}

int main(int argc, char* argv[]) {
    std::cout << "🌍 OSG球体测试程序" << std::endl;
    std::cout << "==================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    try {
        // 创建查看器
        g_viewer = new osgViewer::Viewer();
        
        // 设置场景
        osg::ref_ptr<osg::Group> scene = createScene();
        g_viewer->setSceneData(scene);
        
        // 设置相机操作器
        g_viewer->setCameraManipulator(new osgGA::TrackballManipulator());
        
        // 添加事件处理器
        g_viewer->addEventHandler(new CustomEventHandler());
        g_viewer->addEventHandler(new osgGA::StateSetManipulator(g_viewer->getCamera()->getOrCreateStateSet()));
        g_viewer->addEventHandler(new osgViewer::StatsHandler());
        g_viewer->addEventHandler(new osgViewer::WindowSizeHandler());
        
        // 添加更新回调
        scene->setUpdateCallback(new UpdateCallback());
        
        // 设置窗口大小
        g_viewer->setUpViewInWindow(100, 100, 800, 600);
        
        // 初始化查看器
        g_viewer->realize();
        
        std::cout << "[main] ✅ OSG查看器初始化成功" << std::endl;
        std::cout << "[main] 控制说明:" << std::endl;
        std::cout << "[main] - 鼠标左键拖拽: 旋转视角" << std::endl;
        std::cout << "[main] - 鼠标右键拖拽: 缩放" << std::endl;
        std::cout << "[main] - 鼠标中键拖拽: 平移" << std::endl;
        std::cout << "[main] - +/-键: 调整球体旋转速度" << std::endl;
        std::cout << "[main] - 空格键: 重置视角" << std::endl;
        std::cout << "[main] - ESC键: 退出" << std::endl;
        
#ifdef EMSCRIPTEN
        // WebAssembly主循环
        emscripten_set_main_loop(main_loop, 60, 1);
#else
        // 桌面主循环
        while (g_running && !g_viewer->done()) {
            main_loop();
        }
#endif
        
    } catch (const std::exception& e) {
        std::cout << "[main] ❌ 异常: " << e.what() << std::endl;
        return 1;
    }
    
    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
