/* osgEarth
 * Copyright 2008-2014 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_INTERSECT_FEATUREFILTER_OPTIONS
#define OSGEARTH_DRIVER_INTERSECT_FEATUREFILTER_OPTIONS 1

#include <osgEarth/Common>
#include <osgEarth/FeatureSource>
#include <osgEarth/LayerReference>

namespace osgEarth { namespace Drivers
{
    using namespace osgEarth;

    /**
     * Options for the Raster feature driver.
     */
    class IntersectFeatureFilterOptions : public ConfigOptions // NO EXPORT; header only
    {
    public:
        IntersectFeatureFilterOptions( const ConfigOptions& opt =ConfigOptions() ) : ConfigOptions( opt )
        {
            _conf.set("driver", "intersect");
            _contains.init( true );
            fromConfig( _conf );
        }

        /** Features to load and use as boundary geometries */
        OE_OPTION_LAYER(FeatureSource, featureSource);

        /** If true (the default), accept features whose centroids are contained in the boundaries */
        optional<bool>& contains() { return _contains; }
        const optional<bool>& contains() const { return _contains; }

    public:
        Config getConfig() const {
            Config conf = ConfigOptions::getConfig();
            conf.key() = "intersect";
            featureSource().set(conf, "features");
            conf.set( "contains", _contains );
            return conf;
        }

    protected:
        void mergeConfig( const Config& conf ) {
            ConfigOptions::mergeConfig( conf );
            fromConfig( conf );
        }

    private:
        void fromConfig( const Config& conf ) {
            featureSource().get(conf, "features");
            conf.get( "contains", _contains );
        }

        optional<bool>                   _contains;
    };

} } // namespace osgEarth::Drivers

#endif // OSGEARTH_DRIVER_INTERSECT_FEATUREFILTER_OPTIONS
