/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/ImageLayer>
#include <osgEarth/LayerReference>
#include <osgEarth/FeatureSource>
#include <osgEarth/StyleSheet>
#include <osgEarth/SDF>

namespace osgEarth
{
    /**
     * Rasterizes feature data into distance fields.
     */
    class OSGEARTH_EXPORT FeatureSDFLayer : public ImageLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public ImageLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, ImageLayer::Options);
            OE_OPTION_LAYER(FeatureSource, features);
            OE_OPTION_VECTOR(ConfigOptions, filters);
            OE_OPTION_LAYER(StyleSheet, styleSheet);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgE<PERSON>h, FeatureSDFLayer, Options, osgEarth::ImageLayer, FeatureSDF);

        //! Sets the feature source to get road data from; call either this
        //! or setFeatureSourceLayer
        void setFeatureSource(FeatureSource* source);
        inline FeatureSource* getFeatureSource() const;

        //! Style sheet to use to render feature data
        void setStyleSheet(StyleSheet* styles);
        inline StyleSheet* getStyleSheet() const;

    public: // ImageLayer

        // Opens the layer and returns a status
        Status openImplementation() override;

        GeoImage createImageImplementation(
            const TileKey& key,
            ProgressCallback* progress) const override;

    protected: // Layer

        // Called by Map when it adds this layer
        void addedToMap(const class Map*) override;

        // Called by Map when it removes this layer
        void removedFromMap(const class Map*) override;

        // post-ctor initialization
        void init() override;


    protected:
        osg::ref_ptr<Session> _session;
        osg::ref_ptr<const FeatureProfile> _featureProfile;
        FeatureFilterChain _filterChain;
        SDFGenerator _sdfGenerator;

        void updateSession();

        void establishProfile();
    };    
    
    // template/inline impls .................................................

    FeatureSource* FeatureSDFLayer::getFeatureSource() const { return options().features().getLayer(); }
    StyleSheet* FeatureSDFLayer::getStyleSheet() const { return options().styleSheet().getLayer(); }

}

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::FeatureSDFLayer::Options);

