/**
 * WebGL兼容的简化数字地球测试程序
 * 专门为WebAssembly/WebGL环境设计
 */

#include <iostream>
#include <cmath>
#include <vector>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#include <GLES2/gl2.h>
#else
#include <SDL.h>
#include <SDL_opengl.h>
#endif

// 全局变量
static SDL_Window* g_window = nullptr;
static SDL_GLContext g_gl_context = nullptr;
static bool g_running = true;
static float g_rotation = 0.0f;
static double g_zoom = 1.0;

// 简化的顶点数据
struct Vertex {
    float x, y, z;
    float r, g, b;
};

// 地球顶点数据
static std::vector<Vertex> g_earth_vertices;
static std::vector<unsigned int> g_earth_indices;

// 初始化地球几何体
void initEarthGeometry() {
    std::cout << "[earth] 初始化地球几何体..." << std::endl;
    
    g_earth_vertices.clear();
    g_earth_indices.clear();
    
    const int slices = 32;
    const int stacks = 16;
    const float radius = 1.0f;
    
    // 生成球体顶点
    for (int i = 0; i <= stacks; ++i) {
        float lat = M_PI * (-0.5f + (float)i / stacks);
        float z = sin(lat);
        float zr = cos(lat);
        
        for (int j = 0; j <= slices; ++j) {
            float lng = 2 * M_PI * (float)j / slices;
            float x = cos(lng) * zr;
            float y = sin(lng) * zr;
            
            Vertex vertex;
            vertex.x = radius * x;
            vertex.y = radius * y;
            vertex.z = radius * z;
            
            // 根据位置设置颜色（模拟地球）
            if (z > 0.3f) {
                // 北极 - 白色
                vertex.r = 0.9f; vertex.g = 0.9f; vertex.b = 1.0f;
            } else if (z < -0.3f) {
                // 南极 - 白色
                vertex.r = 0.9f; vertex.g = 0.9f; vertex.b = 1.0f;
            } else if (abs(x) > 0.5f || abs(y) > 0.5f) {
                // 陆地 - 绿色
                vertex.r = 0.2f; vertex.g = 0.8f; vertex.b = 0.3f;
            } else {
                // 海洋 - 蓝色
                vertex.r = 0.2f; vertex.g = 0.5f; vertex.b = 0.9f;
            }
            
            g_earth_vertices.push_back(vertex);
        }
    }
    
    // 生成索引
    for (int i = 0; i < stacks; ++i) {
        for (int j = 0; j < slices; ++j) {
            int first = i * (slices + 1) + j;
            int second = first + slices + 1;
            
            // 第一个三角形
            g_earth_indices.push_back(first);
            g_earth_indices.push_back(second);
            g_earth_indices.push_back(first + 1);
            
            // 第二个三角形
            g_earth_indices.push_back(second);
            g_earth_indices.push_back(second + 1);
            g_earth_indices.push_back(first + 1);
        }
    }
    
    std::cout << "[earth] 生成了 " << g_earth_vertices.size() << " 个顶点，" 
              << g_earth_indices.size() / 3 << " 个三角形" << std::endl;
}

// 渲染地球
void renderEarth() {
    // 启用顶点数组
    glEnableClientState(GL_VERTEX_ARRAY);
    glEnableClientState(GL_COLOR_ARRAY);
    
    // 设置顶点指针
    glVertexPointer(3, GL_FLOAT, sizeof(Vertex), &g_earth_vertices[0].x);
    glColorPointer(3, GL_FLOAT, sizeof(Vertex), &g_earth_vertices[0].r);
    
    // 绘制三角形
    glDrawElements(GL_TRIANGLES, g_earth_indices.size(), GL_UNSIGNED_INT, &g_earth_indices[0]);
    
    // 禁用顶点数组
    glDisableClientState(GL_VERTEX_ARRAY);
    glDisableClientState(GL_COLOR_ARRAY);
}

// 渲染坐标轴
void renderAxes() {
    glLineWidth(3.0f);
    
    Vertex axes[] = {
        // X轴 - 红色
        {0.0f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f},
        {1.5f, 0.0f, 0.0f, 1.0f, 0.0f, 0.0f},
        // Y轴 - 绿色
        {0.0f, 0.0f, 0.0f, 0.0f, 1.0f, 0.0f},
        {0.0f, 1.5f, 0.0f, 0.0f, 1.0f, 0.0f},
        // Z轴 - 蓝色
        {0.0f, 0.0f, 0.0f, 0.0f, 0.0f, 1.0f},
        {0.0f, 0.0f, 1.5f, 0.0f, 0.0f, 1.0f}
    };
    
    glEnableClientState(GL_VERTEX_ARRAY);
    glEnableClientState(GL_COLOR_ARRAY);
    
    glVertexPointer(3, GL_FLOAT, sizeof(Vertex), &axes[0].x);
    glColorPointer(3, GL_FLOAT, sizeof(Vertex), &axes[0].r);
    
    glDrawArrays(GL_LINES, 0, 6);
    
    glDisableClientState(GL_VERTEX_ARRAY);
    glDisableClientState(GL_COLOR_ARRAY);
}

// 主渲染函数
void render() {
    // 清除屏幕
    glClearColor(0.05f, 0.1f, 0.2f, 1.0f);
    glClear(GL_COLOR_BUFFER_BIT | GL_DEPTH_BUFFER_BIT);
    
    // 启用深度测试
    glEnable(GL_DEPTH_TEST);
    
    // 设置投影矩阵（简化的正交投影）
    glMatrixMode(GL_PROJECTION);
    glLoadIdentity();
    glOrtho(-2.0, 2.0, -1.5, 1.5, -10.0, 10.0);
    
    // 设置模型视图矩阵
    glMatrixMode(GL_MODELVIEW);
    glLoadIdentity();
    
    // 应用缩放和旋转
    glScalef(g_zoom, g_zoom, g_zoom);
    glRotatef(g_rotation, 0.0f, 1.0f, 0.0f);
    glRotatef(20.0f, 1.0f, 0.0f, 0.0f); // 稍微倾斜以便观看
    
    // 渲染地球
    renderEarth();
    
    // 渲染坐标轴
    renderAxes();
    
    // 更新旋转
    g_rotation += 0.5f;
    if (g_rotation >= 360.0f) {
        g_rotation = 0.0f;
    }
}

// 处理事件
void handle_events() {
    SDL_Event event;
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                g_running = false;
                std::cout << "[event] 收到退出事件" << std::endl;
                break;
                
            case SDL_KEYDOWN:
                std::cout << "[event] 按键: " << SDL_GetKeyName(event.key.keysym.sym) << std::endl;
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    g_running = false;
                } else if (event.key.keysym.sym == SDLK_PLUS || event.key.keysym.sym == SDLK_EQUALS) {
                    g_zoom *= 1.2;
                    std::cout << "[earth] 放大，缩放级别: " << g_zoom << std::endl;
                } else if (event.key.keysym.sym == SDLK_MINUS) {
                    g_zoom /= 1.2;
                    std::cout << "[earth] 缩小，缩放级别: " << g_zoom << std::endl;
                }
                break;
                
            case SDL_MOUSEBUTTONDOWN:
                std::cout << "[event] 鼠标点击: (" << event.button.x << ", " << event.button.y << ")" << std::endl;
                break;
                
            case SDL_MOUSEWHEEL:
                if (event.wheel.y > 0) {
                    g_zoom *= 1.1;
                    std::cout << "[earth] 滚轮放大，缩放级别: " << g_zoom << std::endl;
                } else if (event.wheel.y < 0) {
                    g_zoom /= 1.1;
                    std::cout << "[earth] 滚轮缩小，缩放级别: " << g_zoom << std::endl;
                }
                break;
        }
    }
}

// 主循环函数
void main_loop() {
    if (!g_running) {
        return;
    }
    
    handle_events();
    render();
    SDL_GL_SwapWindow(g_window);
}

int main(int argc, char* argv[]) {
    std::cout << "🌍 WebGL兼容数字地球测试程序" << std::endl;
    std::cout << "==============================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;
    
    // 设置OpenGL属性
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MAJOR_VERSION, 2);
    SDL_GL_SetAttribute(SDL_GL_CONTEXT_MINOR_VERSION, 0);
    SDL_GL_SetAttribute(SDL_GL_DOUBLEBUFFER, 1);
    SDL_GL_SetAttribute(SDL_GL_DEPTH_SIZE, 16);
    
    // 创建窗口
    g_window = SDL_CreateWindow(
        "WebGL Compatible Digital Earth",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_OPENGL | SDL_WINDOW_SHOWN
    );
    
    if (!g_window) {
        std::cout << "[main] ❌ 窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 窗口创建成功: 800x600" << std::endl;
    
    // 创建OpenGL上下文
    g_gl_context = SDL_GL_CreateContext(g_window);
    if (!g_gl_context) {
        std::cout << "[main] ❌ OpenGL上下文创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(g_window);
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ OpenGL上下文创建成功" << std::endl;
    
    // 设置视口
    glViewport(0, 0, 800, 600);
    
    // 初始化地球几何体
    initEarthGeometry();
    
    std::cout << "[main] ✅ WebGL兼容数字地球初始化完成" << std::endl;
    std::cout << "[main] 控制说明:" << std::endl;
    std::cout << "[main] - 滚轮: 缩放" << std::endl;
    std::cout << "[main] - +/-键: 缩放" << std::endl;
    std::cout << "[main] - ESC键: 退出" << std::endl;
    std::cout << "[main] - 地球会自动旋转" << std::endl;
    
#ifdef EMSCRIPTEN
    // WebAssembly主循环
    emscripten_set_main_loop(main_loop, 60, 1);
#else
    // 桌面主循环
    while (g_running) {
        main_loop();
        SDL_Delay(16);
    }
#endif
    
    // 清理资源
    if (g_gl_context) {
        SDL_GL_DeleteContext(g_gl_context);
    }
    if (g_window) {
        SDL_DestroyWindow(g_window);
    }
    SDL_Quit();
    
    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
