#!/usr/bin/env python3
"""
直接编译WebAssembly测试程序
"""

import os
import subprocess
import sys

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        if result.returncode == 0:
            print(f"✅ {description} 成功")
            return True
        else:
            print(f"❌ {description} 失败 (返回码: {result.returncode})")
            return False
    except Exception as e:
        print(f"❌ {description} 异常: {e}")
        return False

def main():
    print("🚀 WebAssembly测试程序编译器")
    print("============================")
    
    # 设置环境
    os.chdir(os.path.dirname(os.path.abspath(__file__)))
    
    # 检查Emscripten
    if not run_command("C:\\dev\\emsdk\\emsdk_env.bat && emcc --version", "检查Emscripten"):
        return False
    
    # 编译weejobs测试程序（简化版，不依赖osgEarth）
    weejobs_cmd = """C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_weejobs_simple.cpp -o test_weejobs.js -pthread -s USE_PTHREADS=1 -s PTHREAD_POOL_SIZE=2 -s SHARED_MEMORY=1 -s PROXY_TO_PTHREAD=1 -s ASSERTIONS=1 -s ALLOW_MEMORY_GROWTH=1 -O2"""
    
    if run_command(weejobs_cmd, "编译weejobs测试程序"):
        print("✅ weejobs测试程序编译成功")
    else:
        print("❌ weejobs测试程序编译失败")
    
    # 编译SDL2简化测试程序
    sdl2_cmd = """C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_sdl2_simple.cpp -o test_sdl2.js -s USE_SDL=2 -s ALLOW_MEMORY_GROWTH=1 -O2"""
    
    if run_command(sdl2_cmd, "编译SDL2测试程序"):
        print("✅ SDL2测试程序编译成功")
    else:
        print("❌ SDL2测试程序编译失败")
    
    print("\n🎯 编译完成！")
    print("测试地址:")
    print("  • weejobs测试: http://localhost:8080/test_weejobs.html")
    print("  • SDL2测试: http://localhost:8080/test_sdl2.html")

if __name__ == "__main__":
    main()
