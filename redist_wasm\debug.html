<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth WebAssembly - Debug Mode</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background-color: #000;
            font-family: Arial, sans-serif;
            overflow: hidden;
        }
        
        #canvas {
            display: block;
            width: 100vw;
            height: 100vh;
            background-color: #111;
        }
        
        #debug {
            position: absolute;
            top: 10px;
            left: 10px;
            color: #0f0;
            background: rgba(0,0,0,0.9);
            padding: 10px;
            border-radius: 5px;
            font-size: 10px;
            z-index: 1000;
            max-width: 400px;
            font-family: monospace;
            max-height: 200px;
            overflow-y: auto;
        }
        
        .error { color: #f00; }
        .warning { color: #fa0; }
        .info { color: #0f0; }
    </style>
</head>
<body>
    <div id="debug">
        <h4>osgEarth WebAssembly Debug Console</h4>
        <div id="messages"></div>
    </div>
    
    <canvas id="canvas"></canvas>
    
    <script>
        let messageCount = 0;
        const maxMessages = 50;
        
        function addMessage(text, type = 'info') {
            const messagesDiv = document.getElementById('messages');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = `${++messageCount}: ${text}`;
            messagesDiv.appendChild(messageDiv);
            
            // 保持最新的消息在顶部
            if (messagesDiv.children.length > maxMessages) {
                messagesDiv.removeChild(messagesDiv.firstChild);
            }
            messagesDiv.scrollTop = messagesDiv.scrollHeight;
            
            console.log(`[${type}] ${text}`);
        }
        
        addMessage('Starting WebAssembly application...');
        
        // 创建精确的配置，明确指定max_level
        const preciseConfig = `<map name="DebugMap" type="geocentric" version="2">
    <XYZImage name="OpenStreetMap">
        <url>https://a.tile.openstreetmap.org/{z}/{x}/{y}.png</url>
        <profile>spherical-mercator</profile>
        <format>png</format>
        <min_level>0</min_level>
        <max_level>18</max_level>
        <options>
            <max_concurrent_requests>2</max_concurrent_requests>
            <timeout>30</timeout>
        </options>
    </XYZImage>
    <options>
        <terrain>
            <tile_size>256</tile_size>
            <max_lod>18</max_lod>
            <cast_shadows>false</cast_shadows>
            <progressive>false</progressive>
            <concurrency>2</concurrency>
            <normalize_edges>false</normalize_edges>
            <mercator_fast_path>true</mercator_fast_path>
        </terrain>
        <cache_policy usage="read_write" />
        <services>
            <sky hours="12.0" />
        </services>
    </options>
</map>`;
        
        // 配置WebAssembly模块
        var Module = {
            canvas: document.getElementById('canvas'),
            
            preRun: [
                function() {
                    addMessage('PreRun: Setting up virtual filesystem...');
                    
                    // 设置WebGL上下文
                    Module.canvas.webglContextAttributes = {
                        alpha: false,
                        depth: true,
                        stencil: false,
                        antialias: false,
                        premultipliedAlpha: false,
                        preserveDrawingBuffer: false,
                        powerPreference: "default",
                        failIfMajorPerformanceCaveat: false,
                        majorVersion: 1,
                        minorVersion: 0
                    };
                    
                    addMessage('WebGL context configured for OpenGL ES 2.0');
                    
                    // 延迟文件系统操作
                    setTimeout(() => {
                        if (typeof FS !== 'undefined') {
                            try {
                                addMessage('Writing configuration file...');
                                FS.writeFile('/myviewer_config.earth', preciseConfig);
                                
                                // 验证文件创建
                                const stat = FS.analyzePath('/myviewer_config.earth');
                                if (stat.exists) {
                                    addMessage(`Config file created: ${stat.object.contents.length} bytes`);
                                } else {
                                    addMessage('ERROR: Config file not created', 'error');
                                }
                                
                                // 列出根目录文件
                                const files = FS.readdir('/');
                                addMessage(`Root files: ${files.join(', ')}`);
                                
                            } catch (e) {
                                addMessage(`Filesystem error: ${e.message}`, 'error');
                            }
                        } else {
                            addMessage('FS not available in preRun, will retry later', 'warning');
                        }
                    }, 10);
                }
            ],
            
            onRuntimeInitialized: function() {
                addMessage('WebAssembly runtime initialized');
                
                // 最后一次确保配置文件存在
                setTimeout(() => {
                    if (typeof FS !== 'undefined') {
                        try {
                            if (!FS.analyzePath('/myviewer_config.earth').exists) {
                                addMessage('Creating config file at runtime...');
                                FS.writeFile('/myviewer_config.earth', preciseConfig);
                            }
                            
                            // 显示文件内容的一部分用于验证
                            const content = FS.readFile('/myviewer_config.earth', { encoding: 'utf8' });
                            addMessage(`Config content preview: ${content.substring(0, 50)}...`);
                            
                        } catch (e) {
                            addMessage(`Runtime filesystem error: ${e.message}`, 'error');
                        }
                    }
                }, 100);
            },
            
            print: function(text) {
                // 解析不同类型的消息
                if (text.includes('[myviewer]')) {
                    addMessage(`App: ${text.replace('[myviewer] ', '')}`);
                } else if (text.includes('[WebAssemblyInputHandler]')) {
                    // 忽略过多的输入处理器消息
                    return;
                } else if (text.includes('配置WebAssembly渲染环境')) {
                    addMessage('WebAssembly graphics environment configured');
                } else if (text.includes('尝试加载默认配置文件')) {
                    addMessage('Loading default configuration file...');
                } else {
                    addMessage(`stdout: ${text}`);
                }
            },
            
            printErr: function(text) {
                let messageType = 'error';
                let processedText = text;
                
                if (text.includes('Warning')) {
                    messageType = 'warning';
                    processedText = text.replace(/\[.*?\]\s*/, ''); // 移除标签
                } else if (text.includes('Error reading file')) {
                    addMessage('Config file read error - checking filesystem...', 'error');
                    
                    // 诊断文件系统状态
                    if (typeof FS !== 'undefined') {
                        try {
                            const stat = FS.analyzePath('/myviewer_config.earth');
                            addMessage(`File exists: ${stat.exists}`, 'info');
                            if (stat.exists) {
                                addMessage(`File size: ${stat.object.contents.length} bytes`, 'info');
                            }
                        } catch (e) {
                            addMessage(`Filesystem check error: ${e.message}`, 'error');
                        }
                    }
                } else if (text.includes('Failed to realize')) {
                    addMessage('Graphics context issue detected', 'warning');
                } else if (text.includes('No earth file loaded')) {
                    addMessage('No configuration loaded, using default map', 'warning');
                } else if (text.includes('Aborted')) {
                    addMessage('APPLICATION ABORTED', 'error');
                }
                
                addMessage(`stderr: ${processedText}`, messageType);
            },
            
            onAbort: function(what) {
                addMessage(`ABORT: ${what || 'Unknown error'}`, 'error');
                
                // 显示调用堆栈
                if (typeof stackTrace !== 'undefined') {
                    addMessage(`Stack: ${stackTrace().join(' -> ')}`, 'error');
                }
                
                // 尝试获取更多调试信息
                setTimeout(() => {
                    addMessage('Attempting to collect debug information...', 'info');
                    
                    // 检查WebGL状态
                    const gl = Module.canvas.getContext('webgl') || Module.canvas.getContext('experimental-webgl');
                    if (gl) {
                        const error = gl.getError();
                        if (error !== gl.NO_ERROR) {
                            addMessage(`WebGL error: ${error}`, 'error');
                        } else {
                            addMessage('WebGL context appears healthy', 'info');
                        }
                    } else {
                        addMessage('WebGL context unavailable', 'error');
                    }
                }, 100);
            },
            
            onOutOfMemory: function() {
                addMessage('OUT OF MEMORY ERROR', 'error');
            },
            
            // 保守的内存配置
            INITIAL_MEMORY: 128 * 1024 * 1024, // 128MB
            ALLOW_MEMORY_GROWTH: true,
            MAXIMUM_MEMORY: 512 * 1024 * 1024, // 最大512MB
            
            // 环境变量
            ENV: {
                'OSG_NOTIFY_LEVEL': 'INFO',
                'OSGEARTH_NOTIFY_LEVEL': 'INFO',
                'OSG_GL_ERROR_CHECKING': 'ON'
            },
            
            // 不传递参数，让程序使用默认行为
            arguments: [],
            
            // 其他设置
            noInitialRun: false,
            noExitRuntime: true,
            
            locateFile: function(path, prefix) {
                addMessage(`Locating: ${prefix}${path}`);
                return prefix + path;
            },
            
            monitorRunDependencies: function(left) {
                addMessage(`Dependencies remaining: ${left}`);
            }
        };
        
        // 检查WebGL支持
        function checkWebGLSupport() {
            const canvas = document.createElement('canvas');
            const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
            
            if (!gl) {
                addMessage('WebGL not supported!', 'error');
                return false;
            }
            
            const vendor = gl.getParameter(gl.VENDOR);
            const renderer = gl.getParameter(gl.RENDERER);
            const version = gl.getParameter(gl.VERSION);
            
            addMessage(`WebGL supported: ${vendor} ${renderer} ${version}`);
            return true;
        }
        
        if (!checkWebGLSupport()) {
            addMessage('WebGL检查失败，但尝试继续...', 'warning');
        }
        
        // 加载WebAssembly模块
        addMessage('Loading WebAssembly script...');
        const script = document.createElement('script');
        script.src = 'osgearth_myviewer.js';
        script.onload = function() {
            addMessage('WebAssembly script loaded successfully');
        };
        script.onerror = function(e) {
            addMessage(`Failed to load WebAssembly script: ${e.message}`, 'error');
        };
        document.body.appendChild(script);
        
        // 全局错误处理
        window.onerror = function(message, source, lineno, colno, error) {
            addMessage(`Global error: ${message} at ${source}:${lineno}`, 'error');
            return false;
        };
        
        window.onunhandledrejection = function(event) {
            addMessage(`Unhandled promise rejection: ${event.reason}`, 'error');
        };
        
    </script>
</body>
</html> 