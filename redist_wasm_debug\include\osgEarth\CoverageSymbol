/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHSYMBOLOGY_COVERAGE_SYMBOL_H
#define OSGEARTHSYMBOLOGY_COVERAGE_SYMBOL_H 1

#include <osgEarth/Common>
#include <osgEarth/Expression>
#include <osgEarth/Symbol>
#include <osg/Referenced>
#include <vector>

namespace osgEarth
{
    /** 
     * Symbol that contains coverage encoding information
     */
    class OSGEARTH_EXPORT CoverageSymbol : public Symbol
    {
    public:
        META_Object(osgEarth, CoverageSymbol);

        /** construct a symbol */
        CoverageSymbol(const Config& conf =Config());
        CoverageSymbol(const CoverageSymbol& rhs,const osg::CopyOp& copyop=osg::CopyOp::SHALLOW_COPY);

        /** coverage value expression; resolves to a 32-bit floating point number. */
        optional<NumericExpression>& valueExpression() { return _valueExpr; }
        const optional<NumericExpression>& valueExpression() const { return _valueExpr; }

    public:
        virtual Config getConfig() const;
        virtual void mergeConfig( const Config& conf );
        static void parseSLD(const Config& c, class Style& style);

    protected:
        optional<NumericExpression> _valueExpr;
        
        /** dtor */
        virtual ~CoverageSymbol() { }
    };
} // namespace osgEarth

#endif // OSGEARTHSYMBOLOGY_COVERAGE_SYMBOL_H
