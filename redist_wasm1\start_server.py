#!/usr/bin/env python3
"""
强化版HTTP服务器 - 确保SharedArrayBuffer支持
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class SharedArrayBufferHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """
    自定义HTTP请求处理器，强制添加支持SharedArrayBuffer的HTTP头
    """
    
    def end_headers(self):
        # 强制添加支持SharedArrayBuffer的HTTP头
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 添加其他重要的安全头
        self.send_header('Cross-Origin-Allowlist-Policy', '*')
        
        # 缓存控制 - 确保WebAssembly文件不被缓存
        if self.path.endswith('.wasm'):
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
            self.send_header('Pragma', 'no-cache')
            self.send_header('Expires', '0')
        elif self.path.endswith('.js'):
            self.send_header('Cache-Control', 'no-cache, no-store, must-revalidate')
            self.send_header('Pragma', 'no-cache')
            self.send_header('Expires', '0')
        
        super().end_headers()
    
    def guess_type(self, path):
        """
        设置正确的MIME类型
        """
        mimetype, encoding = super().guess_type(path)
        
        # 设置WebAssembly文件的MIME类型
        if path.endswith('.wasm'):
            return 'application/wasm', encoding
        elif path.endswith('.js'):
            return 'application/javascript', encoding
        elif path.endswith('.html'):
            return 'text/html', encoding
        
        return mimetype, encoding
    
    def log_message(self, format, *args):
        """
        自定义日志格式，显示HTTP头信息
        """
        timestamp = self.log_date_time_string()
        print(f"[{timestamp}] {format % args}")
        
        # 对于关键文件，显示HTTP头
        if any(self.path.endswith(ext) for ext in ['.wasm', '.js', '.html']):
            print(f"  -> 文件: {self.path}")
            print(f"  -> COEP: require-corp")
            print(f"  -> COOP: same-origin")

def main():
    """
    启动强化版HTTP服务器
    """
    port = 8083
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"错误: 无效的端口号 '{sys.argv[1]}'")
            sys.exit(1)
    
    # 检查当前目录
    current_dir = os.getcwd()
    wasm_files = [f for f in os.listdir('.') if f.endswith('.wasm')]
    js_files = [f for f in os.listdir('.') if f.endswith('.js') and not f.startswith('start_server')]
    html_files = [f for f in os.listdir('.') if f.endswith('.html')]
    
    print("🚀 强化版WebAssembly多线程HTTP服务器")
    print("=" * 60)
    print(f"端口: {port}")
    print(f"目录: {current_dir}")
    print(f"WebAssembly文件: {len(wasm_files)} 个")
    print(f"JavaScript文件: {len(js_files)} 个")
    print(f"HTML文件: {len(html_files)} 个")
    print()
    
    if wasm_files:
        print("📁 WebAssembly文件:")
        for wasm_file in sorted(wasm_files):
            size = os.path.getsize(wasm_file) / (1024 * 1024)
            print(f"  • {wasm_file} ({size:.1f} MB)")
        print()
    
    print("🔧 强制HTTP头配置:")
    print("  • Cross-Origin-Embedder-Policy: require-corp")
    print("  • Cross-Origin-Opener-Policy: same-origin")
    print("  • Cross-Origin-Resource-Policy: cross-origin")
    print("  • Cache-Control: no-cache (for .wasm/.js)")
    print()
    
    print("🌐 测试地址:")
    print(f"  • 简单pthread测试: http://localhost:{port}/test_simple_pthread.html")
    print(f"  • 最小化osgEarth: http://localhost:{port}/test_minimal.html")
    print(f"  • 完整多线程osgEarth: http://localhost:{port}/test_multithreaded.html")
    print()
    
    print("⚠️  重要提示:")
    print("  • 确保使用支持SharedArrayBuffer的浏览器")
    print("  • Chrome: 启用 --enable-features=SharedArrayBuffer")
    print("  • Firefox: dom.postMessage.sharedArrayBuffer.withCOOP_COEP = true")
    print("  • 检查浏览器开发者工具的控制台和网络标签")
    print()
    
    try:
        with socketserver.TCPServer(("", port), SharedArrayBufferHTTPRequestHandler) as httpd:
            print(f"🚀 服务器启动成功，监听端口 {port}")
            print("📡 HTTP头将被强制设置为支持SharedArrayBuffer")
            print("按 Ctrl+C 停止服务器")
            print("=" * 60)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ 错误: 端口 {port} 已被占用")
            print("请尝试使用其他端口，例如:")
            print(f"python start_server.py {port + 1}")
        else:
            print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
