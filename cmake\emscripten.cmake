# Emscripten CMake toolchain file for WebAssembly compilation
# 用于osgEarth WebAssembly编译的Emscripten工具链配置

# 首先设置系统信息
set(CMAKE_SYSTEM_NAME Emscripten)
set(CMAKE_SYSTEM_VERSION 1)  
set(CMAKE_SYSTEM_PROCESSOR x86)

# 自动检测Emscripten SDK路径 - 这必须在设置编译器之前
if(NOT DEFINED EMSCRIPTEN_ROOT_PATH)
    if(DEFINED ENV{EMSCRIPTEN})
        set(EMSCRIPTEN_ROOT_PATH "$ENV{EMSCRIPTEN}")
    elseif(DEFINED ENV{EMSDK})
        set(EMSCRIPTEN_ROOT_PATH "$ENV{EMSDK}/upstream/emscripten")
    elseif(EXISTS "C:/dev/emsdk/upstream/emscripten")
        set(EMSCRIPTEN_ROOT_PATH "C:/dev/emsdk/upstream/emscripten")
    else()
        message(FATAL_ERROR "Could not find Emscripten. Please set EMSDK environment variable or install Emscripten to C:/dev/emsdk")
    endif()
endif()

message(STATUS "Using Emscripten at: ${EMSCRIPTEN_ROOT_PATH}")

# 在 CMake 进行任何编译器检测之前设置编译器
# 标准化路径以避免反斜杠问题
file(TO_CMAKE_PATH "${EMSCRIPTEN_ROOT_PATH}" EMSCRIPTEN_ROOT_PATH)

# 检查工具是否存在并设置正确的编译器路径
if(EXISTS "${EMSCRIPTEN_ROOT_PATH}/emcc.bat")
    set(CMAKE_C_COMPILER "${EMSCRIPTEN_ROOT_PATH}/emcc.bat" CACHE FILEPATH "C compiler" FORCE)
    set(CMAKE_CXX_COMPILER "${EMSCRIPTEN_ROOT_PATH}/em++.bat" CACHE FILEPATH "C++ compiler" FORCE)
    set(CMAKE_AR "${EMSCRIPTEN_ROOT_PATH}/emar.bat" CACHE FILEPATH "Archiver" FORCE)
    set(CMAKE_RANLIB "${EMSCRIPTEN_ROOT_PATH}/emranlib.bat" CACHE FILEPATH "Ranlib" FORCE)
    message(STATUS "Using .bat wrappers for Emscripten tools")
elseif(EXISTS "${EMSCRIPTEN_ROOT_PATH}/emcc")
    set(CMAKE_C_COMPILER "${EMSCRIPTEN_ROOT_PATH}/emcc" CACHE FILEPATH "C compiler" FORCE)
    set(CMAKE_CXX_COMPILER "${EMSCRIPTEN_ROOT_PATH}/em++" CACHE FILEPATH "C++ compiler" FORCE)
    set(CMAKE_AR "${EMSCRIPTEN_ROOT_PATH}/emar" CACHE FILEPATH "Archiver" FORCE)
    set(CMAKE_RANLIB "${EMSCRIPTEN_ROOT_PATH}/emranlib" CACHE FILEPATH "Ranlib" FORCE)
    message(STATUS "Using direct Emscripten tools")
else()
    message(FATAL_ERROR "Could not find emcc at ${EMSCRIPTEN_ROOT_PATH}")
endif()

# 这些设置告诉CMake这是交叉编译环境
set(CMAKE_CROSSCOMPILING TRUE)
set(CMAKE_CROSSCOMPILING_EMULATOR "")

# 设置交叉编译根路径模式
set(CMAKE_FIND_ROOT_PATH ${EMSCRIPTEN_ROOT_PATH})
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_PACKAGE ONLY)

# WebAssembly特定编译选项 - 启用调试信息和断言
# 添加详细的错误信息和调试支持
set(CMAKE_C_FLAGS "-O1 -g -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ASSERTIONS=1 -s SAFE_HEAP=1 -s STACK_OVERFLOW_CHECK=2 -D__EMSCRIPTEN__" CACHE STRING "C flags")
set(CMAKE_CXX_FLAGS "-O1 -g -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ASSERTIONS=1 -s SAFE_HEAP=1 -s STACK_OVERFLOW_CHECK=2 -std=c++17 -D__EMSCRIPTEN__ -frtti" CACHE STRING "CXX flags")

# WebAssembly链接选项
set(CMAKE_EXE_LINKER_FLAGS "-s WASM=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s DISABLE_EXCEPTION_CATCHING=0 -s EXPORTED_RUNTIME_METHODS=['ccall','cwrap','FS','writeFile'] --shell-file ${CMAKE_SOURCE_DIR}/html/shell.html" CACHE STRING "Linker flags")

# 设置WebAssembly依赖库路径
# 检查wasm_dep是否存在，否则使用vcpkg
set(WASM_DEP_ROOT "F:/cmo-dev/my_osgearth_web/osgearth_third_party/wasm_dep")
if(EXISTS "${WASM_DEP_ROOT}")
    set(WASM_INCLUDE_DIR "${WASM_DEP_ROOT}/include")
    set(WASM_LIB_DIR "${WASM_DEP_ROOT}/lib")
    message(STATUS "Using wasm_dep dependencies: ${WASM_DEP_ROOT}")
else()
    # 使用VCPKG WebAssembly依赖库路径
    set(VCPKG_WASM_ROOT "C:/dev/vcpkg/installed/wasm32-emscripten")
    set(WASM_INCLUDE_DIR "${VCPKG_WASM_ROOT}/include")
    set(WASM_LIB_DIR "${VCPKG_WASM_ROOT}/lib")
    message(STATUS "Using vcpkg dependencies: ${VCPKG_WASM_ROOT}")
endif()

set(VCPKG_WASM_ROOT "C:/dev/vcpkg/installed/wasm32-emscripten")
set(VCPKG_WASM_INCLUDE_DIR "${VCPKG_WASM_ROOT}/include")
set(VCPKG_WASM_LIB_DIR "${VCPKG_WASM_ROOT}/lib")

# 函数：按优先级查找库文件
function(find_wasm_library VAR_NAME LIB_NAME)
    # 优先在wasm_dep中查找
    if(EXISTS "${WASM_LIB_DIR}/${LIB_NAME}")
        set(${VAR_NAME} "${WASM_LIB_DIR}/${LIB_NAME}" PARENT_SCOPE)
        message(STATUS "Found ${LIB_NAME} in wasm_dep: ${WASM_LIB_DIR}/${LIB_NAME}")
    # 然后在vcpkg中查找
    elseif(EXISTS "${VCPKG_WASM_LIB_DIR}/${LIB_NAME}")
        set(${VAR_NAME} "${VCPKG_WASM_LIB_DIR}/${LIB_NAME}" PARENT_SCOPE)
        message(STATUS "Found ${LIB_NAME} in vcpkg: ${VCPKG_WASM_LIB_DIR}/${LIB_NAME}")
    else()
        message(WARNING "Library ${LIB_NAME} not found in either wasm_dep or vcpkg")
        set(${VAR_NAME} "${WASM_LIB_DIR}/${LIB_NAME}" PARENT_SCOPE)
    endif()
endfunction()

# 函数：按优先级设置包含目录
function(set_wasm_includes)
    # 优先使用wasm_dep的头文件
    if(EXISTS "${WASM_INCLUDE_DIR}")
        include_directories(${WASM_INCLUDE_DIR})
        message(STATUS "Using include directory: ${WASM_INCLUDE_DIR}")
    endif()
    
    # 然后添加vcpkg的头文件目录
    if(EXISTS "${VCPKG_WASM_INCLUDE_DIR}")
        include_directories(${VCPKG_WASM_INCLUDE_DIR})
        message(STATUS "Using include directory: ${VCPKG_WASM_INCLUDE_DIR}")
    endif()
endfunction()

# 设置OpenSceneGraph路径
set(OPENSCENEGRAPH_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSG_PLUGINS_DIR "${WASM_LIB_DIR}/osgPlugins-3.7.0")

# 设置OpenSceneGraph版本
set(OPENSCENEGRAPH_VERSION "3.7.0")
set(OPENSCENEGRAPH_MAJOR_VERSION "3")
set(OPENSCENEGRAPH_MINOR_VERSION "7")
set(OPENSCENEGRAPH_PATCH_VERSION "0")

# 使用优先级查找函数设置OpenSceneGraph库
find_wasm_library(OSG_LIBRARY "libosg.a")
find_wasm_library(OSGDB_LIBRARY "libosgDB.a")
find_wasm_library(OSGGA_LIBRARY "libosgGA.a")
find_wasm_library(OSGUTIL_LIBRARY "libosgUtil.a")
find_wasm_library(OSGVIEWER_LIBRARY "libosgViewer.a")
find_wasm_library(OSGTEXT_LIBRARY "libosgText.a")
find_wasm_library(OSGSHADOW_LIBRARY "libosgShadow.a")
find_wasm_library(OSGSIM_LIBRARY "libosgSim.a")
find_wasm_library(OSGMANIPULATOR_LIBRARY "libosgManipulator.a")
find_wasm_library(OPENTHREADS_LIBRARY "libOpenThreads.a")

# 设置包含目录变量（优先使用wasm_dep）
set(OSG_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGDB_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGGA_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGUTIL_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGVIEWER_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGTEXT_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGSHADOW_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGSIM_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OSGMANIPULATOR_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(OPENTHREADS_INCLUDE_DIR "${WASM_INCLUDE_DIR}")

# 设置FOUND标志
set(OSG_FOUND TRUE)
set(OSGDB_FOUND TRUE)
set(OSGGA_FOUND TRUE)
set(OSGUTIL_FOUND TRUE)
set(OSGVIEWER_FOUND TRUE)
set(OSGTEXT_FOUND TRUE)
set(OSGSHADOW_FOUND TRUE)
set(OSGSIM_FOUND TRUE)
set(OSGMANIPULATOR_FOUND TRUE)
set(OPENTHREADS_FOUND TRUE)
set(OpenSceneGraph_FOUND TRUE)

# 设置库列表
set(OPENSCENEGRAPH_LIBRARIES
    ${OSG_LIBRARY}
    ${OSGDB_LIBRARY}
    ${OSGGA_LIBRARY}
    ${OSGUTIL_LIBRARY}
    ${OSGVIEWER_LIBRARY}
    ${OSGTEXT_LIBRARY}
    ${OSGSHADOW_LIBRARY}
    ${OSGSIM_LIBRARY}
    ${OSGMANIPULATOR_LIBRARY}
    ${OPENTHREADS_LIBRARY}
)

# 禁用一些不兼容的功能
set(CMAKE_FIND_ROOT_PATH_MODE_PROGRAM NEVER)
set(CMAKE_FIND_ROOT_PATH_MODE_LIBRARY ONLY)
set(CMAKE_FIND_ROOT_PATH_MODE_INCLUDE ONLY)

# 使用EmscriptenSDL2替代系统SDL2
set(SDL2_FOUND TRUE)
set(SDL2_LIBRARIES "")
set(SDL2_INCLUDE_DIRS "${EMSCRIPTEN_ROOT_PATH}/system/include/SDL")

# 设置OpenGL ES包含路径以解决GLES2/gl2.h缺失问题
include_directories("${EMSCRIPTEN_ROOT_PATH}/system/include")
include_directories("${EMSCRIPTEN_ROOT_PATH}/system/include/GLES2")
include_directories("${EMSCRIPTEN_ROOT_PATH}/system/include/GLES3")

# 设置OpenGL变量以支持WebGL
set(OPENGL_FOUND TRUE)
set(OPENGL_opengl_LIBRARY "GL")
set(OPENGL_glx_LIBRARY "")
set(OPENGL_INCLUDE_DIR "${EMSCRIPTEN_ROOT_PATH}/system/include")
set(OPENGL_LIBRARIES "GL")
set(OPENGL_gl_LIBRARY "GL")

# 设置OpenGL缓存变量
set(OPENGL_FOUND TRUE CACHE BOOL "OpenGL found")
set(OPENGL_opengl_LIBRARY "GL" CACHE STRING "OpenGL library")
set(OPENGL_glx_LIBRARY "" CACHE STRING "GLX library")
set(OPENGL_INCLUDE_DIR "${EMSCRIPTEN_ROOT_PATH}/system/include" CACHE PATH "OpenGL include directory")
set(OPENGL_LIBRARIES "GL" CACHE STRING "OpenGL libraries")
set(OPENGL_gl_LIBRARY "GL" CACHE STRING "OpenGL GL library")

# 禁用一些在WebAssembly中不支持的功能
add_definitions(-DOSG_GL_MATRICES_AVAILABLE)
add_definitions(-DOSG_GLES2_AVAILABLE)
add_definitions(-DOSG_GLES3_AVAILABLE)
add_definitions(-DWEBGL_AVAILABLE)

# 使用优先级查找函数设置其他依赖库
find_wasm_library(CURL_LIBRARY "libcurl.a")
find_wasm_library(SQLITE3_LIBRARY "libsqlite3.a")
find_wasm_library(TIFF_LIBRARY "libtiff.a")
find_wasm_library(ASSIMP_LIBRARY "libassimp.a")

# 设置基本库的包含目录和FOUND标志
set(CURL_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
set(CURL_FOUND TRUE)

# 使用优先级查找ZLIB库
find_wasm_library(ZLIB_LIBRARY "libz.a")
set(ZLIB_FOUND TRUE)
set(ZLIB_LIBRARIES "${ZLIB_LIBRARY}")
# 设置包含目录（优先使用wasm_dep）
if(EXISTS "${WASM_INCLUDE_DIR}")
    set(ZLIB_INCLUDE_DIR "${WASM_INCLUDE_DIR}")
    set(ZLIB_INCLUDE_DIRS "${WASM_INCLUDE_DIR}")
else()
    set(ZLIB_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}")
    set(ZLIB_INCLUDE_DIRS "${VCPKG_WASM_INCLUDE_DIR}")
endif()

# 使用优先级查找GeographicLib库 - 修复路径配置
find_wasm_library(GeographicLib_LIBRARY "libGeographicLib.a")
set(GeographicLib_FOUND TRUE)
set(GeographicLib_LIBRARIES "${GeographicLib_LIBRARY}")
set(GEOGRAPHICLIB_LIBRARIES "${GeographicLib_LIBRARY}")

# 设置GeographicLib包含目录（优先使用vcpkg版本，因为它更完整）
set(GeographicLib_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}")
set(GEOGRAPHICLIB_INCLUDE_DIRS "${VCPKG_WASM_INCLUDE_DIR}")

# 使用优先级查找GEOS库 - 修复路径配置
find_wasm_library(GEOS_LIBRARY "libgeos.a")
find_wasm_library(GEOS_C_LIBRARY "libgeos_c.a")
set(GEOS_FOUND TRUE)

# 设置GEOS包含目录（优先使用vcpkg版本）
set(GEOS_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}")
set(GEOS_INCLUDE_DIRS "${VCPKG_WASM_INCLUDE_DIR}")

# 确保头文件包含路径正确
include_directories("${VCPKG_WASM_INCLUDE_DIR}")
include_directories("${VCPKG_WASM_INCLUDE_DIR}/geos")
include_directories("${VCPKG_WASM_INCLUDE_DIR}/GeographicLib")

# 禁用GDAL和PROJ库（已移除依赖）
set(GDAL_FOUND FALSE)
set(PROJ_FOUND FALSE)
set(CMAKE_DISABLE_FIND_PACKAGE_GDAL TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_PROJ TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_PROJ4 TRUE)
add_definitions(-DOSGEARTH_NO_GDAL)
add_definitions(-DOSGEARTH_NO_PROJ)

# 设置 CURL 缓存变量
set(CURL_LIBRARY "${WASM_LIB_DIR}/libcurl.a" CACHE FILEPATH "CURL library")
set(CURL_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "CURL include directory")
set(CURL_FOUND TRUE CACHE BOOL "CURL found")

# 调用函数设置包含目录（按优先级）
set_wasm_includes()

# 使用CACHE变量确保这些值在配置时被持久化
set(OSG_LIBRARY "${WASM_LIB_DIR}/libosg.a" CACHE FILEPATH "OSG library")
set(OSG_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSG include directory")
set(OSGDB_LIBRARY "${WASM_LIB_DIR}/libosgDB.a" CACHE FILEPATH "OSGDB library")
set(OSGDB_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGDB include directory")
set(OSGGA_LIBRARY "${WASM_LIB_DIR}/libosgGA.a" CACHE FILEPATH "OSGGA library")
set(OSGGA_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGGA include directory")
set(OSGUTIL_LIBRARY "${WASM_LIB_DIR}/libosgUtil.a" CACHE FILEPATH "OSGUTIL library")
set(OSGUTIL_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGUTIL include directory")
set(OSGVIEWER_LIBRARY "${WASM_LIB_DIR}/libosgViewer.a" CACHE FILEPATH "OSGVIEWER library")
set(OSGVIEWER_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGVIEWER include directory")
set(OSGTEXT_LIBRARY "${WASM_LIB_DIR}/libosgText.a" CACHE FILEPATH "OSGTEXT library")
set(OSGTEXT_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGTEXT include directory")
set(OSGSHADOW_LIBRARY "${WASM_LIB_DIR}/libosgShadow.a" CACHE FILEPATH "OSGSHADOW library")
set(OSGSHADOW_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGSHADOW include directory")
set(OSGSIM_LIBRARY "${WASM_LIB_DIR}/libosgSim.a" CACHE FILEPATH "OSGSIM library")
set(OSGSIM_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGSIM include directory")
set(OSGMANIPULATOR_LIBRARY "${WASM_LIB_DIR}/libosgManipulator.a" CACHE FILEPATH "OSGMANIPULATOR library")
set(OSGMANIPULATOR_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OSGMANIPULATOR include directory")
set(OPENTHREADS_LIBRARY "${WASM_LIB_DIR}/libOpenThreads.a" CACHE FILEPATH "OPENTHREADS library")
set(OPENTHREADS_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OPENTHREADS include directory")

# 设置FOUND标志为CACHE变量，强制设置所有必需的OSG组件
set(OSG_FOUND TRUE CACHE BOOL "OSG found")
set(OSGDB_FOUND TRUE CACHE BOOL "OSGDB found")
set(OSGGA_FOUND TRUE CACHE BOOL "OSGGA found")
set(OSGUTIL_FOUND TRUE CACHE BOOL "OSGUTIL found")
set(OSGVIEWER_FOUND TRUE CACHE BOOL "OSGVIEWER found")
set(OSGTEXT_FOUND TRUE CACHE BOOL "OSGTEXT found")
set(OSGSHADOW_FOUND TRUE CACHE BOOL "OSGSHADOW found")
set(OSGSIM_FOUND TRUE CACHE BOOL "OSGSIM found")
set(OSGMANIPULATOR_FOUND TRUE CACHE BOOL "OSGMANIPULATOR found")
set(OPENTHREADS_FOUND TRUE CACHE BOOL "OPENTHREADS found")
set(OpenSceneGraph_FOUND TRUE CACHE BOOL "OpenSceneGraph found")

# 设置OpenSceneGraph库列表为CACHE变量
set(OPENSCENEGRAPH_LIBRARIES "${OPENSCENEGRAPH_LIBRARIES}" CACHE STRING "OpenSceneGraph libraries")
set(OPENSCENEGRAPH_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "OpenSceneGraph include directory")

# 设置所有组件的CACHE变量
set(GeographicLib_LIBRARY "${VCPKG_WASM_LIB_DIR}/libGeographicLib.a" CACHE FILEPATH "GeographicLib library")
set(GeographicLib_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}" CACHE PATH "GeographicLib include directory")
set(GeographicLib_FOUND TRUE CACHE BOOL "GeographicLib found")
set(GeographicLib_LIBRARIES "${GeographicLib_LIBRARY}" CACHE STRING "GeographicLib libraries")

set(ZLIB_LIBRARY "${VCPKG_WASM_LIB_DIR}/libz.a" CACHE FILEPATH "ZLIB library")
set(ZLIB_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}" CACHE PATH "ZLIB include directory")
set(ZLIB_FOUND TRUE CACHE BOOL "ZLIB found")
set(ZLIB_LIBRARIES "${ZLIB_LIBRARY}" CACHE STRING "ZLIB libraries")

set(GEOS_LIBRARY "${VCPKG_WASM_LIB_DIR}/libgeos.a" CACHE FILEPATH "GEOS library")
set(GEOS_C_LIBRARY "${VCPKG_WASM_LIB_DIR}/libgeos_c.a" CACHE FILEPATH "GEOS C library")
set(GEOS_INCLUDE_DIR "${VCPKG_WASM_INCLUDE_DIR}" CACHE PATH "GEOS include directory")
set(GEOS_FOUND TRUE CACHE BOOL "GEOS found")

set(SQLITE3_LIBRARY "${WASM_LIB_DIR}/libsqlite3.a" CACHE FILEPATH "SQLite3 library")
set(SQLITE3_INCLUDE_DIR "${WASM_INCLUDE_DIR}" CACHE PATH "SQLite3 include directory")
set(SQLITE3_FOUND TRUE CACHE BOOL "SQLite3 found")

# 防止find_package寻找这些库
set(CMAKE_DISABLE_FIND_PACKAGE_OpenSceneGraph TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_OSG TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_ZLIB TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_GeographicLib TRUE)
set(CMAKE_DISABLE_FIND_PACKAGE_GEOS TRUE)

# 强制设置所有必需的OSG组件变量
set(OSGMANIPULATOR_LIBRARY "${WASM_LIB_DIR}/libosgManipulator.a" CACHE FILEPATH "OSGMANIPULATOR library")
set(OSGSHADOW_LIBRARY "${WASM_LIB_DIR}/libosgShadow.a" CACHE FILEPATH "OSGSHADOW library")
set(OSGSIM_LIBRARY "${WASM_LIB_DIR}/libosgSim.a" CACHE FILEPATH "OSGSIM library")
set(OSGVIEWER_LIBRARY "${WASM_LIB_DIR}/libosgViewer.a" CACHE FILEPATH "OSGVIEWER library")
set(OSGGA_LIBRARY "${WASM_LIB_DIR}/libosgGA.a" CACHE FILEPATH "OSGGA library")
set(OSGUTIL_LIBRARY "${WASM_LIB_DIR}/libosgUtil.a" CACHE FILEPATH "OSGUTIL library")
set(OSGTEXT_LIBRARY "${WASM_LIB_DIR}/libosgText.a" CACHE FILEPATH "OSGTEXT library")
set(OSGDB_LIBRARY "${WASM_LIB_DIR}/libosgDB.a" CACHE FILEPATH "OSGDB library")
set(OSG_LIBRARY "${WASM_LIB_DIR}/libosg.a" CACHE FILEPATH "OSG library")
set(OPENTHREADS_LIBRARY "${WASM_LIB_DIR}/libOpenThreads.a" CACHE FILEPATH "OPENTHREADS library")

# 设置输出目录 - 支持自定义输出目录
if(NOT CMAKE_RUNTIME_OUTPUT_DIRECTORY)
    set(CMAKE_RUNTIME_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm)
endif()
if(NOT CMAKE_LIBRARY_OUTPUT_DIRECTORY)
    set(CMAKE_LIBRARY_OUTPUT_DIRECTORY ${CMAKE_CURRENT_SOURCE_DIR}/redist_wasm)
endif()

# 禁用不需要的功能
set(OSGEARTH_BUILD_EXAMPLES OFF)
set(OSGEARTH_BUILD_TOOLS OFF)
set(OSGEARTH_BUILD_TESTS OFF)
set(OSGEARTH_BUILD_DOCS OFF)
set(OSGEARTH_BUILD_SHARED_LIBS OFF)

# 禁用在WebAssembly中不兼容的驱动程序
set(OSGEARTH_ENABLE_FASTDXT_DRIVER OFF)
set(BUILD_OSGEARTH_DRIVER_FASTDXT OFF)
add_definitions(-DOSGEARTH_DISABLE_FASTDXT)

# WebAssembly特定定义
add_definitions(-DOSGEARTH_WEBASSEMBLY)
add_definitions(-DEMSCRIPTEN)
add_definitions(-DUSE_WEBGL)
add_definitions(-DUSE_SDL2)

# 禁用CURL，使用WebAssembly HTTP客户端
add_definitions(-DOSGEARTH_USE_WEBASSEMBLY_HTTP)
set(CMAKE_DISABLE_FIND_PACKAGE_CURL TRUE)

# 包含WebGL兼容性头文件
include_directories(${CMAKE_CURRENT_SOURCE_DIR}/cmake)
add_definitions(-DUSE_WEBGL_COMPATIBILITY)

# 设置WebAssembly变量以触发主CMakeLists.txt中的特殊处理
set(OSGEARTH_WEBASSEMBLY ON CACHE BOOL "WebAssembly build")
set(EMSCRIPTEN ON CACHE BOOL "Emscripten build")

# 禁用不兼容的功能
add_definitions(-DOSGEARTH_NO_THREADING)

# 设置库目录
link_directories(${WASM_LIB_DIR})
link_directories(${VCPKG_WASM_LIB_DIR})
link_directories(${OSG_PLUGINS_DIR})

# 直接设置 find_package 需要的变量，不要禁用 find_package
# 相反，我们预先设置所有需要的变量，让 find_package 能够找到它们
set(OpenSceneGraph_FOUND TRUE)
set(OpenSceneGraph_LIBRARIES "${OPENSCENEGRAPH_LIBRARIES}")
set(OpenSceneGraph_INCLUDE_DIRS "${WASM_INCLUDE_DIR}")

# 设置所有组件为 FOUND
set(osg_FOUND TRUE)
set(osgDB_FOUND TRUE)
set(osgGA_FOUND TRUE)
set(osgUtil_FOUND TRUE)
set(osgViewer_FOUND TRUE)
set(osgText_FOUND TRUE)
set(osgShadow_FOUND TRUE)
set(osgSim_FOUND TRUE)
set(osgManipulator_FOUND TRUE)
set(OpenThreads_FOUND TRUE) 