/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/ImageLayer>
#include <osgEarth/Geometry>
#include <osgEarth/Map>
#include <osgText/Font>

using namespace osgEarth;

namespace osgEarth { namespace Util
{
    /**
     * Image layer displaying TileKey information
     */
    class OSGEARTH_EXPORT DebugImageLayer : public ImageLayer
    {
    public:
        class OSGEARTH_EXPORT Options : public ImageLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, ImageLayer::Options);
            OE_OPTION(std::string, colorCode, "#000000");
            OE_OPTION(bool, invertY, false);
            OE_OPTION(bool, showTessellation, false);
            virtual Config getConfig() const;
            static Config getMetadata();
        private:
            void fromConfig( const Config& conf );
        };

    public:
        META_Layer(osgEarth, DebugImageLayer, Options, ImageLayer, DebugImage);        

    public: // Layer

        void init() override;
        Status openImplementation() override;
        void addedToMap(const Map* map) override;
        GeoImage createImageImplementation(const TileKey& key, ProgressCallback* progress) const override;

        //! Allow the user to directly set the profile before opening the layer
        void setProfile(const Profile* profile) override;

    protected:

        //! Destructor
        virtual ~DebugImageLayer() { }

        osg::ref_ptr<osgEarth::Geometry> _geom;
        osg::ref_ptr<osgText::Font> _font;
        osg::Vec4f _color;
        osg::ref_ptr<const osg::Image> _tessImage;
    };

} }

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Util::DebugImageLayer::Options);
