<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>基础功能测试</title>
    <style>
        body {
            font-family: '<PERSON>solas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: '<PERSON>sol<PERSON>', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-success { color: #90ee90; }
        .test-button {
            background-color: #007acc;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 14px;
        }
        .test-button:hover {
            background-color: #005a9e;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 基础功能测试</h1>
        
        <div id="status" class="status">状态: 准备测试...</div>
        
        <div class="status">
            <h3>🔍 环境检查</h3>
            <div id="env-check">检查中...</div>
        </div>
        
        <div class="status">
            <h3>🧵 多线程支持</h3>
            <div id="thread-check">检查中...</div>
        </div>
        
        <div class="status">
            <h3>🎮 测试选项</h3>
            <button id="test-simple" class="test-button">测试简单pthread</button>
            <button id="test-minimal" class="test-button">测试最小化osgEarth</button>
            <button id="test-full" class="test-button">测试完整osgEarth</button>
        </div>
        
        <div class="log">
            <h3>📝 测试日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // 检查环境
        function checkEnvironment() {
            const envCheck = document.getElementById('env-check');
            let html = '';
            
            // 基础检查
            html += `✅ <strong>浏览器:</strong> ${navigator.userAgent.split(' ')[0]}<br>`;
            html += `✅ <strong>WebAssembly:</strong> ${typeof WebAssembly !== 'undefined' ? '支持' : '不支持'}<br>`;
            html += `✅ <strong>Worker:</strong> ${typeof Worker !== 'undefined' ? '支持' : '不支持'}<br>`;
            
            envCheck.innerHTML = html;
            addLog('环境检查完成', 'success');
        }
        
        // 检查多线程支持
        function checkThreadSupport() {
            const threadCheck = document.getElementById('thread-check');
            let html = '';
            let hasSupport = true;
            
            // SharedArrayBuffer检查
            if (typeof SharedArrayBuffer !== 'undefined') {
                html += '✅ <strong>SharedArrayBuffer:</strong> 支持<br>';
                addLog('SharedArrayBuffer: 支持', 'success');
            } else {
                html += '❌ <strong>SharedArrayBuffer:</strong> 不支持<br>';
                addLog('SharedArrayBuffer: 不支持', 'error');
                hasSupport = false;
            }
            
            // Atomics检查
            if (typeof Atomics !== 'undefined') {
                html += '✅ <strong>Atomics:</strong> 支持<br>';
                addLog('Atomics: 支持', 'success');
            } else {
                html += '❌ <strong>Atomics:</strong> 不支持<br>';
                addLog('Atomics: 不支持', 'error');
                hasSupport = false;
            }
            
            if (!hasSupport) {
                html += '<br>⚠️ <strong>注意:</strong> 多线程功能可能不可用<br>';
                html += '请检查HTTP头设置和浏览器支持<br>';
            }
            
            threadCheck.innerHTML = html;
            
            if (hasSupport) {
                updateStatus('多线程环境检查通过', false, true);
            } else {
                updateStatus('多线程环境检查失败', true);
            }
        }
        
        // 测试函数
        function testSimple() {
            addLog('启动简单pthread测试...', 'info');
            window.open('test_simple_pthread.html', '_blank');
        }
        
        function testMinimal() {
            addLog('启动最小化osgEarth测试...', 'info');
            window.open('test_minimal.html', '_blank');
        }
        
        function testFull() {
            addLog('启动完整osgEarth测试...', 'info');
            window.open('test_multithreaded.html', '_blank');
        }
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始基础功能测试', 'info');
            
            // 检查环境
            checkEnvironment();
            
            // 检查多线程支持
            checkThreadSupport();
            
            // 绑定按钮事件
            document.getElementById('test-simple').addEventListener('click', testSimple);
            document.getElementById('test-minimal').addEventListener('click', testMinimal);
            document.getElementById('test-full').addEventListener('click', testFull);
            
            updateStatus('基础功能测试就绪');
            addLog('所有测试选项已准备就绪', 'success');
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
