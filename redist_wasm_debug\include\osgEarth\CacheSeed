/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTH_CACHE_SEED_H
#define OSGEARTH_CACHE_SEED_H 1

#include <osgEarth/Common>
#include <osgEarth/TileKey>
#include <osgEarth/TileVisitor>

namespace osgEarth {
    class Map;
}

namespace osgEarth { namespace Contrib
{
    using namespace osgEarth::Util;

    /**
    * A TileHandler that caches tiles for the given layer.
    */
    class OSGEARTH_EXPORT CacheTileHandler : public TileHandler
    {
    public:
        CacheTileHandler( TileLayer* layer, const Map* map );
        virtual bool handleTile( const TileKey& key, const TileVisitor& tv );
        virtual bool hasData( const TileKey& key ) const;

        virtual std::string getProcessString() const;

    protected:
        osg::ref_ptr< TileLayer > _layer;
        osg::ref_ptr< const Map > _map;
    };    

    /**
    * Utility class for seeding a cache
    */
    class OSGEARTH_EXPORT CacheSeed
    {
    public:
        CacheSeed();

        /**
        * Gets the TileVisitor to use when seeding the cache.  Use this to set traversal options like the extents, levels, etc.
        */
        TileVisitor* getVisitor() const;

        /**
        * Sets the TileVisitor to use when seeding the cache.  Must be configured BEFORE you call run
        */
        void setVisitor(TileVisitor* visitor);

        /**
        * Seeds a TileLayer
        */
        void run(TileLayer* layer, const Map* map );


    protected:

        osg::ref_ptr< TileVisitor > _visitor;
    };
} }

#endif //OSGEARTH_CACHE_SEED_H
