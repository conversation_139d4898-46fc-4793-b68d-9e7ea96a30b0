/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_ECEF_H
#define OSGEARTH_ECEF_H 1

#include <osgEarth/Common>
#include <osgEarth/SpatialReference>
#include <osg/Matrix>

namespace osgEarth { namespace Util
{
    struct OSGEARTH_EXPORT ECEF
    {
        /**
         * Transforms a point into ECEF coordinates, localizes it with
         * the provided world2local matrix, and puts the result in "output".
         */
        static bool transformAndLocalize(
            const osg::Vec3d&       input,
            const SpatialReference* inputSRS,
            osg::Vec3d&             output,
            const SpatialReference* outputSRS,
            const osg::Matrixd&     world2local =osg::Matrixd() );

        /**
         * Transforms the points in "input" to ECEF coordinates, localizes them with
         * the provided world2local matrix, and puts the result in "output".
         */
        static bool transformAndLocalize(
            const std::vector<osg::Vec3d>& input,
            const SpatialReference*        inputSRS,
            osg::Vec3Array*                output,
            const SpatialReference*        outputSRS,
            const osg::Matrixd&            world2local =osg::Matrixd() );

        /**
         * Transforms the points in "input" to ECEF coordinates, localizes them with
         * the provided world2local matrix, and puts the resulting verts in "out_verts"
         * and the resulting localized normals in "out_normals" (if "out_normals" is
         * non-NULL).
         */
        static bool transformAndLocalize(
            const std::vector<osg::Vec3d>& input,
            const SpatialReference*        inputSRS,
            osg::Vec3Array*                out_verts,
            osg::Vec3Array*                out_normals,
            const SpatialReference*        outputSRS,
            const osg::Matrixd&            world2local =osg::Matrixd() );

        /**
         * Transforms a point to ECEF, and at the same time returns a quaternion that
         * rotates the point into the local tangent place at that point.
         */
        static bool transformAndGetRotationMatrix(
            const osg::Vec3d&       input,
            const SpatialReference* inputSRS,
            osg::Vec3d&             out_ecef_point,
            const SpatialReference* outputSRS,
            osg::Matrixd&           out_rotation );
    };
} }

#endif // OSGEARTH_ECEF_H
