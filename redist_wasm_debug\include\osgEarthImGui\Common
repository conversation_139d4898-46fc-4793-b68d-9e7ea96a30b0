#pragma once

#if defined(_MSC_VER)
#define NOMINMAX
#pragma warning( disable : 4244 )
#pragma warning( disable : 4251 )
#pragma warning( disable : 4267 )
#pragma warning( disable : 4275 )
#pragma warning( disable : 4290 )
#pragma warning( disable : 4786 )
#pragma warning( disable : 4305 )
#pragma warning( disable : 4996 )
#endif

// note: No Windows declspec's here because we are using CMAKE_WINDOWS_EXPORT_ALL_SYMBOLS

#ifndef IMGUI_VERSION
#include <osgEarthImGui/imgui.h>
#include <osgEarthImGui/imnodes.h>
#endif
