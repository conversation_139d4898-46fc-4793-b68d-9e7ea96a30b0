#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
WebAssembly编译结果测试验证脚本
检查编译输出和库文件完整性
"""

import os
import sys

def check_file_exists(filepath, description):
    """检查文件是否存在"""
    if os.path.exists(filepath):
        size = os.path.getsize(filepath)
        print(f"✅ {description}: {filepath} ({size:,} bytes)")
        return True
    else:
        print(f"❌ {description}: {filepath} (不存在)")
        return False

def check_directory_exists(dirpath, description):
    """检查目录是否存在"""
    if os.path.exists(dirpath) and os.path.isdir(dirpath):
        files = len([f for f in os.listdir(dirpath) if os.path.isfile(os.path.join(dirpath, f))])
        print(f"✅ {description}: {dirpath} ({files} 个文件)")
        return True
    else:
        print(f"❌ {description}: {dirpath} (不存在)")
        return False

def main():
    print("🔍 WebAssembly编译结果验证")
    print("==========================")
    
    base_dir = "F:\\cmo-dev\\my_osgearth_web"
    install_dir = f"{base_dir}\\osgearth_third_party\\wasm_dep"
    
    print(f"检查安装目录: {install_dir}")
    
    success = True
    
    # 检查目录结构
    print("\n📁 目录结构检查:")
    dirs_to_check = [
        (f"{install_dir}", "安装根目录"),
        (f"{install_dir}\\include", "头文件目录"),
        (f"{install_dir}\\lib", "库文件目录"),
        (f"{install_dir}\\include\\osg", "OSG头文件目录"),
        (f"{install_dir}\\include\\osgEarth", "osgEarth头文件目录"),
        (f"{install_dir}\\include\\weejobs", "weejobs头文件目录"),
    ]
    
    for dirpath, description in dirs_to_check:
        if not check_directory_exists(dirpath, description):
            success = False
    
    # 检查关键头文件
    print("\n📄 关键头文件检查:")
    headers_to_check = [
        (f"{install_dir}\\include\\osg\\Node", "OSG核心头文件"),
        (f"{install_dir}\\include\\osg\\Geode", "OSG几何节点头文件"),
        (f"{install_dir}\\include\\osgViewer\\Viewer", "OSG查看器头文件"),
        (f"{install_dir}\\include\\osgEarth\\Map", "osgEarth地图头文件"),
        (f"{install_dir}\\include\\osgEarth\\MapNode", "osgEarth地图节点头文件"),
        (f"{install_dir}\\include\\osgEarth\\EarthManipulator", "osgEarth操作器头文件"),
        (f"{install_dir}\\include\\weejobs\\weejobs.h", "weejobs线程池头文件"),
    ]
    
    for filepath, description in headers_to_check:
        if not check_file_exists(filepath, description):
            success = False
    
    # 检查库文件
    print("\n📚 库文件检查:")
    libs_to_check = [
        (f"{install_dir}\\lib\\libosg.a", "OSG核心库"),
        (f"{install_dir}\\lib\\libosgViewer.a", "OSG查看器库"),
        (f"{install_dir}\\lib\\libosgGA.a", "OSG图形适配器库"),
        (f"{install_dir}\\lib\\libosgDB.a", "OSG数据库库"),
        (f"{install_dir}\\lib\\libosgUtil.a", "OSG工具库"),
        (f"{install_dir}\\lib\\libosgEarth.a", "osgEarth核心库"),
        (f"{install_dir}\\lib\\libosgEarthUtil.a", "osgEarth工具库"),
    ]
    
    for filepath, description in libs_to_check:
        if not check_file_exists(filepath, description):
            success = False
    
    # 检查编译输出
    print("\n🌍 应用程序编译输出检查:")
    app_dir = f"{base_dir}\\osgearth_simple2\\myosgearth\\redist_wasm1"
    app_files_to_check = [
        (f"{app_dir}\\osgearth_basic_earth.js", "基础数字地球JS文件"),
        (f"{app_dir}\\osgearth_basic_earth.wasm", "基础数字地球WASM文件"),
        (f"{app_dir}\\osgearth_basic_earth.html", "基础数字地球HTML页面"),
    ]
    
    for filepath, description in app_files_to_check:
        if not check_file_exists(filepath, description):
            success = False
    
    # 总结
    print(f"\n{'='*50}")
    if success:
        print("🎉 所有检查通过！WebAssembly编译成功")
        print("\n下一步:")
        print("1. 启动HTTP服务器:")
        print(f"   cd {app_dir}")
        print("   python server.py 8080")
        print("\n2. 在浏览器中访问:")
        print("   http://localhost:8080/osgearth_basic_earth.html")
        print("\n3. 验证功能:")
        print("   - 地球纹理显示")
        print("   - 经纬度网格线")
        print("   - 鼠标交互控制")
        print("   - 键盘快捷键")
    else:
        print("❌ 检查发现问题，请重新编译")
        print("\n建议:")
        print("1. 检查源码是否正确下载")
        print("2. 检查Emscripten是否正确安装")
        print("3. 重新运行编译脚本:")
        print("   python build_all_wasm.py")
    
    print(f"{'='*50}")
    
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
