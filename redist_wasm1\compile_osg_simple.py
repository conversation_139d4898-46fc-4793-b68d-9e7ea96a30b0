#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的OSG和osgEarth WebAssembly测试程序编译器
用于测试编译环境和基础功能
"""

import os
import subprocess

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🚀 简化OSG和osgEarth WebAssembly测试编译器")
    print("==========================================")
    
    # 检查Emscripten
    emcc_check = """C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"""
    
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return
    
    # 创建模拟的OSG球体测试程序（不依赖真实OSG库）
    mock_osg_cmd = """C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_earth_canvas.cpp -o test_osg_sphere.js -s USE_SDL=2 -s ALLOW_MEMORY_GROWTH=1 -O2"""
    
    if run_command(mock_osg_cmd, "编译模拟OSG球体测试程序"):
        print("✅ 模拟OSG球体测试程序编译成功")
    else:
        print("❌ 模拟OSG球体测试程序编译失败")
    
    # 创建模拟的osgEarth测试程序（不依赖真实osgEarth库）
    mock_osgearth_cmd = """C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_earth_canvas.cpp -o test_osgearth_full.js -s USE_SDL=2 -s ALLOW_MEMORY_GROWTH=1 -O2"""
    
    if run_command(mock_osgearth_cmd, "编译模拟osgEarth测试程序"):
        print("✅ 模拟osgEarth测试程序编译成功")
    else:
        print("❌ 模拟osgEarth测试程序编译失败")
    
    print("\n🎯 编译完成！")
    print("测试地址:")
    print("  • 模拟OSG球体测试: http://localhost:8080/test_osg_sphere.html")
    print("  • 模拟osgEarth完整测试: http://localhost:8080/test_osgearth_full.html")
    print("\n📝 说明:")
    print("  • 当前使用Canvas 2D程序模拟OSG和osgEarth功能")
    print("  • 这是为了测试编译环境和页面框架")
    print("  • 真实的OSG/osgEarth库需要单独编译为WebAssembly版本")
    print("  • 可以验证页面布局、事件处理和基础渲染功能")

if __name__ == "__main__":
    main()
