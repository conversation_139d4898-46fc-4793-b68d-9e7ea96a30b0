<!DOCTYPE html>
<html lang="en">

<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>osgEarth WebAssembly - New Build Test</title>
  <style>
    body {
      margin: 0;
      padding: 0;
      background-color: #000;
      font-family: Arial, sans-serif;
      overflow: hidden;
    }

    #canvas {
      display: block;
      width: 100vw;
      height: 100vh;
      background-color: #111;
    }

    #debug {
      position: absolute;
      top: 10px;
      left: 10px;
      color: #0f0;
      background: rgba(0, 0, 0, 0.9);
      padding: 10px;
      border-radius: 5px;
      font-size: 10px;
      z-index: 1000;
      max-width: 400px;
      max-height: 300px;
      overflow-y: auto;
    }

    #status {
      position: absolute;
      top: 10px;
      right: 10px;
      color: #fff;
      background: rgba(0, 0, 0, 0.8);
      padding: 5px 10px;
      border-radius: 3px;
      font-size: 12px;
      z-index: 1000;
    }

    .info {
      color: #0f0;
    }

    .warning {
      color: #ff0;
    }

    .error {
      color: #f00;
    }
  </style>
</head>

<body>
  <canvas id="canvas"></canvas>
  <div id="debug"></div>
  <div id="status">Initializing...</div>

  <script>
    // 调试函数
    function logToDebug(message, type = 'info') {
      const debugDiv = document.getElementById('debug');
      const timestamp = new Date().toLocaleTimeString();
      const line = document.createElement('div');
      line.className = type;
      line.textContent = `[${timestamp}] ${message}`;
      debugDiv.appendChild(line);
      debugDiv.scrollTop = debugDiv.scrollHeight;
      console.log(`[${type}] ${message}`);
    }

    // 状态更新函数
    function updateStatus(message) {
      document.getElementById('status').textContent = message;
      logToDebug(message, 'info');
    }

    // WebGL支持检查
    function checkWebGLSupport() {
      try {
        const canvas = document.createElement('canvas');
        const gl = canvas.getContext('webgl') || canvas.getContext('experimental-webgl');
        if (!gl) {
          throw new Error('WebGL not supported');
        }

        logToDebug('WebGL supported', 'info');
        logToDebug(`WebGL Version: ${gl.getParameter(gl.VERSION)}`, 'info');
        logToDebug(`WebGL Vendor: ${gl.getParameter(gl.VENDOR)}`, 'info');
        logToDebug(`WebGL Renderer: ${gl.getParameter(gl.RENDERER)}`, 'info');

        return true;
      } catch (error) {
        logToDebug(`WebGL Error: ${error.message}`, 'error');
        return false;
      }
    }

    // 初始化
    logToDebug('Starting osgEarth WebAssembly Application (New Build)', 'info');

    if (!checkWebGLSupport()) {
      updateStatus('WebGL not supported');
      document.getElementById('canvas').style.display = 'none';
      throw new Error('WebGL support required');
    }

    updateStatus('Loading WebAssembly...');

    // Emscripten 配置 - 移除运行时内存设置
    var Module = {
      preRun: [],
      postRun: [],
      canvas: document.getElementById('canvas'),

      // WebGL 上下文配置
      preinitializedWebGLContext: null,

      // 移除运行时内存配置，这些应该在编译时设置
      // INITIAL_MEMORY: 128*1024*1024,  // 不能在运行时设置
      // MAXIMUM_MEMORY: 512*1024*1024,  // 不能在运行时设置

      // 错误处理
      printErr: function (text) {
        logToDebug(`stderr: ${text}`, 'error');
      },

      print: function (text) {
        logToDebug(`stdout: ${text}`, 'info');
      },

      setStatus: function (text) {
        updateStatus(text);
      },

      totalDependencies: 0,
      monitorRunDependencies: function (left) {
        this.totalDependencies = Math.max(this.totalDependencies, left);
        if (left == 0) {
          updateStatus('All dependencies loaded');
          logToDebug('WebAssembly module fully loaded', 'info');
        } else {
          updateStatus(`Loading dependencies: ${left}/${this.totalDependencies}`);
        }
      },

      // 异常处理
      onAbort: function (what) {
        logToDebug(`Application aborted: ${what}`, 'error');
        updateStatus('Application aborted');
      },

      onRuntimeInitialized: function () {
        logToDebug('Runtime initialized successfully', 'info');
        updateStatus('Runtime Ready');
      }
    };

    // 异常处理
    window.onerror = function (message, source, lineno, colno, error) {
      logToDebug(`JavaScript Error: ${message}`, 'error');
      logToDebug(`Source: ${source}:${lineno}:${colno}`, 'error');
      return false;
    };

    // 未捕获的Promise异常
    window.addEventListener('unhandledrejection', function (event) {
      logToDebug(`Unhandled Promise Rejection: ${event.reason}`, 'error');
      event.preventDefault();
    });

    // 加载WebAssembly模块
    logToDebug('Loading WebAssembly module...', 'info');

  </script>

  <!-- 加载WebAssembly模块 -->
  <script src="bin/osgearth_myviewer.js"></script>
</body>

</html>