
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:85 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-vyrs4v"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-vyrs4v"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-vyrs4v
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_ee592 && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_ee592.dir/src.c.o -MF CMakeFiles\\cmTC_ee592.dir\\src.c.o.d -o CMakeFiles/cmTC_ee592.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-vyrs4v/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_ee592.dir/src.c.o -o cmTC_ee592.js   && cd ."
        
      exitCode: 0
...

---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CMakeDetermineSystem.cmake:199 (message)"
      - "CMakeLists.txt:13 (project)"
    message: |
      The target system is: Emscripten - 1 - x86
      The host system is: Windows - 10.0.26058 - AMD64
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/CheckCSourceCompiles.cmake:76 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:97 (CHECK_C_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.26/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "cmake/oe_unix.cmake:13 (find_package)"
      - "CMakeLists.txt:85 (include)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-7m1f4z"
      binary: "F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-7m1f4z"
    cmakeVariables:
      CMAKE_C_FLAGS: ""
      CMAKE_C_FLAGS_DEBUG: "-g"
      CMAKE_EXE_LINKER_FLAGS: ""
      CMAKE_MODULE_PATH: "C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;C:/dev/emsdk/upstream/emscripten/cmake/Modules;F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/cmake"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-7m1f4z
        
        Run Build Command(s):C:/Windows/System32/ninja.exe -v cmTC_8241c && [1/2] C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat -DCMAKE_HAVE_LIBC_PTHREAD   -MD -MT CMakeFiles/cmTC_8241c.dir/src.c.o -MF CMakeFiles\\cmTC_8241c.dir\\src.c.o.d -o CMakeFiles/cmTC_8241c.dir/src.c.o -c F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm_debug/CMakeFiles/CMakeScratch/TryCompile-7m1f4z/src.c
        [2/2] cmd.exe /C "cd . && C:\\dev\\emsdk\\upstream\\emscripten\\emcc.bat   CMakeFiles/cmTC_8241c.dir/src.c.o -o cmTC_8241c.js   && cd ."
        
      exitCode: 0
...
