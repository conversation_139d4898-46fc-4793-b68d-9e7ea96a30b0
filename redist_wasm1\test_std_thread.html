<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>std::thread创建测试</title>
    <style>
        body {
            font-family: 'Consol<PERSON>', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 400px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
        .test-item {
            background-color: #3a3a3a;
            padding: 10px;
            margin: 8px 0;
            border-radius: 5px;
            border-left: 4px solid #007acc;
        }
        .test-item.success { border-left-color: #44ff44; }
        .test-item.error { border-left-color: #ff4444; }
        .test-item.running { border-left-color: #ffa500; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧵 std::thread创建测试</h1>
        
        <div id="status" class="status">状态: 准备测试std::thread创建...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>这是最底层的线程测试，直接测试std::thread的创建和执行：</p>
            <ul>
                <li><strong>测试1</strong>: 创建单个std::thread</li>
                <li><strong>测试2</strong>: 创建多个std::thread</li>
                <li><strong>测试3</strong>: 创建lambda线程（模拟osgEarth方式）</li>
            </ul>
            <p><strong>如果这些测试失败，说明问题在WebAssembly的pthread支持层面。</strong></p>
        </div>
        
        <div class="status">
            <h3>📊 测试结果</h3>
            <div id="test-results"></div>
        </div>
        
        <div class="log">
            <h3>📝 详细日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        let testResults = [];
        
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        function addTestResult(testName, status = 'pending') {
            const resultsEl = document.getElementById('test-results');
            const testId = `test-${testResults.length}`;
            
            testResults.push({id: testId, name: testName, status: status});
            
            const div = document.createElement('div');
            div.id = testId;
            div.className = `test-item ${status}`;
            
            let statusIcon = '⏳';
            if (status === 'success') statusIcon = '✅';
            else if (status === 'error') statusIcon = '❌';
            else if (status === 'running') statusIcon = '🔄';
            
            div.innerHTML = `${statusIcon} <strong>${testName}</strong>`;
            resultsEl.appendChild(div);
        }
        
        function updateTestResult(testIndex, status, details = '') {
            const test = testResults[testIndex];
            if (test) {
                test.status = status;
                const testEl = document.getElementById(test.id);
                if (testEl) {
                    testEl.className = `test-item ${status}`;
                    
                    let statusIcon = '⏳';
                    if (status === 'success') statusIcon = '✅';
                    else if (status === 'error') statusIcon = '❌';
                    else if (status === 'running') statusIcon = '🔄';
                    
                    let content = `${statusIcon} <strong>${test.name}</strong>`;
                    if (details) {
                        content += `<br><small>${details}</small>`;
                    }
                    
                    testEl.innerHTML = content;
                }
            }
        }
        
        // Module配置
        var Module = {
            print: function(text) {
                console.log('std::thread Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 解析测试进度
                if (text.includes('测试1:')) {
                    updateTestResult(0, 'running');
                } else if (text.includes('测试2:')) {
                    updateTestResult(1, 'running');
                } else if (text.includes('测试3:')) {
                    updateTestResult(2, 'running');
                }
                
                // 检查成功标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                    
                    if (text.includes('单线程测试成功')) {
                        updateTestResult(0, 'success', '单个std::thread创建和执行成功');
                    } else if (text.includes('多线程测试成功')) {
                        const match = text.match(/最终计数:\s*(\d+)/);
                        const count = match ? match[1] : '未知';
                        updateTestResult(1, 'success', `多个std::thread创建成功，计数: ${count}`);
                    } else if (text.includes('Lambda线程测试成功')) {
                        updateTestResult(2, 'success', 'Lambda线程创建和执行成功');
                    }
                }
                
                // 检查错误标记
                if (text.includes('❌')) {
                    addLog(text, 'error');
                    
                    if (text.includes('单线程测试失败')) {
                        updateTestResult(0, 'error', '单个std::thread创建失败');
                    } else if (text.includes('多线程测试失败')) {
                        updateTestResult(1, 'error', '多个std::thread创建失败');
                    } else if (text.includes('Lambda线程测试失败')) {
                        updateTestResult(2, 'error', 'Lambda线程创建失败');
                    }
                }
                
                // 检查系统错误
                if (text.includes('std::system_error')) {
                    addLog('检测到系统错误：这通常表示pthread支持问题', 'error');
                }
            },
            
            printErr: function(text) {
                console.error('std::thread Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('std::thread测试模块初始化完成');
                addLog('std::thread测试模块初始化完成', 'success');
                updateStatus('std::thread测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('std::thread测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
                
                // 标记当前运行的测试为失败
                for (let i = 0; i < testResults.length; i++) {
                    if (testResults[i].status === 'running') {
                        updateTestResult(i, 'error', '模块中止');
                        break;
                    }
                }
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                
                if (status === 0) {
                    updateStatus('🎉 所有std::thread测试通过！', false, true);
                    addLog('std::thread功能正常，osgEarth问题可能在其他地方', 'success');
                } else {
                    updateStatus('❌ std::thread测试失败', true);
                    addLog('这解释了osgEarth线程池创建失败的原因', 'error');
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始std::thread测试', 'info');
            
            // 初始化测试结果
            addTestResult('测试1: 创建单个std::thread');
            addTestResult('测试2: 创建多个std::thread');
            addTestResult('测试3: 创建lambda线程');
            
            updateStatus('加载std::thread测试模块...');
            addLog('开始加载std::thread测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_std_thread.js';
            script.onload = function() {
                addLog('std::thread测试脚本加载成功', 'success');
                updateStatus('std::thread测试脚本已加载');
            };
            script.onerror = function() {
                addLog('std::thread测试脚本加载失败', 'error');
                updateStatus('std::thread测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
