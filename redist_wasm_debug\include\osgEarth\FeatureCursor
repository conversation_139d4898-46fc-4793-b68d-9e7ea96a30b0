/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Filter>
#include <osgEarth/Progress>
#include <osgEarth/Profile>
#include <functional>

namespace osgEarth
{
    /**
     * A cursor that lets you iterate over a collection of features returned 
     * from a feature query performed on a FeatureStore.
     */
    class OSGEARTH_EXPORT FeatureCursor : public osg::Referenced
    {
    public:
        //! Whether a call to nextFeature() will return a valid feature
        virtual bool hasMore() const =0;

        //! The next feature, or nullptr if empty
        virtual Feature* nextFeature() =0;

        //! Copy all features to the list, returning the size.
        unsigned fill(FeatureList& output);

        //! Copy all features to the list that pass the predicate, returning the size.
        unsigned fill(FeatureList& output, std::function<bool(const Feature*)> predicate);

        //! Progress callback to check for cancelation
        ProgressCallback* getProgress() const { return _progress.get(); }

    protected:

        FeatureCursor(ProgressCallback* progress = nullptr);

        virtual ~FeatureCursor();

        osg::ref_ptr<ProgressCallback> _progress;
    };

    /**
     * A simple cursor implementation that returns features from an in-memory
     * feature list.
     */
    class OSGEARTH_EXPORT FeatureListCursor : public FeatureCursor
    {
    public:
        FeatureListCursor(const FeatureList& input) :
            _features(input),
            _iter(_features.begin()) { }

        FeatureListCursor(FeatureList&& rhs) noexcept {
            _features.swap(rhs);
            _iter = _features.begin();
            rhs.clear();
        }

    public: // FeatureCursor
        bool hasMore() const override {
            return _iter != _features.end();
        }

        Feature* nextFeature() override {
            return (_iter++)->get();
        }

    protected:
        FeatureList _features;
        FeatureList::iterator _iter;
    };

    /**
     * A simple cursor that returns each Geometry wrapped in a feature.
     */
    class OSGEARTH_EXPORT GeometryFeatureCursor : public FeatureCursor
    {
    public:
        GeometryFeatureCursor( Geometry* geom );
        GeometryFeatureCursor( Geometry* geom, const FeatureProfile* fp, const FeatureFilterChain& filters );

    public: // FeatureCursor
        virtual bool hasMore() const;
        virtual Feature* nextFeature();

    protected:
        virtual ~GeometryFeatureCursor();
        osg::ref_ptr<Geometry> _geom;
        osg::ref_ptr<const FeatureProfile> _featureProfile;
        FeatureFilterChain _filterChain;
        osg::ref_ptr<Feature> _lastFeature;
    };

} // namespace osgEarth

