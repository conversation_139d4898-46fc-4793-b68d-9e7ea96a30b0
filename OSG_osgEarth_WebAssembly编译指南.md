# OSG和osgEarth WebAssembly编译指南

## 概述

本指南详细说明如何将OSG（OpenSceneGraph）和osgEarth库编译为WebAssembly版本，以支持在浏览器中运行3D地球应用。

## 🚀 快速开始

### 一键编译（推荐）

```bash
# 编译所有库和应用程序
python build_all_wasm.py
```

这个脚本会按顺序执行：
1. 编译weejobs线程池库
2. 编译OSG库
3. 编译osgEarth库
4. 编译基础数字地球应用

### 分步编译

如果需要单独编译某个组件：

```bash
# 1. 编译weejobs线程池库
python build_weejobs_wasm.py

# 2. 编译OSG库
python build_osg_wasm.py

# 3. 编译osgEarth库
python build_osgearth_wasm.py

# 4. 编译数字地球应用
python redist_wasm1/compile_earth_basic.py
```

## 测试进度

### ✅ 已完成的测试

1. **weejobs多线程系统** - 完全成功
   - 基础线程创建和执行
   - std::async任务分发
   - 简化线程池功能
   - WebAssembly多线程环境兼容性

2. **SDL2图形系统** - 完全成功
   - SDL2初始化和窗口创建
   - 2D渲染器功能
   - 事件处理系统
   - 动画渲染循环

3. **Canvas 2D数字地球** - 完全成功
   - 2D球面投影渲染
   - 经纬网格系统
   - 大陆轮廓显示
   - 交互式缩放控制

### 🔄 当前阶段

4. **OSG球体测试** - 框架就绪，需要真实OSG库
5. **osgEarth完整数字地球** - 框架就绪，需要真实osgEarth库

## 📋 编译前提条件

### 环境要求

1. **Emscripten SDK**
   - 安装路径：`C:\dev\emsdk`
   - 版本：3.1.0或更高

2. **源码准备**
   ```
   F:\cmo-dev\my_osgearth_web\
   ├── OpenSceneGraph\          # OSG源码
   ├── osgearth_origin\
   │   └── osgearth\           # osgEarth源码
   └── osgearth_third_party\
       └── wasm_dep\           # 编译输出目录
   ```

3. **工具链**
   - CMake 3.10+
   - Python 3.6+
   - Visual Studio 2019+ (Windows)

### 源码下载

```bash
# 下载OSG源码
git clone https://github.com/openscenegraph/OpenSceneGraph.git

# 下载osgEarth源码
git clone https://github.com/gwaldron/osgearth.git
```

## 编译步骤

### 第一步：编译OSG库为WebAssembly

#### 1.1 准备环境

```bash
# 确保Emscripten已安装
source /path/to/emsdk/emsdk_env.sh

# 创建构建目录
mkdir osg_wasm_build
cd osg_wasm_build
```

#### 1.2 配置CMake

```bash
emcmake cmake ../OpenSceneGraph \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_INSTALL_PREFIX=/path/to/wasm_deps \
    -DOSG_GL1_AVAILABLE=OFF \
    -DOSG_GL2_AVAILABLE=OFF \
    -DOSG_GLES2_AVAILABLE=ON \
    -DOSG_GL3_AVAILABLE=OFF \
    -DOSG_GL_LIBRARY_STATIC=ON \
    -DOSG_GL_DISPLAYLISTS_AVAILABLE=OFF \
    -DOSG_GL_MATRICES_AVAILABLE=OFF \
    -DOSG_GL_VERTEX_FUNCS_AVAILABLE=OFF \
    -DOSG_GL_VERTEX_ARRAY_FUNCS_AVAILABLE=ON \
    -DOSG_GL_FIXED_FUNCTION_AVAILABLE=OFF \
    -DDYNAMIC_OPENSCENEGRAPH=OFF \
    -DBUILD_OSG_APPLICATIONS=OFF \
    -DBUILD_OSG_EXAMPLES=OFF
```

#### 1.3 编译和安装

```bash
emmake make -j4
emmake make install
```

### 第二步：编译osgEarth库为WebAssembly

#### 2.1 配置CMake

```bash
mkdir osgearth_wasm_build
cd osgearth_wasm_build

emcmake cmake ../osgearth \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_INSTALL_PREFIX=/path/to/wasm_deps \
    -DOSG_DIR=/path/to/wasm_deps \
    -DOSGEARTH_BUILD_EXAMPLES=OFF \
    -DOSGEARTH_BUILD_APPLICATIONS=OFF \
    -DOSGEARTH_BUILD_TESTS=OFF \
    -DDYNAMIC_OSGEARTH=OFF
```

#### 2.2 编译和安装

```bash
emmake make -j4
emmake make install
```

### 第三步：编译weejobs线程池库

#### 3.1 配置CMake

```bash
mkdir weejobs_wasm_build
cd weejobs_wasm_build

emcmake cmake ../weejobs \
    -DCMAKE_BUILD_TYPE=Release \
    -DCMAKE_INSTALL_PREFIX=/path/to/wasm_deps \
    -DWEEJOBS_BUILD_TESTS=OFF \
    -DWEEJOBS_BUILD_EXAMPLES=OFF
```

#### 3.2 编译和安装

```bash
emmake make -j4
emmake make install
```

## 编译参数说明

### OSG关键参数

- `DOSG_GLES2_AVAILABLE=ON`: 启用OpenGL ES 2.0支持
- `DOSG_GL_FIXED_FUNCTION_AVAILABLE=OFF`: 禁用固定功能管线
- `DDYNAMIC_OPENSCENEGRAPH=OFF`: 静态链接
- `DBUILD_OSG_APPLICATIONS=OFF`: 不构建应用程序

### osgEarth关键参数

- `DOSGEARTH_BUILD_EXAMPLES=OFF`: 不构建示例
- `DDYNAMIC_OSGEARTH=OFF`: 静态链接

### Emscripten链接参数

```bash
-s USE_SDL=2
-s USE_WEBGL2=1
-s FULL_ES3=1
-s ALLOW_MEMORY_GROWTH=1
-s LEGACY_GL_EMULATION=1
-s USE_PTHREADS=1
-s PTHREAD_POOL_SIZE=4
-s SHARED_MEMORY=1
-s PROXY_TO_PTHREAD=1
```

## 测试程序编译

### OSG球体测试

```bash
emcc test_osg_sphere.cpp -o test_osg_sphere.js \
    -I/path/to/wasm_deps/include \
    -L/path/to/wasm_deps/lib \
    -losg -losgViewer -losgGA -losgDB -losgUtil \
    -s USE_SDL=2 \
    -s USE_WEBGL2=1 \
    -s FULL_ES3=1 \
    -s ALLOW_MEMORY_GROWTH=1 \
    -s LEGACY_GL_EMULATION=1 \
    -O2
```

### osgEarth完整测试

```bash
emcc test_osgearth_full.cpp -o test_osgearth_full.js \
    -I/path/to/wasm_deps/include \
    -L/path/to/wasm_deps/lib \
    -losgEarth -losgEarthUtil -losgEarthFeatures -losgEarthSymbology \
    -losg -losgViewer -losgGA -losgDB -losgUtil -losgText \
    -lweejobs \
    -s USE_SDL=2 \
    -s USE_WEBGL2=1 \
    -s FULL_ES3=1 \
    -s ALLOW_MEMORY_GROWTH=1 \
    -s LEGACY_GL_EMULATION=1 \
    -s USE_PTHREADS=1 \
    -s PTHREAD_POOL_SIZE=4 \
    -s SHARED_MEMORY=1 \
    -s PROXY_TO_PTHREAD=1 \
    -O2
```

## 常见问题和解决方案

### 1. OpenGL兼容性问题

**问题**: WebAssembly只支持OpenGL ES 2.0
**解决**: 
- 禁用固定功能管线
- 使用着色器程序
- 启用LEGACY_GL_EMULATION

### 2. 多线程支持问题

**问题**: WebAssembly多线程需要特殊配置
**解决**:
- 启用SharedArrayBuffer
- 正确设置HTTP头
- 使用PROXY_TO_PTHREAD

### 3. 内存管理问题

**问题**: WebAssembly内存限制
**解决**:
- 启用ALLOW_MEMORY_GROWTH
- 优化资源使用
- 实现资源缓存

## 目录结构

```
F:\cmo-dev\my_osgearth_web\osgearth_third_party\wasm_dep\
├── include/
│   ├── osg/
│   ├── osgEarth/
│   └── weejobs/
├── lib/
│   ├── libosg.a
│   ├── libosgEarth.a
│   └── libweejobs.a
└── share/
```

## 下一步计划

1. **编译真实OSG库**: 按照上述步骤编译OSG为WebAssembly版本
2. **编译真实osgEarth库**: 编译osgEarth为WebAssembly版本
3. **集成weejobs**: 确保多线程支持正常工作
4. **性能优化**: 优化渲染性能和内存使用
5. **功能测试**: 验证所有功能正常工作

## 技术架构

```mermaid
graph TB
    A[浏览器] --> B[WebAssembly模块]
    B --> C[osgEarth数字地球]
    C --> D[OSG 3D引擎]
    C --> E[weejobs线程池]
    D --> F[OpenGL ES 2.0]
    E --> G[Web Workers]
    C --> H[图像层]
    C --> I[高程层]
    H --> J[Google卫星]
    I --> K[AWS地形]
```

这个指南为完整的OSG和osgEarth WebAssembly移植提供了详细的路线图。
