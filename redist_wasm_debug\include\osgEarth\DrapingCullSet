/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#ifndef OSGEARTH_DRAPING_CULL_SET
#define OSGEARTH_DRAPING_CULL_SET

#include <osgEarth/Common>
#include <osgEarth/Containers>
#include <osgEarth/DrapeableNode>
#include <osg/Camera>
#include <osg/ObserverNodePath>
#include <queue>

namespace osgEarth { namespace Util
{
    /**
     * Culling set for tracking groups whose contents should be "draped",
     * i.e. rendered to a texture and projected onto the terrain.
     * Internal object - do not use directly. Use a DrapeableNode instead.
     */
    class OSGEARTH_INTERNAL DrapingCullSet
    {
    public:
        struct Entry
        {            
            osg::observer_ptr<DrapeableNode> _node;
            osg::ref_ptr<osg::RefMatrix> _matrix;
            osg::ObserverNodePath        _path;
            int                          _frame;
        };

    public:
        DrapingCullSet();
        ~DrapingCullSet() { } // not virtual

        /** Pushes a node and its matrix into the cull set */
        void push(DrapeableNode* node, const osg::NodePath& path, const osg::FrameStamp* stamp);

        /** Runs a node visitor on the cull set, optionally popping as it goes along. */
        void accept(osg::NodeVisitor& nv);

        //! Bounds of the latest set
        const osg::BoundingSphere& getBound() const;

    private:
        struct FrameData {
            FrameData() : _acceptFrame(-1) { }
            int _acceptFrame;
            std::vector<Entry> _entries;
            osg::BoundingSphere _bs;
        };
        using SortedFrameData = std::map<int, FrameData>;
        SortedFrameData _data;
        std::mutex _data_mutex;
    };

    /**
     * Houses all the active DrapingCullSets under an osgEarth TerrainEngine.
     * Internal node - so not use directly.
     */
    class OSGEARTH_INTERNAL DrapingManager
    {
    public:
        DrapingManager();

        //! Gets the draping cull set associated with a camera.
        DrapingCullSet& get(const osg::Camera*);

        //! Sets the render bin number for DrapingCameras.
        void setRenderBinNumber(int value) { _renderBinNum = value; }
        int getRenderBinNumber() const { return _renderBinNum; }

    private:
        PerObjectFastMap<const osg::Camera*, DrapingCullSet> _sets;
        int _renderBinNum;
    };

} } // namespace osgEarth

#endif // OSGEARTH_DRAPING_CULL_SET
