/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Geometry>
#include <osgEarth/SpatialReference>

namespace osgEarth
{
    namespace GeometryUtils
    {
        OSGEARTH_EXPORT std::string geometryToWKT(const Geometry *geometry);
        OSGEARTH_EXPORT std::string geometryToIsoWKT(const Geometry *geometry);
        OSGEARTH_EXPORT Geometry *geometryFromWKT(const std::string &wkt, bool rewindPolygons = true);

        OSGEARTH_EXPORT std::string geometryToGeoJSON(const Geometry *geometry, const SpatialReference *srs = nullptr);
        OSGEARTH_EXPORT Geometry *geometryFromGeoJSON(const std::string &geojson, bool rewindPolygons = true);

        OSGEARTH_EXPORT std::string geometryToKML(const Geometry *geometry);
        OSGEARTH_EXPORT std::string geometryToGML(const Geometry *geometry);
        OSGEARTH_EXPORT double getGeometryArea(const Geometry *geometry);
    }

    // GDAL替代函数的命名空间
    namespace GDAL
    {

        //! 图像重投影函数（替代GDAL::reprojectImage）
        extern OSGEARTH_EXPORT osg::Image *reprojectImage(
            const osg::Image *srcImage,
            const std::string srcWKT,
            double srcMinX, double srcMinY, double srcMaxX, double srcMaxY,
            const std::string destWKT,
            double destMinX, double destMinY, double destMaxX, double destMaxY,
            int width = 0,
            int height = 0,
            bool useBilinearInterpolation = true);

        //! 高度场转TIFF函数（替代GDAL::heightFieldToTiff）
        extern OSGEARTH_EXPORT std::string heightFieldToTiff(const osg::HeightField *heightfield);

    } // namespace GDAL

} // namespace osgEarth
