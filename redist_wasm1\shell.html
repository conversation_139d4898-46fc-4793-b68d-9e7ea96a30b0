<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>osgEarth WebAssembly Viewer</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 0;
            background-color: #1a1a1a;
            color: #ffffff;
            overflow: hidden;
        }

        #header {
            background-color: #2d2d2d;
            padding: 10px;
            text-align: center;
            border-bottom: 2px solid #4a4a4a;
        }

        #header h1 {
            margin: 0;
            font-size: 24px;
            color: #ffffff;
        }

        #canvas-container {
            position: relative;
            width: 100vw;
            height: calc(100vh - 60px);
            display: flex;
            justify-content: center;
            align-items: center;
            background-color: #000000;
        }

        #canvas {
            border: 1px solid #4a4a4a;
            background-color: #000000;
            cursor: crosshair;
        }

        #loading {
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            text-align: center;
            color: #ffffff;
            font-size: 18px;
        }

        #loading-progress {
            width: 300px;
            height: 20px;
            border: 2px solid #4a4a4a;
            border-radius: 10px;
            overflow: hidden;
            margin: 20px auto;
        }

        #loading-bar {
            width: 0%;
            height: 100%;
            background: linear-gradient(90deg, #4CAF50, #8BC34A);
            transition: width 0.3s ease;
        }

        #controls {
            position: absolute;
            top: 10px;
            left: 10px;
            background-color: rgba(45, 45, 45, 0.9);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #4a4a4a;
            font-size: 12px;
            max-width: 200px;
        }

        #controls h3 {
            margin: 0 0 10px 0;
            color: #ffffff;
        }

        #controls ul {
            margin: 0;
            padding-left: 20px;
            color: #cccccc;
        }

        #status {
            position: absolute;
            bottom: 10px;
            left: 10px;
            background-color: rgba(45, 45, 45, 0.9);
            padding: 5px 10px;
            border-radius: 3px;
            font-size: 12px;
            color: #cccccc;
        }

        .hidden {
            display: none;
        }

        .error {
            color: #ff4444;
            background-color: rgba(255, 68, 68, 0.1);
            padding: 10px;
            border-radius: 5px;
            border: 1px solid #ff4444;
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            #controls {
                position: static;
                width: 100%;
                box-sizing: border-box;
                margin: 10px 0;
            }
            
            #canvas-container {
                height: calc(100vh - 120px);
            }
        }
    </style>
</head>
<body>
    <div id="header">
        <h1>osgEarth WebAssembly 地球可视化</h1>
    </div>

    <div id="canvas-container">
        <canvas id="canvas" oncontextmenu="event.preventDefault()"></canvas>
        
        <div id="loading">
            <div>正在加载osgEarth WebAssembly模块...</div>
            <div id="loading-progress">
                <div id="loading-bar"></div>
            </div>
            <div id="loading-text">初始化中...</div>
        </div>

        <div id="controls">
            <h3>控制说明</h3>
            <ul>
                <li>左键拖拽：平移</li>
                <li>右键拖拽：旋转</li>
                <li>滚轮：缩放</li>
                <li>T键：时间动画</li>
                <li>空格：重置视角</li>
            </ul>
        </div>

        <div id="status">
            状态：初始化中...
        </div>
    </div>

    <script>
        // 全局变量
        let Module = {};
        let canvas = null;
        let loadingElement = null;
        let loadingBar = null;
        let loadingText = null;
        let statusElement = null;

        // 初始化函数
        function init() {
            canvas = document.getElementById('canvas');
            loadingElement = document.getElementById('loading');
            loadingBar = document.getElementById('loading-bar');
            loadingText = document.getElementById('loading-text');
            statusElement = document.getElementById('status');

            // 设置Canvas大小
            resizeCanvas();
            
            // 监听窗口大小变化
            window.addEventListener('resize', resizeCanvas);
            
            // 设置Module配置
            setupModule();
        }

        // 设置Canvas大小
        function resizeCanvas() {
            const container = document.getElementById('canvas-container');
            const rect = container.getBoundingClientRect();
            
            canvas.width = rect.width;
            canvas.height = rect.height;
            
            // 通知WebAssembly模块窗口大小改变
            if (Module.ccall) {
                try {
                    Module.ccall('handleResize', 'void', ['number', 'number'], [canvas.width, canvas.height]);
                } catch (e) {
                    console.log('Resize handler not available yet');
                }
            }
        }

        // 设置Module配置
        function setupModule() {
            Module = {
                canvas: canvas,
                
                // 打印函数
                print: function(text) {
                    console.log('osgEarth:', text);
                },
                
                // 错误处理
                printErr: function(text) {
                    console.error('osgEarth Error:', text);
                    updateStatus('错误: ' + text, true);
                },
                
                // 加载进度
                setStatus: function(text) {
                    updateStatus(text);
                },
                
                // 模块加载完成
                onRuntimeInitialized: function() {
                    console.log('osgEarth WebAssembly模块加载完成');
                    hideLoading();
                    updateStatus('就绪');
                    
                    // 启动应用
                    try {
                        Module.ccall('main', 'number', ['number', 'number'], [0, 0]);
                    } catch (e) {
                        console.error('启动应用失败:', e);
                        updateStatus('启动失败: ' + e.message, true);
                    }
                },
                
                // 总计字节大小
                totalDependencies: 0,
                
                // 预加载进度
                preloadProgress: function(loaded, total) {
                    if (total > 0) {
                        const progress = Math.round((loaded / total) * 100);
                        updateLoadingProgress(progress);
                        loadingText.textContent = `加载资源: ${progress}%`;
                    }
                },
                
                // 模块加载进度
                monitorRunDependencies: function(left) {
                    Module.totalDependencies = Math.max(Module.totalDependencies, left);
                    if (left > 0) {
                        const progress = Math.round(((Module.totalDependencies - left) / Module.totalDependencies) * 100);
                        updateLoadingProgress(progress);
                        loadingText.textContent = `加载模块: ${progress}%`;
                    }
                }
            };
        }

        // 更新加载进度
        function updateLoadingProgress(progress) {
            if (loadingBar) {
                loadingBar.style.width = progress + '%';
            }
        }

        // 隐藏加载界面
        function hideLoading() {
            if (loadingElement) {
                loadingElement.classList.add('hidden');
            }
        }

        // 更新状态
        function updateStatus(text, isError = false) {
            if (statusElement) {
                statusElement.textContent = '状态: ' + text;
                if (isError) {
                    statusElement.classList.add('error');
                } else {
                    statusElement.classList.remove('error');
                }
            }
        }

        // 键盘事件处理
        document.addEventListener('keydown', function(event) {
            if (Module.ccall) {
                try {
                    Module.ccall('handleKeyDown', 'void', ['number'], [event.keyCode]);
                } catch (e) {
                    console.log('Key handler not available:', e);
                }
            }
        });

        // 鼠标事件处理
        canvas.addEventListener('mousedown', function(event) {
            if (Module.ccall) {
                try {
                    Module.ccall('handleMouseDown', 'void', ['number', 'number', 'number'], 
                        [event.button, event.clientX, event.clientY]);
                } catch (e) {
                    console.log('Mouse handler not available:', e);
                }
            }
        });

        canvas.addEventListener('mousemove', function(event) {
            if (Module.ccall) {
                try {
                    Module.ccall('handleMouseMove', 'void', ['number', 'number'], 
                        [event.clientX, event.clientY]);
                } catch (e) {
                    console.log('Mouse handler not available:', e);
                }
            }
        });

        canvas.addEventListener('mouseup', function(event) {
            if (Module.ccall) {
                try {
                    Module.ccall('handleMouseUp', 'void', ['number', 'number', 'number'], 
                        [event.button, event.clientX, event.clientY]);
                } catch (e) {
                    console.log('Mouse handler not available:', e);
                }
            }
        });

        // 滚轮事件处理
        canvas.addEventListener('wheel', function(event) {
            event.preventDefault();
            if (Module.ccall) {
                try {
                    Module.ccall('handleWheel', 'void', ['number'], [event.deltaY]);
                } catch (e) {
                    console.log('Wheel handler not available:', e);
                }
            }
        });

        // 页面加载完成后初始化
        window.addEventListener('load', init);
    </script>

    {{{ SCRIPT }}}
</body>
</html> 