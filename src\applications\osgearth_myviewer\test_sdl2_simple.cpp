/**
 * 简化的SDL2测试程序
 * 只测试SDL2基础功能，不使用复杂的OpenGL
 */

#include <iostream>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#else
#include <SDL.h>
#endif

// 全局变量
static SDL_Window* g_window = nullptr;
static SDL_Renderer* g_renderer = nullptr;
static bool g_running = true;
static int g_frame_count = 0;
static Uint32 g_start_time = 0;

// 简单的2D渲染
void render() {
    // 设置背景色（深蓝色）
    SDL_SetRenderDrawColor(g_renderer, 20, 40, 80, 255);
    SDL_RenderClear(g_renderer);
    
    // 绘制一个移动的矩形
    int time = SDL_GetTicks() - g_start_time;
    int x = (int)(200 + 150 * sin(time * 0.002));
    int y = (int)(200 + 100 * cos(time * 0.003));
    
    SDL_Rect rect = {x, y, 100, 60};
    
    // 设置矩形颜色（红色）
    SDL_SetRenderDrawColor(g_renderer, 255, 100, 100, 255);
    SDL_RenderFillRect(g_renderer, &rect);
    
    // 绘制一个圆形（用多个小矩形模拟）
    int cx = 400, cy = 300, radius = 50;
    SDL_SetRenderDrawColor(g_renderer, 100, 255, 100, 255);
    
    for (int angle = 0; angle < 360; angle += 10) {
        double rad = angle * M_PI / 180.0;
        int px = cx + (int)(radius * cos(rad));
        int py = cy + (int)(radius * sin(rad));
        SDL_Rect point = {px-2, py-2, 4, 4};
        SDL_RenderFillRect(g_renderer, &point);
    }
    
    // 显示帧数信息
    g_frame_count++;
    if (g_frame_count % 60 == 0) {
        float fps = g_frame_count * 1000.0f / (SDL_GetTicks() - g_start_time);
        std::cout << "[render] 帧数: " << g_frame_count << ", FPS: " << fps << std::endl;
    }
    
    SDL_RenderPresent(g_renderer);
}

// 处理事件
void handle_events() {
    SDL_Event event;
    while (SDL_PollEvent(&event)) {
        switch (event.type) {
            case SDL_QUIT:
                g_running = false;
                std::cout << "[event] 收到退出事件" << std::endl;
                break;
                
            case SDL_KEYDOWN:
                std::cout << "[event] 按键按下: " << SDL_GetKeyName(event.key.keysym.sym) << std::endl;
                if (event.key.keysym.sym == SDLK_ESCAPE) {
                    g_running = false;
                }
                break;
                
            case SDL_MOUSEBUTTONDOWN:
                std::cout << "[event] 鼠标按下: (" << event.button.x << ", " << event.button.y << ")" << std::endl;
                break;
                
            case SDL_MOUSEMOTION:
                // 只在按下鼠标时显示移动信息
                if (event.motion.state & SDL_BUTTON_LMASK) {
                    std::cout << "[event] 鼠标拖拽: (" << event.motion.x << ", " << event.motion.y << ")" << std::endl;
                }
                break;
        }
    }
}

// 主循环函数
void main_loop() {
    if (!g_running) {
        return;
    }
    
    handle_events();
    render();
}

int main(int argc, char* argv[]) {
    std::cout << "🎮 简化SDL2测试程序" << std::endl;
    std::cout << "===================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;
    
    // 创建窗口
    g_window = SDL_CreateWindow(
        "SDL2 + WebAssembly Simple Test",
        SDL_WINDOWPOS_CENTERED,
        SDL_WINDOWPOS_CENTERED,
        800, 600,
        SDL_WINDOW_SHOWN
    );
    
    if (!g_window) {
        std::cout << "[main] ❌ 窗口创建失败: " << SDL_GetError() << std::endl;
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 窗口创建成功: 800x600" << std::endl;
    
    // 创建渲染器
    g_renderer = SDL_CreateRenderer(g_window, -1, SDL_RENDERER_ACCELERATED | SDL_RENDERER_PRESENTVSYNC);
    if (!g_renderer) {
        std::cout << "[main] ❌ 渲染器创建失败: " << SDL_GetError() << std::endl;
        SDL_DestroyWindow(g_window);
        SDL_Quit();
        return 1;
    }
    std::cout << "[main] ✅ 渲染器创建成功" << std::endl;
    
    // 获取渲染器信息
    SDL_RendererInfo info;
    if (SDL_GetRendererInfo(g_renderer, &info) == 0) {
        std::cout << "[renderer] 名称: " << info.name << std::endl;
        std::cout << "[renderer] 标志: " << info.flags << std::endl;
        std::cout << "[renderer] 纹理格式数: " << info.num_texture_formats << std::endl;
    }
    
    g_start_time = SDL_GetTicks();
    
    std::cout << "[main] ✅ SDL2设置完成" << std::endl;
    std::cout << "[main] 开始渲染循环..." << std::endl;
    std::cout << "[main] 按ESC键或关闭窗口退出" << std::endl;
    std::cout << "[main] 点击和拖拽鼠标查看事件响应" << std::endl;
    
#ifdef EMSCRIPTEN
    // WebAssembly主循环
    emscripten_set_main_loop(main_loop, 60, 1); // 60 FPS
#else
    // 桌面主循环
    while (g_running) {
        main_loop();
        SDL_Delay(16); // ~60 FPS
    }
#endif
    
    // 清理资源
    if (g_renderer) {
        SDL_DestroyRenderer(g_renderer);
    }
    if (g_window) {
        SDL_DestroyWindow(g_window);
    }
    SDL_Quit();
    
    std::cout << "[main] 程序正常结束" << std::endl;
    std::cout << "[main] 总帧数: " << g_frame_count << std::endl;
    return 0;
}
