#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础数字地球WebAssembly编译器
基于原始osgearth_myviewer.cpp实现，包含纹理和经纬度网格
"""

import os
import subprocess

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🌍 基础数字地球WebAssembly编译器")
    print("================================")
    print("基于原始osgearth_myviewer.cpp实现")
    print("包含完整纹理和经纬度网格线")
    
    # 检查Emscripten
    emcc_check = """C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"""
    
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return
    
    # 设置第三方库路径
    third_party_path = "F:\\cmo-dev\\my_osgearth_web\\osgearth_third_party\\wasm_dep"
    
    # 检查第三方库路径是否存在
    if not os.path.exists(third_party_path):
        print(f"⚠️ 第三方库路径不存在: {third_party_path}")
        print("将使用模拟版本进行编译测试...")
        
        # 使用Canvas 2D版本模拟
        mock_cmd = """C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_earth_canvas.cpp -o osgearth_basic_earth.js -s USE_SDL=2 -s ALLOW_MEMORY_GROWTH=1 -O2"""
        
        if run_command(mock_cmd, "编译模拟基础数字地球"):
            print("✅ 模拟基础数字地球编译成功")
        else:
            print("❌ 模拟基础数字地球编译失败")
            
        print("\n📝 说明:")
        print("  • 当前使用Canvas 2D程序模拟基础数字地球")
        print("  • 要编译真实的osgEarth版本，需要先编译OSG和osgEarth库为WebAssembly")
        print("  • 参考 OSG_osgEarth_WebAssembly编译指南.md")
        
    else:
        # OSG和osgEarth库路径
        osg_include = f"{third_party_path}/include"
        osg_lib = f"{third_party_path}/lib"
        
        print(f"✅ 找到第三方库路径: {third_party_path}")
        
        # 编译真实的基础数字地球程序
        earth_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/osgearth_myviewer_minimal.cpp -o osgearth_basic_earth.js \\
            -I{osg_include} \\
            -L{osg_lib} \\
            -losgEarth -losgEarthUtil -losgEarthFeatures -losgEarthSymbology \\
            -losg -losgViewer -losgGA -losgDB -losgUtil -losgText \\
            -s USE_SDL=2 \\
            -s USE_WEBGL2=1 \\
            -s FULL_ES3=1 \\
            -s ALLOW_MEMORY_GROWTH=1 \\
            -s LEGACY_GL_EMULATION=1 \\
            -s EXPORTED_FUNCTIONS="['_main']" \\
            -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']" \\
            -O2"""
        
        if run_command(earth_cmd, "编译真实基础数字地球"):
            print("✅ 真实基础数字地球编译成功")
        else:
            print("❌ 真实基础数字地球编译失败")
    
    print("\n🎯 编译完成！")
    print("测试地址:")
    print("  • 基础数字地球: http://localhost:8080/osgearth_basic_earth.html")
    print("\n🌍 功能特性:")
    print("  • Google卫星图像层（桌面）/ OpenStreetMap（WebAssembly）")
    print("  • AWS地形高程数据（桌面版）")
    print("  • 经纬度网格线显示")
    print("  • 天空效果和大气渲染")
    print("  • 完整的鼠标键盘交互")
    print("  • 快捷键导航（1-北京，2-纽约，空格-重置）")

if __name__ == "__main__":
    main()
