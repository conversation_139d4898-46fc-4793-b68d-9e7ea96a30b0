<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简化数字地球测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #0a0a0a;
            color: #ffffff;
            font-size: 14px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
        }

        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }

        .error {
            border-left-color: #ff4444;
            background-color: #4a1a1a;
        }

        .success {
            border-left-color: #44ff44;
            background-color: #1a4a1a;
        }

        .warning {
            border-left-color: #ffa500;
            background-color: #4a3a1a;
        }

        .earth-container {
            background: linear-gradient(135deg, #0a0a2e, #1a1a3e);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            border: 2px solid #333;
        }

        .controls {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .control-group {
            text-align: center;
        }

        .control-group h4 {
            margin: 0 0 10px 0;
            color: #00d4ff;
        }

        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }

        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }

        .log-info {
            color: #87ceeb;
        }

        .log-error {
            color: #ff6b6b;
        }

        .log-warn {
            color: #ffd93d;
        }

        .log-success {
            color: #90ee90;
        }

        canvas {
            border: 3px solid #444;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }

        .info-panel {
            background-color: #1a1a1a;
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-size: 12px;
        }

        .coord-display {
            color: #00ff88;
            font-weight: bold;
        }
    </style>
</head>

<body>
    <div class="container">
        <h1>🌍 Canvas 2D数字地球测试</h1>

        <div id="status" class="status">状态: 准备加载Canvas 2D数字地球...</div>

        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>测试Canvas 2D数字地球系统，模拟osgEarth的核心功能：</p>
            <ul>
                <li>2D球面投影渲染</li>
                <li>经纬网格系统</li>
                <li>大陆轮廓显示</li>
                <li>交互式缩放控制</li>
            </ul>
        </div>

        <div class="earth-container">
            <h3>🌍 数字地球视窗</h3>
            <p>这是一个简化的数字地球演示，展示了osgEarth的基本概念</p>
            <canvas id="canvas" width="800" height="600"></canvas>

            <div class="info-panel">
                <div>当前坐标: <span id="coords" class="coord-display">北京 (116.3°E, 39.9°N)</span></div>
                <div>缩放级别: <span id="zoom" class="coord-display">1.0x</span></div>
                <div>瓦片状态: <span id="tiles" class="coord-display">加载中...</span></div>
            </div>
        </div>

        <div class="controls">
            <div class="control-group">
                <h4>🖱️ 鼠标控制</h4>
                <p>• 拖拽: 移动地球</p>
                <p>• 滚轮: 缩放视图</p>
                <p>• 点击: 查看坐标</p>
            </div>

            <div class="control-group">
                <h4>⌨️ 键盘控制</h4>
                <p>• +/= 键: 放大</p>
                <p>• - 键: 缩小</p>
                <p>• ESC 键: 退出</p>
            </div>

            <div class="control-group">
                <h4>🗺️ 地图特性</h4>
                <p>• 瓦片地图系统</p>
                <p>• 球面投影</p>
                <p>• LOD层级管理</p>
            </div>

            <div class="control-group">
                <h4>🚀 技术特性</h4>
                <p>• WebAssembly + SDL2</p>
                <p>• OpenGL ES 2.0</p>
                <p>• 实时渲染</p>
            </div>
        </div>

        <div class="log">
            <h3>📝 系统日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        let currentZoom = 1.0;
        let currentLon = 116.3;
        let currentLat = 39.9;

        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }

        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }

        function updateUI() {
            document.getElementById('coords').textContent =
                `(${currentLon.toFixed(2)}°E, ${currentLat.toFixed(2)}°N)`;
            document.getElementById('zoom').textContent = `${currentZoom.toFixed(1)}x`;
        }

        // Module配置
        var Module = {
            canvas: document.getElementById('canvas'),

            print: function (text) {
                console.log('Earth Test:', text);
                addLog('输出: ' + text, 'info');

                // 解析坐标信息
                if (text.includes('拖拽到:')) {
                    const match = text.match(/拖拽到: \(([^,]+), ([^)]+)\)/);
                    if (match) {
                        currentLon = parseFloat(match[1]);
                        currentLat = parseFloat(match[2]);
                        updateUI();
                    }
                }

                // 解析缩放信息
                if (text.includes('缩放级别:')) {
                    const match = text.match(/缩放级别: ([0-9.]+)/);
                    if (match) {
                        currentZoom = parseFloat(match[1]);
                        updateUI();
                    }
                }

                // 解析瓦片信息
                if (text.includes('创建了') && text.includes('个瓦片')) {
                    const match = text.match(/创建了 (\d+) 个瓦片/);
                    if (match) {
                        document.getElementById('tiles').textContent = `${match[1]} 个瓦片已创建`;
                    }
                }

                // 检查成功和失败标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                } else if (text.includes('❌')) {
                    addLog(text, 'error');
                } else if (text.includes('⚠️')) {
                    addLog(text, 'warn');
                }
            },

            printErr: function (text) {
                console.error('Earth Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },

            onRuntimeInitialized: function () {
                console.log('简化数字地球测试模块初始化完成');
                addLog('简化数字地球测试模块初始化完成', 'success');
                updateStatus('简化数字地球测试模块就绪', false, true);
                updateUI();
            },

            onAbort: function (what) {
                console.error('简化数字地球测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },

            onExit: function (status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');

                if (status === 0) {
                    updateStatus('🎉 简化数字地球测试完成！', false, true);
                } else {
                    updateStatus('❌ 简化数字地球测试失败', true);
                }
            }
        };

        // 页面加载完成后初始化
        window.addEventListener('load', function () {
            addLog('页面加载完成，开始简化数字地球测试', 'info');

            updateStatus('加载简化数字地球测试模块...');
            addLog('开始加载简化数字地球测试模块', 'info');

            // 确保Module在全局作用域
            window.Module = Module;

            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_earth.js';
            script.onload = function () {
                addLog('简化数字地球测试脚本加载成功', 'success');
                updateStatus('简化数字地球测试脚本已加载');
            };
            script.onerror = function () {
                addLog('简化数字地球测试脚本加载失败', 'error');
                updateStatus('简化数字地球测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });

        // 全局错误处理
        window.addEventListener('error', function (event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });

        window.addEventListener('unhandledrejection', function (event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>

</html>