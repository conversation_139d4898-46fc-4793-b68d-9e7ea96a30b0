/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_GEOCOMMON_H
#define OSGEARTH_GEOCOMMON_H 1

// For FLT_MAX
#include <limits.h>
#include <cfloat>

#undef  NO_DATA_VALUE
#define NO_DATA_VALUE -FLT_MAX

namespace osgEarth
{
//    constexpr float NO_DATA_VALUE = -FLT_MAX;

    /**
     * Types of interpolation between two geodetic locations.
     */
    enum GeoInterpolation
    {
        GEOINTERP_GREAT_CIRCLE,
        GEOINTERP_RHUMB_LINE,
        GEOINTERP_DEFAULT = GEOINTERP_RHUMB_LINE
    };

    /**
     * Elevation/Image grid interpolation methods.
     */
    enum RasterInterpolation
    {
        INTERP_AVERAGE,
        INTERP_NEAREST,
        INTERP_BILINEAR,
        INTERP_TRIANGULATE,
        INTERP_CUBIC,
        INTERP_CUBICSPLINE
    };

    /**
     * Elevation stack sampling policy
     */
    enum ElevationSamplePolicy
    {
        SAMPLE_FIRST_VALID,
        SAMPLE_HIGHEST,
        SAMPLE_LOWEST,
        SAMPLE_AVERAGE
    };

    /**
     * Elevation NO_DATA treatment policy
     */
    enum ElevationNoDataPolicy
    {
        NODATA_INTERPOLATE,     // interpolate across NO_DATA samples
        NODATA_MSL              // rewrite NO_DATA samples as MSL
    };

    /**
     * Indicates how to interpret a Z coordinate.
     */
    enum AltitudeMode
    {
        ALTMODE_ABSOLUTE,  // Z value is the absolute height above MSL/HAE.
        ALTMODE_RELATIVE   // Z value is the height above the terrain elevation.
    };
}

#endif // OSGEARTH_GEODATA_H
