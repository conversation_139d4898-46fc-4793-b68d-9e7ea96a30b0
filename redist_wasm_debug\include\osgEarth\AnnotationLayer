/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_ANNOTATION_ANNOTATION_LAYER
#define OSGEARTH_ANNOTATION_ANNOTATION_LAYER

#include <osgEarth/VisibleLayer>
#include <osgEarth/Common>

namespace osgEarth
{
    class AnnotationNode;

    /**
     * Layer that holds annotation nodes.
     *
     * Use with the options structure is intended for loading from an earth file.
     * To use from the API, you can just call getGroup() and add Annotations there.
     */
    class OSGEARTH_EXPORT AnnotationLayer : public VisibleLayer
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public VisibleLayer::Options {
        public:
            META_LayerOptions(osgEarth, Options, VisibleLayer::Options);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, AnnotationLayer, Options, VisibleLayer, Annotations);

        //! Adds an annotation to the layer
        void addChild(osg::Node*);

        //! Gets the group to which you can add annotations
        osg::Group* getGroup() const;

    public: // Layer
        
        virtual osg::Node* getNode() const;

        virtual void init();

    protected:

        /** dtor */
        virtual ~AnnotationLayer() { }        

    private:

        osg::ref_ptr<osg::Group> _root;

        void deserialize();
    };  
}

#endif // OSGEARTH_ANNOTATION_ANNOTATION_LAYER
