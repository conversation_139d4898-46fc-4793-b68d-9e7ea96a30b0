<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单WebAssembly测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        #canvas {
            border: 1px solid #4a4a4a;
            background-color: #000000;
            display: block;
            margin: 20px auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 4px;
            margin: 10px 0;
        }
        .error { background-color: #4a1a1a; }
        .success { background-color: #1a4a1a; }
    </style>
</head>
<body>
    <div class="container">
        <h1>osgEarth WebAssembly 简单测试</h1>
        
        <div id="status" class="status">状态: 初始化中...</div>
        
        <canvas id="canvas" width="800" height="600"></canvas>
        
        <div id="console" class="status">
            <h3>控制台输出:</h3>
            <div id="console-output"></div>
        </div>
    </div>

    <script>
        // 简单的Module配置，避免冲突
        var Module = {
            canvas: document.getElementById('canvas'),
            
            print: function(text) {
                console.log('osgEarth:', text);
                addConsoleOutput('INFO: ' + text);
                updateStatus('运行中: ' + text);
            },
            
            printErr: function(text) {
                console.error('osgEarth Error:', text);
                addConsoleOutput('ERROR: ' + text, true);
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('WebAssembly模块初始化完成');
                addConsoleOutput('WebAssembly模块初始化完成');
                updateStatus('就绪 - WebAssembly模块已加载', false, true);
            },
            
            onAbort: function(what) {
                console.error('WebAssembly模块中止:', what);
                addConsoleOutput('模块中止: ' + what, true);
                updateStatus('模块中止: ' + what, true);
            }
        };
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            statusEl.textContent = '状态: ' + text;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        function addConsoleOutput(text, isError = false) {
            const outputEl = document.getElementById('console-output');
            const div = document.createElement('div');
            div.textContent = new Date().toLocaleTimeString() + ' - ' + text;
            if (isError) div.style.color = '#ff6666';
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        // 页面加载完成后加载WebAssembly
        window.addEventListener('load', function() {
            updateStatus('加载WebAssembly模块...');
            addConsoleOutput('开始加载WebAssembly模块');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'osgearth_myviewer.js';
            script.onload = function() {
                addConsoleOutput('WebAssembly脚本加载成功');
                updateStatus('WebAssembly脚本已加载');
            };
            script.onerror = function() {
                addConsoleOutput('WebAssembly脚本加载失败', true);
                updateStatus('WebAssembly脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
    </script>
</body>
</html>
