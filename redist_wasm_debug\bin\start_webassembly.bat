@echo off
chcp 65001 > nul
setlocal enabledelayedexpansion

:: 设置控制台标题
title osgEarth WebAssembly HTTP Server

:: 显示欢迎信息
echo.
echo ========================================
echo  osgEarth WebAssembly HTTP Server
echo ========================================
echo.

:: 切换到脚本所在目录
cd /d "%~dp0"

:: 检查Python是否可用
python --version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Python环境
    echo 请确保Python已安装并已添加到PATH环境变量
    echo.
    echo 您可以从以下地址下载Python:
    echo https://www.python.org/downloads/
    echo.
    pause
    exit /b 1
)

:: 显示Python版本
for /f "tokens=2" %%i in ('python --version 2^>^&1') do set PYTHON_VERSION=%%i
echo 检测到Python版本: %PYTHON_VERSION%

:: 检查必要文件是否存在
set "missing_files="
if not exist "index.html" set "missing_files=!missing_files! index.html"
if not exist "osgearth_myviewer.js" set "missing_files=!missing_files! osgearth_myviewer.js"
if not exist "osgearth_myviewer.wasm" set "missing_files=!missing_files! osgearth_myviewer.wasm"
if not exist "http_server.py" set "missing_files=!missing_files! http_server.py"

if not "!missing_files!"=="" (
    echo.
    echo 错误: 缺少必要文件:!missing_files!
    echo 请确保在正确的目录中运行此脚本
    echo 当前目录: %CD%
    echo.
    pause
    exit /b 1
)

:: 显示文件信息
echo.
echo 检查WebAssembly文件...
for %%f in (*.wasm) do (
    echo   %%f - !%%~zf! bytes
)

echo.
echo 检查JavaScript文件...
for %%f in (*.js) do (
    echo   %%f - !%%~zf! bytes
)

echo.
echo 即将启动HTTP服务器...
echo.
echo 服务器配置:
echo   - 端口: 8000
echo   - 主机: localhost
echo   - 地址: http://localhost:8000
echo.
echo 注意事项:
echo   - 服务器启动后会自动打开浏览器
echo   - 如需停止服务器，请按 Ctrl+C
echo   - 支持CORS跨域访问
echo.

:: 启动HTTP服务器
echo 正在启动服务器...
python http_server.py --port 8000 --host localhost

:: 如果服务器异常退出，显示错误信息
if %errorlevel% neq 0 (
    echo.
    echo 服务器启动失败 (错误代码: %errorlevel%)
    echo.
    echo 可能的原因:
    echo   - 端口8000已被占用
    echo   - Python脚本执行错误
    echo   - 权限不足
    echo.
    echo 尝试解决方案:
    echo   1. 关闭其他可能占用端口8000的程序
    echo   2. 以管理员身份运行此脚本
    echo   3. 手动运行: python http_server.py --port 8080
    echo.
    pause
)

echo.
echo 服务器已停止
pause