// ***DO NOT EDIT THIS FILE - IT IS AUTOMATICALLY GENERATED BY CMAKE***

#include <osgEarthDrivers/sky_simple/SimpleSkyShaders>


using namespace osgEarth::SimpleSky;

Shaders::Shaders()
{
    Atmosphere_Vert = "SimpleSky.Atmosphere.vert.glsl";
    _sources[Atmosphere_Vert] = 
R"(#pragma vp_function atmos_vertex_main, vertex_view, 0.5
// Atmospheric Scattering and Sun Shaders
// Adapted from code that is Copyright (c) 2004 Sean ONeil
uniform mat4 osg_ViewMatrixInverse;   // camera position in [3].xyz
uniform vec3 atmos_v3LightDir;        // The direction vector to the light source 
uniform float atmos_fOuterRadius;     // Outer atmosphere radius 
uniform float atmos_fInnerRadius;     // Inner planetary radius 
const float PI = 3.1415927;
const float Kr = 0.0025;
const float Km = 0.0015;
const float ESun = 15.0;
const float RaleighScaleDepth = 0.25;
const float atmos_fKrESun = Kr * ESun;
const float atmos_fKmESun = Km * ESun;
const float atmos_fKr4PI = Kr * 4 * PI;
const float atmos_fKm4PI = Km * 4 * PI;
const vec3 atmos_v3InvWavelength = vec3(5.6020447, 9.4732844, 19.6438026);
#define N_SAMPLES 2
#define F_SAMPLES 2.0
float atmos_fCameraHeight;
float atmos_fCameraHeight2;
float atmos_fOuterRadius2;
float atmos_fScale;
float atmos_fScaleOverScaleDepth;
vec3 atmos_vVec;
out vec3 atmos_v3Direction; 
out vec3 atmos_mieColor; 
out vec3 atmos_rayleighColor; 
out float atmos_renderFromSpace;
float atmos_fastpow(in float x, in float y) 
{ 
    return x/(x+y-y*x); 
} 
float atmos_scale(float fCos) 	
{ 
    float x = 1.0 - fCos; 
    return RaleighScaleDepth * exp(-0.00287 + x*(0.459 + x*(3.83 + x*(-6.80 + x*5.25))));
} 
void atmos_SkyFromSpace(void) 
{ 
    // Get the ray from the camera to the vertex and its length (which is the far point of the ray passing through the atmosphere) 
    vec3 v3Pos = gl_Vertex.xyz; 
    vec3 v3Ray = v3Pos - atmos_vVec; 
    float fFar = length(v3Ray); 
    v3Ray /= fFar; 
    // Calculate the closest intersection of the ray with the outer atmosphere 
    // (which is the near point of the ray passing through the atmosphere) 
    float B = 2.0 * dot(atmos_vVec, v3Ray); 
    float C = atmos_fCameraHeight2 - atmos_fOuterRadius2; 
    float fDet = max(0.0, B*B - 4.0 * C); 	
    float fNear = 0.5 * (-B - sqrt(fDet)); 		
    // Calculate the ray's starting position, then calculate its scattering offset 
    vec3 v3Start = atmos_vVec + v3Ray * fNear; 			
    fFar -= fNear; 	
    float fStartAngle = dot(v3Ray, v3Start) / atmos_fOuterRadius; 			
    float fStartDepth = exp(-1.0 / RaleighScaleDepth); 
    float fStartOffset = fStartDepth*atmos_scale(fStartAngle); 		
    // Initialize the atmos_ing loop variables 	
    float fSampleLength = fFar / F_SAMPLES; 		
    float fScaledLength = fSampleLength * atmos_fScale; 					
    vec3 v3SampleRay = v3Ray * fSampleLength; 	
    vec3 v3SamplePoint = v3Start + v3SampleRay * 0.5; 	
    // Now loop through the sample rays 
    vec3 v3FrontColor = vec3(0.0, 0.0, 0.0); 
    vec3 v3Attenuate;   
    for(int i=0; i<N_SAMPLES; i++) 		
    { 
        float fHeight = length(v3SamplePoint); 			
        float fDepth = exp(atmos_fScaleOverScaleDepth * (atmos_fInnerRadius - fHeight)); 
        float fLightAngle = dot(atmos_v3LightDir, v3SamplePoint) / fHeight; 		
        float fCameraAngle = dot(v3Ray, v3SamplePoint) / fHeight; 			
        float fscatter = (fStartOffset + fDepth*(atmos_scale(fLightAngle) - atmos_scale(fCameraAngle))); 	
        v3Attenuate = exp(-fscatter * (atmos_v3InvWavelength * atmos_fKr4PI + atmos_fKm4PI)); 	
        v3FrontColor += v3Attenuate * (fDepth * fScaledLength); 					
        v3SamplePoint += v3SampleRay; 		
    } 		
    // Finally, scale the Mie and Rayleigh colors and set up the varying 			
    // variables for the pixel shader 	
    atmos_mieColor      = v3FrontColor * atmos_fKmESun; 				
    atmos_rayleighColor = v3FrontColor * (atmos_v3InvWavelength * atmos_fKrESun); 						
    atmos_v3Direction = atmos_vVec  - v3Pos; 			
} 		
void atmos_SkyFromAtmosphere(void) 		
{ 
    // Get the ray from the camera to the vertex, and its length (which is the far 
    // point of the ray passing through the atmosphere) 
    vec3 v3Pos = gl_Vertex.xyz; 	
    vec3 v3Ray = v3Pos - atmos_vVec; 			
    float fFar = length(v3Ray); 					
    v3Ray /= fFar; 				
    // Calculate the ray's starting position, then calculate its atmos_ing offset 
    vec3 v3Start = atmos_vVec; 
    float fHeight = length(v3Start); 		
    float fDepth = exp(atmos_fScaleOverScaleDepth * (atmos_fInnerRadius - atmos_fCameraHeight)); 
    float fStartAngle = dot(v3Ray, v3Start) / fHeight; 	
    float fStartOffset = fDepth*atmos_scale(fStartAngle); 
    // Initialize the atmos_ing loop variables 		
    float fSampleLength = fFar / F_SAMPLES; 			
    float fScaledLength = fSampleLength * atmos_fScale; 				
    vec3 v3SampleRay = v3Ray * fSampleLength; 		
    vec3 v3SamplePoint = v3Start + v3SampleRay * 0.5; 
    // Now loop through the sample rays 		
    vec3 v3FrontColor = vec3(0.0, 0.0, 0.0); 		
    vec3 v3Attenuate;   
    for(int i=0; i<N_SAMPLES; i++) 			
    { 	
        float fHeight = length(v3SamplePoint); 	
        float fDepth = exp(atmos_fScaleOverScaleDepth * (atmos_fInnerRadius - fHeight)); 
        float fLightAngle = dot(atmos_v3LightDir, v3SamplePoint) / fHeight; 
        float fCameraAngle = dot(v3Ray, v3SamplePoint) / fHeight; 	
        float fscatter = (fStartOffset + fDepth*(atmos_scale(fLightAngle) - atmos_scale(fCameraAngle))); 	
        v3Attenuate = exp(-fscatter * (atmos_v3InvWavelength * atmos_fKr4PI + atmos_fKm4PI)); 	
        v3FrontColor += v3Attenuate * (fDepth * fScaledLength); 		
        v3SamplePoint += v3SampleRay; 		
    } 
    // Finally, scale the Mie and Rayleigh colors and set up the varying 
    // variables for the pixel shader 					
    atmos_mieColor      = v3FrontColor * atmos_fKmESun; 			
    atmos_rayleighColor = v3FrontColor * (atmos_v3InvWavelength * atmos_fKrESun); 				
    atmos_v3Direction = atmos_vVec - v3Pos; 				
}
void atmos_vertex_main(inout vec4 VertexVIEW) 
{
    // Get camera position and height 
    atmos_vVec = osg_ViewMatrixInverse[3].xyz; 
    atmos_fCameraHeight = length(atmos_vVec); 
    atmos_fCameraHeight2 = atmos_fCameraHeight * atmos_fCameraHeight;
    atmos_fOuterRadius2 = atmos_fOuterRadius * atmos_fOuterRadius;
    atmos_fScale = 1.0 / (atmos_fOuterRadius - atmos_fInnerRadius);
    atmos_fScaleOverScaleDepth = atmos_fScale / RaleighScaleDepth;
    if(atmos_fCameraHeight >= atmos_fOuterRadius)
    { 
        atmos_SkyFromSpace(); 
        //atmos_renderFromSpace = 1.0;
    } 
    else
    { 
        atmos_SkyFromAtmosphere(); 
        //atmos_renderFromSpace = 0.0;
    }
    // Transition from space to atmosphere
    atmos_renderFromSpace = 1.0 - clamp(
        (atmos_fOuterRadius-atmos_fCameraHeight) * atmos_fScale,
        0.0, 1.0 );
}
)";

    Atmosphere_Frag = "SimpleSky.Atmosphere.frag.glsl";
    _sources[Atmosphere_Frag] = 
R"(#pragma vp_entryPoint atmos_fragment_main
#pragma vp_location   fragment_coloring
#pragma vp_order      0.8
uniform vec3 atmos_v3LightDir; 
const float atmos_mie_g = -0.095;
const float atmos_mie_g2 = atmos_mie_g * atmos_mie_g;
const float atmos_fWeather = 1.0;
in vec3 atmos_v3Direction; 	
in vec3 atmos_mieColor; 
in vec3 atmos_rayleighColor; 
in float atmos_renderFromSpace;
uniform float oe_sky_exposure;
void atmos_fragment_main(inout vec4 color) 
{
    if (gl_ProjectionMatrix[3][3] != 0.0)
        discard;
    float fCos = dot(atmos_v3LightDir, atmos_v3Direction) / length(atmos_v3Direction); 
    float fRayleighPhase = 1.0;  // 0.75 * (1.0 + fCos*fCos); 
    float fMiePhase = 1.5 * ((1.0 - atmos_mie_g2) / (2.0 + atmos_mie_g2)) * (1.0 + fCos*fCos) / pow(1.0 + atmos_mie_g2 - 2.0*atmos_mie_g*fCos, 1.5);
    vec3 f4Color = fRayleighPhase * atmos_rayleighColor + fMiePhase * atmos_mieColor;
    
    vec3 skyColor = 1.0 - exp(f4Color * -oe_sky_exposure);
    vec4 atmosColor;
    atmosColor.rgb = skyColor.rgb*atmos_fWeather; 
    atmosColor.a = (skyColor.r+skyColor.g+skyColor.b) * 2.0;
    color = mix(atmosColor, vec4(f4Color,1.0), atmos_renderFromSpace);
}
)";

    Ground_ONeil_Vert = "SimpleSky.Ground.ONeil.vert.glsl";
    _sources[Ground_ONeil_Vert] = 
R"(#pragma vp_entryPoint atmos_vertex_main
#pragma vp_location   vertex_view
#pragma vp_order      1.1
#pragma import_defines(OE_LIGHTING)
#pragma import_defines(OE_NUM_LIGHTS)
uniform mat4 osg_ViewMatrixInverse;   // world camera position in [3].xyz 
uniform mat4 osg_ViewMatrix;          // GL view matrix 
uniform float atmos_fOuterRadius;     // Outer atmosphere radius 
uniform float atmos_fInnerRadius;     // Inner planetary radius 
//uniform float atmos_fScaleDepth;      // The scale depth 
const float PI = 3.1415927;
const float Kr = 0.0025;
const float Km = 0.0015;
const float ESun = 15.0;
const float RaleighScaleDepth = 0.25;
const float atmos_fKrESun = Kr * ESun;
const float atmos_fKmESun = Km * ESun;
const float atmos_fKr4PI = Kr * 4 * PI;
const float atmos_fKm4PI = Km * 4 * PI;
const vec3  atmos_v3InvWavelength = vec3(5.6020447, 9.4732844, 19.6438026);
#define N_SAMPLES 2
#define F_SAMPLES 2.0
// locals
float atmos_fCameraHeight;
float atmos_fCameraHeight2;
float atmos_fOuterRadius2;
float atmos_fScale;
float atmos_fScaleOverScaleDepth;
out vec3 atmos_color;          // primary sky light color
//out vec3 atmos_atten;          // sky light attenuation factor
out vec3 atmos_lightDir;       // light direction in view space       
out vec3 atmos_up;             // earth up vector at vertex location (not the normal)
out float atmos_space;         // [0..1]: camera: 0=inner radius (ground); 1.0=outer radius
out vec3 vp_Normal;             // surface normal (from osgEarth)
out vec3 earth_center;
// Parameters of each light:
struct osg_LightSourceParameters 
{   
   vec4 ambient;              // Aclarri   
   vec4 diffuse;              // Dcli   
   vec4 specular;             // Scli   
   vec4 position;             // Ppli   
   //vec4 halfVector;           // Derived: Hi   
   vec3 spotDirection;        // Sdli   
   float spotExponent;        // Srli   
   float spotCutoff;          // Crli                              
                              // (range: [0.0,90.0], 180.0)   
   float spotCosCutoff;       // Derived: cos(Crli)                 
                              // (range: [1.0,0.0],-1.0)   
   float constantAttenuation; // K0   
   float linearAttenuation;   // K1   
   float quadraticAttenuation;// K2  
   bool enabled;
};  
uniform osg_LightSourceParameters osg_LightSource[OE_NUM_LIGHTS];
float atmos_scale(float fCos) 	
{ 
    float x = 1.0 - fCos; 
    return RaleighScaleDepth * exp(-0.00287 + x*(0.459 + x*(3.83 + x*(-6.80 + x*5.25))));
} 
void atmos_GroundFromSpace(in vec4 vertexVIEW) 
{ 
    // Get the ray from the camera to the vertex and its length (which is the far point of the ray passing through the atmosphere) 
    vec3 v3Pos = vertexVIEW.xyz; 
    vec3 v3Ray = v3Pos; 
    float fFar = length(v3Ray); 
    v3Ray /= fFar; 
                
    vec4 ec4 = osg_ViewMatrix * vec4(0,0,0,1); 
    vec3 earthCenter = ec4.xyz/ec4.w; 
    vec3 normal = normalize(v3Pos-earthCenter); 
    atmos_up = normal;
    earth_center = earthCenter;
    // Calculate the closest intersection of the ray with the outer atmosphere 
    // (which is the near point of the ray passing through the atmosphere) 
    float B = 2.0 * dot(-earthCenter, v3Ray); 
    float C = atmos_fCameraHeight2 - atmos_fOuterRadius2; 
    float fDet = max(0.0, B*B - 4.0*C); 	
    float fNear = 0.5 * (-B - sqrt(fDet)); 		
    // Calculate the ray's starting position, then calculate its scattering offset 
    vec3 v3Start = v3Ray * fNear; 			
    fFar -= fNear; 
    float fDepth = exp((atmos_fInnerRadius - atmos_fOuterRadius) / RaleighScaleDepth);
    float fCameraAngle = dot(-v3Ray, normal);  // try max(0, ...) to get rid of yellowing building tops
    float fLightAngle = dot(atmos_lightDir, normal); 
    float fCameraScale = atmos_scale(fCameraAngle); 
    float fLightScale = atmos_scale(fLightAngle); 
    float fCameraOffset = fDepth*fCameraScale; 
    float fTemp = fLightScale * fCameraScale; 		
    // Initialize the scattering loop variables 
    float fSampleLength = fFar / F_SAMPLES;
    float fScaledLength = fSampleLength * atmos_fScale; 					
    vec3 v3SampleRay = v3Ray * fSampleLength; 	
    vec3 v3SamplePoint = v3Start + v3SampleRay * 0.5; 	
    // Now loop through the sample rays 
    vec3 v3FrontColor = vec3(0.0, 0.0, 0.0); 
    vec3 v3Attenuate = vec3(1,0,0); 
    for(int i=0; i<N_SAMPLES; ++i) 
    {         
        float fHeight = length(v3SamplePoint-earthCenter); 			
        float fDepth = exp(atmos_fScaleOverScaleDepth * (atmos_fInnerRadius - fHeight)); 
        float fScatter = fDepth*fTemp - fCameraOffset; 
        v3Attenuate = exp(-fScatter * (atmos_v3InvWavelength * atmos_fKr4PI + atmos_fKm4PI)); 	
        v3FrontColor += v3Attenuate * (fDepth * fScaledLength); 					
        v3SamplePoint += v3SampleRay; 		
    } 	
    atmos_color = v3FrontColor * (atmos_v3InvWavelength * atmos_fKrESun + atmos_fKmESun); 
    //atmos_atten = v3Attenuate; 
} 		
void atmos_GroundFromAtmosphere(in vec4 vertexVIEW) 		
{
    // Get the ray from the camera to the vertex and its length (which is the far point of the ray passing through the atmosphere) 
    vec3 v3Pos = vertexVIEW.xyz / vertexVIEW.w; 
    vec3 v3Ray = v3Pos; 
    float fFar = length(v3Ray); 
    v3Ray /= fFar; 
        
    vec4 ec4 = osg_ViewMatrix * vec4(0,0,0,1); 
    vec3 earthCenter = ec4.xyz/ec4.w; 
    vec3 normal = normalize(v3Pos-earthCenter); 
    atmos_up = normal; 
    earth_center = earthCenter;
    // Calculate the ray's starting position, then calculate its scattering offset 
    float fDepth = exp((atmos_fInnerRadius - atmos_fCameraHeight) / RaleighScaleDepth);
    float fCameraAngle = max(0.0, dot(-v3Ray, normal)); 
    float fLightAngle = dot(atmos_lightDir, normal); 
    float fCameraScale = atmos_scale(fCameraAngle); 
    float fLightScale = atmos_scale(fLightAngle); 
    float fCameraOffset = fDepth*fCameraScale; 
    float fTemp = fLightScale * fCameraScale; 
    // Initialize the scattering loop variables 	
    float fSampleLength = fFar / F_SAMPLES; 		
    float fScaledLength = fSampleLength * atmos_fScale; 					
    vec3 v3SampleRay = v3Ray * fSampleLength; 	
    vec3 v3SamplePoint = v3SampleRay * 0.5; 	
    // Now loop through the sample rays 
    vec3 v3FrontColor = vec3(0.0, 0.0, 0.0); 
    vec3 v3Attenuate;   
    for(int i=0; i<N_SAMPLES; i++) 		
    { 
        float fHeight = length(v3SamplePoint-earthCenter); 			
        float fDepth = exp(atmos_fScaleOverScaleDepth * (atmos_fInnerRadius - fHeight)); 
        float fScatter = fDepth*fTemp - fCameraOffset; 
        v3Attenuate = exp(-fScatter * (atmos_v3InvWavelength * atmos_fKr4PI + atmos_fKm4PI)); 	
        v3FrontColor += v3Attenuate * (fDepth * fScaledLength); 					
        v3SamplePoint += v3SampleRay; 		
    } 		
    atmos_color = v3FrontColor * (atmos_v3InvWavelength * atmos_fKrESun + atmos_fKmESun); 			
    //atmos_atten = v3Attenuate; 
} 
void atmos_vertex_main(inout vec4 vertexVIEW) 
{
#ifndef OE_LIGHTING
    return;
#endif
    atmos_fCameraHeight = length(osg_ViewMatrixInverse[3].xyz);
    atmos_fCameraHeight2 = atmos_fCameraHeight * atmos_fCameraHeight;
    atmos_fOuterRadius2 = atmos_fOuterRadius * atmos_fOuterRadius;
    atmos_fScale = 1.0 / (atmos_fOuterRadius - atmos_fInnerRadius);
    atmos_fScaleOverScaleDepth = atmos_fScale / RaleighScaleDepth;
    atmos_lightDir = normalize(osg_LightSource[0].position.xyz);  // view space
    //atmos_vert = vertexVIEW.xyz; 
    atmos_space = max(0.0, (atmos_fCameraHeight-atmos_fInnerRadius)/(atmos_fOuterRadius-atmos_fInnerRadius));
    if(atmos_fCameraHeight >= atmos_fOuterRadius) 
    { 
        atmos_GroundFromSpace(vertexVIEW); 
    } 
    else 
    { 
        atmos_GroundFromAtmosphere(vertexVIEW); 
    }     
}
)";

    Ground_ONeil_Frag = "SimpleSky.Ground.ONeil.frag.glsl";
    _sources[Ground_ONeil_Frag] = 
R"(#pragma vp_entryPoint atmos_fragment_main
#pragma vp_location   fragment_lighting
#pragma vp_order      0.8
#pragma import_defines(OE_LIGHTING)
#pragma import_defines(OE_NUM_LIGHTS)
#pragma import_defines(OE_USE_PBR)
uniform float oe_sky_exposure = 3.3; // HDR scene exposure (ground level)
uniform float oe_sky_ambientBoostFactor; // ambient sunlight booster for daytime (material mode only)
uniform float oe_sky_maxAmbientIntensity = 0.75; // maximum daytime ambient intensity (PBR mode only)
in vec3 atmos_lightDir;    // light direction (view coords)
in vec3 atmos_color;       // atmospheric lighting color
in vec3 atmos_atten;       // atmospheric lighting attenuation factor
in vec3 atmos_up;          // earth up vector at fragment (in view coords)
in float atmos_space;      // camera altitude (0=ground, 1=atmos outer radius)
in vec3 vp_Normal; // surface normal (from osgEarth)
in vec3 vp_VertexView; // from osgEarth
// frag stage global PBR parameters
#ifdef OE_USE_PBR
// fragment stage global PBR parameters.
struct OE_PBR { float displacement, roughness, ao, metal; } oe_pbr;
#endif
// Parameters of each light:
struct osg_LightSourceParameters
{
   vec4 ambient;
   vec4 diffuse;
   vec4 specular;
   vec4 position;
   vec3 spotDirection;
   float spotExponent;
   float spotCutoff;
   float spotCosCutoff;
   float constantAttenuation;
   float linearAttenuation;
   float quadraticAttenuation;
   bool enabled;
};
uniform osg_LightSourceParameters osg_LightSource[OE_NUM_LIGHTS];
// Surface material:
struct osg_MaterialParameters
{
   vec4 emission;    // Ecm
   vec4 ambient;     // Acm
   vec4 diffuse;     // Dcm
   vec4 specular;    // Scm
   float shininess;  // Srm
};
uniform osg_MaterialParameters osg_FrontMaterial;
// https://learnopengl.com/PBR/Lighting
const float PI = 3.14159265359;
float DistributionGGX(vec3 N, vec3 H, float roughness)
{
    float a = roughness * roughness;
    float a2 = a * a;
    float NdotH = max(dot(N, H), 0.0);
    float NdotH2 = NdotH * NdotH;
    float num = a2;
    float denom = (NdotH2 * (a2 - 1.0) + 1.0);
    denom = PI * denom * denom;
    return num / denom;
}
float GeometrySchlickGGX(float NdotX, float roughness)
{
    float r = (roughness + 1.0);
    float k = (r*r) / 8.0;
    float nom = NdotX;
    float denom = NdotX * (1.0 - k) + k;
    return nom / denom;
}
//float GeometrySmith(vec3 N, vec3 V, vec3 L, float roughness)
float GeometrySmith(float NdotV, float NdotL, float roughness)
{
    //float NdotV = max(dot(N, V), 0.0);
    //float NdotL = max(dot(N, L), 0.0);
    float ggx2 = GeometrySchlickGGX(NdotV, roughness);
    float ggx1 = GeometrySchlickGGX(NdotL, roughness);
    return ggx1 * ggx2;
}
vec3 FresnelSchlick(float cosTheta, vec3 F0)
{
    return F0 + (1.0 - F0) * pow(max(1.0 - cosTheta, 0.0), 5.0);
}
const float oe_wrap = 0.22;
uniform float oe_normal_boost = 1.0;
#ifdef OE_USE_PBR
void atmos_fragment_main_pbr(inout vec4 color)
{
#ifndef OE_LIGHTING
    return;
#endif
    // SRGB to linear for PBR compute:
    vec3 albedo = pow(color.rgb, vec3(2.2));
    vec3 N = normalize(vp_Normal);
    vec3 V = normalize(-vp_VertexView);
    vec3 F0 = vec3(0.04);
    F0 = mix(F0, albedo, vec3(oe_pbr.metal));
    vec3 Lo = vec3(0.0);
    float ai = 0.0; // ambient intensity (based on time of day)
    for (int i = 0; i < OE_NUM_LIGHTS; ++i)
    {
        // per-light radiance:
        vec3 L = normalize(osg_LightSource[i].position.xyz - vp_VertexView);
        vec3 H = normalize(V + L);
        //float distance = length(osg_LightSource[i].position.xyz - atmos_vert);
        //float attenuation = 1.0 / (distance * distance);
        vec3 radiance = osg_LightSource[i].diffuse.rgb; // * attenuation
        float NdotL = max(dot(N, L), 0.0);
        // wrap diffuse:
        // https://developer.nvidia.com/gpugems/gpugems/part-iii-materials/chapter-16-real-time-approximations-subsurface-scattering
        float NdotL_wrap = max(0.0, (NdotL + oe_wrap) / (1.0 + oe_wrap));
        float NdotV = max(0.0, dot(N, V));
        // cook-torrance BRDF:
        float NDF = DistributionGGX(N, H, oe_pbr.roughness);
        float G = GeometrySmith(NdotV, NdotL_wrap, oe_pbr.roughness);
        vec3 F = FresnelSchlick(max(dot(H, V), 0.0), F0);
        vec3 kS = F;
        vec3 kD = vec3(1.0) - kS;
        kD *= 1.0 - oe_pbr.metal;
        vec3 numerator = NDF * G * F;
        float denominator = 4.0 * max(dot(N, V), 0.0) * NdotL_wrap;
        vec3 specular = NdotL > 0.0 ? numerator / max(denominator, 0.001) : vec3(0.0);
        // daytime metric
        float day = i == 0 ? max(0.0, dot(atmos_up, L)) : 1.0;
        // color contribution
        Lo += (kD * albedo / PI + specular) * radiance * NdotL_wrap * day;
        // ambient intesntity contribution
        ai = max(ai, NdotL_wrap * oe_sky_maxAmbientIntensity) * day;
    }
    vec3 ambient = clamp(osg_LightSource[0].ambient.rgb + vec3(ai), 0.0, 1.0) * albedo * oe_pbr.ao;
    color.rgb = ambient + Lo;
    // tone map:
    color.rgb = color.rgb / (color.rgb + vec3(1.0));
    // add in the haze
    color.rgb += pow(atmos_color, vec3(2.2)); // add in the (SRGB) color
    // exposure:
    color.rgb = 1.0 - exp(-oe_sky_exposure * color.rgb);
    // brightness and contrast
    //color.rgb = ((color.rgb - 0.5)*oe_pbr.contrast + 0.5) * oe_pbr.brightness;
    // linear back to SRGB
    color.rgb = pow(color.rgb, vec3(1.0/2.2));
}
#else
void atmos_fragment_material(inout vec4 color)
{
    // See:
    // https://en.wikipedia.org/wiki/Phong_reflection_model
    // https://www.opengl.org/sdk/docs/tutorials/ClockworkCoders/lighting.php
    // https://en.wikibooks.org/wiki/GLSL_Programming/GLUT/Multiple_Lights
    // https://en.wikibooks.org/wiki/GLSL_Programming/GLUT/Specular_Highlights
    // normal vector at vertex
    vec3 N = normalize(vp_Normal);
    //vec3 N = normalize(gl_FrontFacing ? vp_Normal : -vp_Normal);
    float shine = clamp(osg_FrontMaterial.shininess, 1.0, 128.0);
    vec4 surfaceSpecularity = osg_FrontMaterial.specular;
    // up vector at vertex
    vec3 U = normalize(atmos_up);
    // Accumulate the lighting for each component separately.
    vec3 totalDiffuse = vec3(0.0);
    vec3 totalAmbient = vec3(0.0);
    vec3 totalSpecular = vec3(0.0);
    int numLights = OE_NUM_LIGHTS;
    for (int i=0; i<numLights; ++i)
    {
        if (osg_LightSource[i].enabled)
        {
            float attenuation = 1.0;
            // L is the normalized camera-to-light vector.
            vec3 L = normalize(osg_LightSource[i].position.xyz);
            // V is the normalized vertex-to-camera vector.
            vec3 V = -normalize(vp_VertexView);
            // point or spot light:
            if (osg_LightSource[i].position.w != 0.0)
            {
                // VLu is the unnormalized vertex-to-light vector
                vec3 Lu = osg_LightSource[i].position.xyz - vp_VertexView;
                // calculate attenuation:
                float distance = length(Lu);
                attenuation = 1.0 / (
                    osg_LightSource[i].constantAttenuation +
                    osg_LightSource[i].linearAttenuation * distance +
                    osg_LightSource[i].quadraticAttenuation * distance * distance);
                // for a spot light, the attenuation help form the cone:
                if (osg_LightSource[i].spotCutoff <= 90.0)
                {
                    vec3 D = normalize(osg_LightSource[i].spotDirection);
                    float clampedCos = max(0.0, dot(-L,D));
                    attenuation = clampedCos < osg_LightSource[i].spotCosCutoff ?
                        0.0 :
                        attenuation * pow(clampedCos, osg_LightSource[i].spotExponent);
                }
            }
            // a term indicating whether it's daytime for light 0 (the sun).
            float dayTerm = i==0? dot(U,L) : 1.0;
            // This term boosts the ambient lighting for the sun (light 0) when it's daytime.
            float ambientBoost = i==0? 1.0 + oe_sky_ambientBoostFactor*clamp(2.0*(dayTerm-0.5), 0.0, 1.0) : 1.0;
            vec3 ambientReflection =
                attenuation
                * osg_LightSource[i].ambient.rgb
                * ambientBoost;
            float NdotL = max(dot(N,L), 0.0);
            // this term, applied to light 0 (the sun), attenuates the diffuse light
            // during the nighttime, so that geometry doesn't get lit based on its
            // normals during the night.
            float diffuseAttenuation = clamp(dayTerm+0.35, 0.0, 1.0);
            vec3 diffuseReflection =
                attenuation
                * diffuseAttenuation
                * osg_LightSource[i].diffuse.rgb
                * NdotL;
            vec3 specularReflection = vec3(0.0);
            if (NdotL > 0.0)
            {
                // prevent a sharp edge where NdotL becomes positive
                // by fading in the spec between (0.0 and 0.1)
                float specAttenuation = clamp(NdotL*10.0, 0.0, 1.0);
                vec3 H = reflect(-L,N);
                float HdotV = max(dot(H,V), 0.0);
                specularReflection =
                      specAttenuation
                    * attenuation
                    * osg_LightSource[i].specular.rgb
                    * surfaceSpecularity.rgb
                    * pow(HdotV, shine);
            }
            totalDiffuse += diffuseReflection;
            totalAmbient += ambientReflection;
            totalSpecular += specularReflection;
        }
    }
    // add the atmosphere color, and incorpoate the lights.
    color.rgb += atmos_color;
    vec3 lightColor =
        osg_FrontMaterial.emission.rgb +
        totalDiffuse * osg_FrontMaterial.diffuse.rgb +
        totalAmbient * osg_FrontMaterial.ambient.rgb;
    color.rgb =
        color.rgb * lightColor +
        totalSpecular; // * osg_FrontMaterial.specular.rgb;
    // Simulate HDR by applying an exposure factor (1.0 is none, 2-3 are reasonable)
    color.rgb = 1.0 - exp(-oe_sky_exposure * 0.33 * color.rgb);
}
#endif
void atmos_fragment_main(inout vec4 color)
{
#ifndef OE_LIGHTING
    return;
#endif
#ifdef OE_USE_PBR
    atmos_fragment_main_pbr(color);
#else
    atmos_fragment_material(color);
#endif
}
)";

    Moon_Vert = "SimpleSky.Moon.vert.glsl";
    _sources[Moon_Vert] = 
R"(#version 330
uniform vec3 moonToSun;
out vec4 moon_TexCoord;
out float moon_Lighting;
void main() 
{ 
    moon_TexCoord = gl_MultiTexCoord0;
    gl_Position = gl_ModelViewProjectionMatrix * gl_Vertex; 
    // dot product results in a "lighting" factor, 0=none, 1=full,
    // to send to the fragment shader
    moon_Lighting = clamp(dot(gl_Normal, moonToSun), 0, 1);
    moon_Lighting = pow(moon_Lighting, 0.4);
}
)";

    Moon_Frag = "SimpleSky.Moon.frag.glsl";
    _sources[Moon_Frag] = 
R"(#version 330
in vec4 moon_TexCoord;
in float moon_Lighting;
uniform sampler2D moonTex;
out vec4 out_FragColor;
void main( void ) 
{
    if (gl_ProjectionMatrix[3][3] != 0.0)
        discard;
    vec4 color = texture(moonTex, moon_TexCoord.st);
    out_FragColor = vec4(color.rgb*moon_Lighting, color.a);
}
)";

    Stars_Vert = "SimpleSky.Stars.vert.glsl";
    _sources[Stars_Vert] = 
R"(#pragma vp_entryPoint oe_Stars_VS
#pragma vp_location vertex_clip
uniform float oe_GL_PointSize = 1.0;
uniform vec3 atmos_v3LightDir; 
uniform mat4 osg_ViewMatrixInverse; 
out float oe_Stars_visibility; 
vec4 vp_Color;
float remap( float val, float vmin, float vmax, float r0, float r1 ) 
{ 
    float vr = (clamp(val, vmin, vmax)-vmin)/(vmax-vmin); 
    return r0 + vr * (r1-r0); 
} 
void oe_Stars_VS(inout vec4 vertexClip)
{ 
    gl_PointSize = vp_Color.r * oe_GL_PointSize;
    vec3 eye = osg_ViewMatrixInverse[3].xyz; 
    float hae = length(eye) - 6378137.0; 
    // highness: visibility increases with altitude
    float highness = remap( hae, 25000.0, 150000.0, 0.0, 1.0 ); 
    eye = normalize(eye); 
    // darkness: visibility increase as the sun goes around the other side of the earth
    float darkness = 1.0-remap(dot(eye, atmos_v3LightDir), -0.25, 0.0, 0.0, 1.0); 
    oe_Stars_visibility = clamp(highness + darkness, 0.0, 1.0); 
    // clamp stars to the far clip plane to prevent any flickering or precision
    // issues based on the extreme distance.
    vertexClip.z = vertexClip.w;
}
)";

    Stars_Frag = "SimpleSky.Stars.frag.glsl";
    _sources[Stars_Frag] = 
R"(#pragma vp_entryPoint oe_Stars_FS
#pragma vp_location fragment_coloring
in float oe_Stars_visibility; 
void oe_Stars_FS(inout vec4 color)
{ 
    float b1 = 1.0-(2.0*abs(gl_PointCoord.s-0.5)); 
    float b2 = 1.0-(2.0*abs(gl_PointCoord.t-0.5)); 
    float i = b1*b1 * b2*b2; 
    color = color * i * oe_Stars_visibility;
}
)";
    
    Stars_GLES_Vert = "SimpleSky.Stars.GLES.vert.glsl";
    _sources[Stars_GLES_Vert] = 
R"(uniform vec3 atmos_v3LightDir; 
uniform mat4 osg_ViewMatrixInverse; 
out float visibility; 
out vec4 osg_FrontColor; 
float remap( float val, float vmin, float vmax, float r0, float r1 ) 
{ 
    float vr = (clamp(val, vmin, vmax)-vmin)/(vmax-vmin); 
    return r0 + vr * (r1-r0); 
} 
void main() 
{ 
    osg_FrontColor = gl_Color; 
    gl_PointSize = gl_Color.r * 2.0; 
    gl_Position = gl_ModelViewProjectionMatrix * gl_Vertex; 
    vec3 eye = osg_ViewMatrixInverse[3].xyz; 
    float hae = length(eye) - 6378137.0; 
    // highness: visibility increases with altitude
    float highness = remap( hae, 25000.0, 150000.0, 0.0, 1.0 ); 
    eye = normalize(eye); 
    // darkness: visibility increase as the sun goes around the other side of the earth
    float darkness = 1.0-remap(dot(eye,atmos_v3LightDir), -0.25, 0.0, 0.0, 1.0); 
    visibility = clamp(highness + darkness, 0.0, 1.0); 
}
)";

    Stars_GLES_Frag = "SimpleSky.Stars.GLES.frag.glsl";
    _sources[Stars_GLES_Frag] = 
R"(in float visibility; 
in vec4 osg_FrontColor; 
out vec4 out_FragColor;
void main( void ) 
{ 
    out_FragColor = osg_FrontColor * visibility; 
}
)";

    Sun_Vert = "SimpleSky.Sun.vert.glsl";
    _sources[Sun_Vert] = 
R"(#version 330
#pragma vp_name SimpleSky Sun vert shader
out vec3 atmos_v3Direction; 
void main() 
{ 
    vec3 v3Pos = gl_Vertex.xyz; 
    gl_Position = gl_ModelViewProjectionMatrix * gl_Vertex; 
    atmos_v3Direction = vec3(0.0,0.0,1.0) - v3Pos; 
    atmos_v3Direction = atmos_v3Direction/length(atmos_v3Direction); 
}
)";

    Sun_Frag = "SimpleSky.Sun.frag.glsl";
    _sources[Sun_Frag] = 
R"(#version 330
#pragma vp_name SimpleSky Sun frag shader
in vec3 atmos_v3Direction;
out vec4 out_FragColor;
float atmos_fastpow(in float x, in float y) 
{ 
    return x/(x+y-y*x); 
} 
void main( void ) 
{ 
   if (gl_ProjectionMatrix[3][3] != 0.0)
      discard;
   float fCos = -atmos_v3Direction[2];          
   float fMiePhase = 0.050387596899224826 * (1.0 + fCos*fCos) / atmos_fastpow(1.9024999999999999 - -1.8999999999999999*fCos, 1.5); 
   out_FragColor.rgb = fMiePhase*vec3(.3,.3,.2);
   // Alpha needs to scale from full at the center (where red == 1) to 0 on the edges of sun disc
   out_FragColor.a = out_FragColor.r;
}
)";
}

