/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTH_ELEVATION_RANGES_H
#define OSGEARTH_ELEVATION_RANGES_H 1

#include <osgEarth/Common>
#include <osgEarth/Profile>

namespace osgEarth
{
    /**
    * An approximate, fast index of the min/max elevation of the entire world.
    */
    class OSGEARTH_EXPORT ElevationRanges : public osg::Referenced
    {
    public:        
        // The maximum level that we have index data for.
        static unsigned int getMaxLevel();

        // Always global-geodetic
        static osg::ref_ptr<const Profile> getProfile();

        // Gets the min/max elevation at the given tile key index.
        static bool getElevationRange(unsigned int level, unsigned int x, unsigned int y, short& min, short& max);      

        // Get the default min/max elevation range if no index data is available.
        static bool getDefaultElevationRange(short& min, short& max);
    };

} // namespace osgEarth

#endif
