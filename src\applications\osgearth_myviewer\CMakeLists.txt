add_osgearth_app(
    TARGET osgearth_myviewer
    SOURCES osgearth_myviewer.cpp
            CustomTileLoader.cpp
            CustomTileLoader.h
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.h>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.h>
    FOLDER Tools )

# Link SSL libraries for HTTPS support (skip for WebAssembly)
if(NOT (EMSCRIPTEN OR OSGEARTH_WEBASSEMBLY))
    find_package(OpenSSL REQUIRED)
    target_link_libraries(osgearth_myviewer PRIVATE ${OPENSSL_LIBRARIES})
    target_include_directories(osgearth_myviewer PRIVATE ${OPENSSL_INCLUDE_DIR})
endif()

# Link additional networking libraries
if(WIN32 AND NOT (EMSCRIPTEN OR OSGEARTH_WEBASSEMBLY))
    target_link_libraries(osgearth_myviewer PRIVATE ws2_32 wininet)
endif()

# Add minimal version for debugging WebAssembly issues
add_osgearth_app(
    TARGET osgearth_myviewer_minimal
    SOURCES osgearth_myviewer_minimal.cpp
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.h>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.h>
    FOLDER Tools )
