add_osgearth_app(
    TARGET osgearth_myviewer
    SOURCES osgearth_myviewer.cpp
            CustomTileLoader.cpp
            CustomTileLoader.h
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.h>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.h>
    FOLDER Tools )

# Link SSL libraries for HTTPS support (skip for WebAssembly)
if(NOT (EMSCRIPTEN OR OSGEARTH_WEBASSEMBLY))
    find_package(OpenSSL REQUIRED)
    target_link_libraries(osgearth_myviewer PRIVATE ${OPENSSL_LIBRARIES})
    target_include_directories(osgearth_myviewer PRIVATE ${OPENSSL_INCLUDE_DIR})
endif()

# Link additional networking libraries
if(WIN32 AND NOT (EMSCRIPTEN OR OSGEARTH_WEBASSEMBLY))
    target_link_libraries(osgearth_myviewer PRIVATE ws2_32 wininet)
endif()

# Add minimal version for debugging WebAssembly issues
add_osgearth_app(
    TARGET osgearth_myviewer_minimal
    SOURCES osgearth_myviewer_minimal.cpp
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyHTTPClient.h>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.cpp>
            $<$<BOOL:${EMSCRIPTEN}>:WebAssemblyInputHandler.h>
    FOLDER Tools )

# Add pthread test version for WebAssembly threading validation
add_executable(osgearth_myviewer_pthread_test osgearth_myviewer_pthread_test.cpp)
# 多线程测试版本不需要osgEarth库，只测试pthread功能
if(EMSCRIPTEN)
    # WebAssembly环境：pthread支持由Emscripten提供
    target_compile_options(osgearth_myviewer_pthread_test PRIVATE -pthread)
    target_link_options(osgearth_myviewer_pthread_test PRIVATE -pthread)
else()
    # 桌面环境：链接系统pthread库
    target_link_libraries(osgearth_myviewer_pthread_test pthread)
endif()

# Add osgEarth jobs test version for threading diagnosis
add_osgearth_app(
    TARGET test_osgearth_jobs
    SOURCES test_osgearth_jobs.cpp
    FOLDER Tools )
