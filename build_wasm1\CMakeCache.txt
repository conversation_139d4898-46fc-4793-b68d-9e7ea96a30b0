# This is the CMakeCache file.
# For build in directory: f:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1
# It was generated by CMake: C:/Program Files/CMake/bin/cmake.exe
# You can edit this file to change values found and used by cmake.
# If you do not want to change any of the values, simply exit the editor.
# If you do want to change a value, simply edit, save, and exit the editor.
# The syntax for the file is as follows:
# KEY:TYPE=VALUE
# KEY is the name of a variable in the cache.
# TYPE is a hint to GUIs for the type of VALUE, DO NOT EDIT TYPE!.
# VALUE is the current value for the KEY.

########################
# EXTERNAL cache entries
########################

//Path to a program.
CMAKE_ADDR2LINE:FILEPATH=CMAKE_ADDR2LINE-NOTFOUND

//Path to a program.
CMAKE_AR:FILEPATH=C:/Program Files/LLVM/bin/llvm-ar.exe

//Choose the type of build, options are: None Debug Release RelWithDebInfo
// MinSizeRel ...
CMAKE_BUILD_TYPE:STRING=Release

//No help, variable specified on the command line.
CMAKE_CROSSCOMPILING_EMULATOR:UNINITIALIZED=C:/dev/emsdk/node/22.16.0_64bit/bin/node.exe

//CXX compiler
CMAKE_CXX_COMPILER:FILEPATH=C:/Program Files/LLVM/bin/clang++.exe

//LLVM archiver
CMAKE_CXX_COMPILER_AR:FILEPATH=C:/Program Files/LLVM/bin/llvm-ar.exe

//`clang-scan-deps` dependency scanner
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS:FILEPATH=C:/Program Files/LLVM/bin/clang-scan-deps.exe

//Generate index for LLVM archive
CMAKE_CXX_COMPILER_RANLIB:FILEPATH=C:/Program Files/LLVM/bin/llvm-ranlib.exe

//Flags used by the CXX compiler during all build types.
CMAKE_CXX_FLAGS:STRING=

//Flags used by the CXX compiler during DEBUG builds.
CMAKE_CXX_FLAGS_DEBUG:STRING=-O0 -g -Xclang -gcodeview

//Flags used by the CXX compiler during MINSIZEREL builds.
CMAKE_CXX_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the CXX compiler during RELEASE builds.
CMAKE_CXX_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the CXX compiler during RELWITHDEBINFO builds.
CMAKE_CXX_FLAGS_RELWITHDEBINFO:STRING=-O2 -DNDEBUG -g -Xclang -gcodeview

//Libraries linked by default with all C++ applications.
CMAKE_CXX_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames

//C compiler
CMAKE_C_COMPILER:FILEPATH=C:/Program Files/LLVM/bin/clang.exe

//LLVM archiver
CMAKE_C_COMPILER_AR:FILEPATH=C:/Program Files/LLVM/bin/llvm-ar.exe

//`clang-scan-deps` dependency scanner
CMAKE_C_COMPILER_CLANG_SCAN_DEPS:FILEPATH=C:/Program Files/LLVM/bin/clang-scan-deps.exe

//Generate index for LLVM archive
CMAKE_C_COMPILER_RANLIB:FILEPATH=C:/Program Files/LLVM/bin/llvm-ranlib.exe

//Flags used by the C compiler during all build types.
CMAKE_C_FLAGS:STRING=

//Flags used by the C compiler during DEBUG builds.
CMAKE_C_FLAGS_DEBUG:STRING=-O0 -g -Xclang -gcodeview

//Flags used by the C compiler during MINSIZEREL builds.
CMAKE_C_FLAGS_MINSIZEREL:STRING=-Os -DNDEBUG

//Flags used by the C compiler during RELEASE builds.
CMAKE_C_FLAGS_RELEASE:STRING=-O3 -DNDEBUG

//Flags used by the C compiler during RELWITHDEBINFO builds.
CMAKE_C_FLAGS_RELWITHDEBINFO:STRING=-O2 -DNDEBUG -g -Xclang -gcodeview

//Libraries linked by default with all C applications.
CMAKE_C_STANDARD_LIBRARIES:STRING=-lkernel32 -luser32 -lgdi32 -lwinspool -lshell32 -lole32 -loleaut32 -luuid -lcomdlg32 -ladvapi32 -loldnames

//Path to a program.
CMAKE_DLLTOOL:FILEPATH=CMAKE_DLLTOOL-NOTFOUND

//Flags used by the linker during all build types.
CMAKE_EXE_LINKER_FLAGS:STRING=

//Flags used by the linker during DEBUG builds.
CMAKE_EXE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during MINSIZEREL builds.
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during RELEASE builds.
CMAKE_EXE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during RELWITHDEBINFO builds.
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Enable/Disable output of compile commands during generation.
CMAKE_EXPORT_COMPILE_COMMANDS:BOOL=

//Value Computed by CMake.
CMAKE_FIND_PACKAGE_REDIRECTS_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1/CMakeFiles/pkgRedirects

//User executables (bin)
CMAKE_INSTALL_BINDIR:PATH=bin

//Read-only architecture-independent data (DATAROOTDIR)
CMAKE_INSTALL_DATADIR:PATH=

//Read-only architecture-independent data root (share)
CMAKE_INSTALL_DATAROOTDIR:PATH=share

//Documentation root (DATAROOTDIR/doc/PROJECT_NAME)
CMAKE_INSTALL_DOCDIR:PATH=

//C header files (include)
CMAKE_INSTALL_INCLUDEDIR:PATH=include

//Info documentation (DATAROOTDIR/info)
CMAKE_INSTALL_INFODIR:PATH=

//Object code libraries (lib)
CMAKE_INSTALL_LIBDIR:PATH=lib

//Program executables (libexec)
CMAKE_INSTALL_LIBEXECDIR:PATH=libexec

//Locale-dependent data (DATAROOTDIR/locale)
CMAKE_INSTALL_LOCALEDIR:PATH=

//Modifiable single-machine data (var)
CMAKE_INSTALL_LOCALSTATEDIR:PATH=var

//Man documentation (DATAROOTDIR/man)
CMAKE_INSTALL_MANDIR:PATH=

//C header files for non-gcc (/usr/include)
CMAKE_INSTALL_OLDINCLUDEDIR:PATH=/usr/include

//Install path prefix, prepended onto install directories.
CMAKE_INSTALL_PREFIX:PATH=C:/Program Files (x86)/OSGEARTH

//Run-time variable data (LOCALSTATEDIR/run)
CMAKE_INSTALL_RUNSTATEDIR:PATH=

//System admin executables (sbin)
CMAKE_INSTALL_SBINDIR:PATH=sbin

//Modifiable architecture-independent data (com)
CMAKE_INSTALL_SHAREDSTATEDIR:PATH=com

//Read-only single-machine data (etc)
CMAKE_INSTALL_SYSCONFDIR:PATH=etc

//Path to a program.
CMAKE_LINKER:FILEPATH=C:/Program Files/LLVM/bin/lld-link.exe

//Program used to build from build.ninja files.
CMAKE_MAKE_PROGRAM:FILEPATH=C:/Windows/System32/ninja.exe

//Flags used by the linker during the creation of modules during
// all build types.
CMAKE_MODULE_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of modules during
// DEBUG builds.
CMAKE_MODULE_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of modules during
// MINSIZEREL builds.
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of modules during
// RELEASE builds.
CMAKE_MODULE_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of modules during
// RELWITHDEBINFO builds.
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_NM:FILEPATH=C:/Program Files/LLVM/bin/llvm-nm.exe

//Path to a program.
CMAKE_OBJCOPY:FILEPATH=C:/Program Files/LLVM/bin/llvm-objcopy.exe

//Path to a program.
CMAKE_OBJDUMP:FILEPATH=C:/Program Files/LLVM/bin/llvm-objdump.exe

//Value Computed by CMake
CMAKE_PROJECT_DESCRIPTION:STATIC=osgEarth SDK

//Value Computed by CMake
CMAKE_PROJECT_HOMEPAGE_URL:STATIC=https://github.com/gwaldron/osgearth

//Value Computed by CMake
CMAKE_PROJECT_NAME:STATIC=OSGEARTH

//Path to a program.
CMAKE_RANLIB:FILEPATH=C:/Program Files/LLVM/bin/llvm-ranlib.exe

//RC compiler
CMAKE_RC_COMPILER:FILEPATH=C:/Program Files (x86)/Embarcadero/Studio/21.0/bin/rc.exe

//Flags for Windows Resource Compiler during all build types.
CMAKE_RC_FLAGS:STRING=

//Flags for Windows Resource Compiler during DEBUG builds.
CMAKE_RC_FLAGS_DEBUG:STRING=

//Flags for Windows Resource Compiler during MINSIZEREL builds.
CMAKE_RC_FLAGS_MINSIZEREL:STRING=

//Flags for Windows Resource Compiler during RELEASE builds.
CMAKE_RC_FLAGS_RELEASE:STRING=

//Flags for Windows Resource Compiler during RELWITHDEBINFO builds.
CMAKE_RC_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_READELF:FILEPATH=CMAKE_READELF-NOTFOUND

//Flags used by the linker during the creation of shared libraries
// during all build types.
CMAKE_SHARED_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of shared libraries
// during DEBUG builds.
CMAKE_SHARED_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of shared libraries
// during MINSIZEREL builds.
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELEASE builds.
CMAKE_SHARED_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of shared libraries
// during RELWITHDEBINFO builds.
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//If set, runtime paths are not added when installing shared libraries,
// but are added when building.
CMAKE_SKIP_INSTALL_RPATH:BOOL=NO

//If set, runtime paths are not added when using shared libraries.
CMAKE_SKIP_RPATH:BOOL=NO

//Flags used by the linker during the creation of static libraries
// during all build types.
CMAKE_STATIC_LINKER_FLAGS:STRING=

//Flags used by the linker during the creation of static libraries
// during DEBUG builds.
CMAKE_STATIC_LINKER_FLAGS_DEBUG:STRING=

//Flags used by the linker during the creation of static libraries
// during MINSIZEREL builds.
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL:STRING=

//Flags used by the linker during the creation of static libraries
// during RELEASE builds.
CMAKE_STATIC_LINKER_FLAGS_RELEASE:STRING=

//Flags used by the linker during the creation of static libraries
// during RELWITHDEBINFO builds.
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO:STRING=

//Path to a program.
CMAKE_STRIP:FILEPATH=C:/Program Files/LLVM/bin/llvm-strip.exe

//No help, variable specified on the command line.
CMAKE_TOOLCHAIN_FILE:UNINITIALIZED=C:\dev\emsdk\upstream\emscripten\cmake\Modules\Platform\Emscripten.cmake

//If this value is on, makefiles will be generated without the
// .SILENT directive, and all commands will be echoed to the console
// during the make.  This is useful for debugging only. With Visual
// Studio IDE projects all commands are done without /nologo.
CMAKE_VERBOSE_MAKEFILE:BOOL=FALSE

//Git command line client
GIT_EXECUTABLE:FILEPATH=C:/Program Files/Git/cmd/git.exe

//OpenGL library for win32
OPENGL_gl_LIBRARY:STRING=opengl32

//GLU library for win32
OPENGL_glu_LIBRARY:STRING=glu32

//Path to a file.
OPENTHREADS_INCLUDE_DIR:PATH=OPENTHREADS_INCLUDE_DIR-NOTFOUND

//Path to a library.
OPENTHREADS_LIBRARY_DEBUG:FILEPATH=OPENTHREADS_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OPENTHREADS_LIBRARY_RELEASE:FILEPATH=OPENTHREADS_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGDB_INCLUDE_DIR:PATH=OSGDB_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGDB_LIBRARY_DEBUG:FILEPATH=OSGDB_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGDB_LIBRARY_RELEASE:FILEPATH=OSGDB_LIBRARY_RELEASE-NOTFOUND

//Assume the use of a single GL context for all GL objects (advanced)
OSGEARTH_ASSUME_SINGLE_GL_CONTEXT:BOOL=OFF

//Assume OSG will always be configured to run in SingleThreaded
// mode (advanced)
OSGEARTH_ASSUME_SINGLE_THREADED_OSG:BOOL=OFF

//Value Computed by CMake
OSGEARTH_BINARY_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1

//Build the Cesium nodekit (osgEarthCesium)
OSGEARTH_BUILD_CESIUM_NODEKIT:BOOL=OFF

//Include the documentation folder
OSGEARTH_BUILD_DOCS:BOOL=ON

//Build the osgEarth example applications
OSGEARTH_BUILD_EXAMPLES:BOOL=ON

//Build the osgEarth ImGui nodekit and ImGui-based apps
OSGEARTH_BUILD_IMGUI_NODEKIT:BOOL=ON

//Build the legacy Controls UI API
OSGEARTH_BUILD_LEGACY_CONTROLS_API:BOOL=OFF

//Build the legacy procedural nodekit (osgEarthSplat)
OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT:BOOL=OFF

//Build the procedural terrain nodekit (osgEarthProcedural)
OSGEARTH_BUILD_PROCEDURAL_NODEKIT:BOOL=OFF

//ON to build shared libraries; OFF to build static libraries.
OSGEARTH_BUILD_SHARED_LIBS:BOOL=ON

//Build support for SunDog SilverLining SDK
OSGEARTH_BUILD_SILVERLINING_NODEKIT:BOOL=OFF

//Build the osgEarth unit tests
OSGEARTH_BUILD_TESTS:BOOL=OFF

//Build the osgEarth command-line tools
OSGEARTH_BUILD_TOOLS:BOOL=ON

//Build support for SunDog Triton SDK
OSGEARTH_BUILD_TRITON_NODEKIT:BOOL=OFF

//Build osgEarth's zip plugin based on libzip
OSGEARTH_BUILD_ZIP_PLUGIN:BOOL=ON

//Enable the geocoder (requires external geocoding service)
OSGEARTH_ENABLE_GEOCODER:BOOL=OFF

//Enable profiling with Tracy
OSGEARTH_ENABLE_PROFILING:BOOL=OFF

//osgEarth CMake package install directory
OSGEARTH_INSTALL_CMAKEDIR:STRING=lib/cmake/osgearth

//osgEarth data directory
OSGEARTH_INSTALL_DATADIR:STRING=share/osgearth

//Whether to deploy Windows .pdb files
OSGEARTH_INSTALL_PDBS:BOOL=OFF

//Parent folder of OSG plugins folder
OSGEARTH_INSTALL_PLUGINSDIR:STRING=bin

//Whether to deploy GLSL shaders when installing (OFF=inlined shaders)
OSGEARTH_INSTALL_SHADERS:BOOL=OFF

//Value Computed by CMake
OSGEARTH_IS_TOP_LEVEL:STATIC=ON

//ON to append so-version numbers to libraries
OSGEARTH_SONAMES:BOOL=ON

//Value Computed by CMake
OSGEARTH_SOURCE_DIR:STATIC=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth

//Path to a file.
OSGGA_INCLUDE_DIR:PATH=OSGGA_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGGA_LIBRARY_DEBUG:FILEPATH=OSGGA_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGGA_LIBRARY_RELEASE:FILEPATH=OSGGA_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGMANIPULATOR_INCLUDE_DIR:PATH=OSGMANIPULATOR_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGMANIPULATOR_LIBRARY_DEBUG:FILEPATH=OSGMANIPULATOR_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGMANIPULATOR_LIBRARY_RELEASE:FILEPATH=OSGMANIPULATOR_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGSHADOW_INCLUDE_DIR:PATH=OSGSHADOW_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGSHADOW_LIBRARY_DEBUG:FILEPATH=OSGSHADOW_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGSHADOW_LIBRARY_RELEASE:FILEPATH=OSGSHADOW_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGSIM_INCLUDE_DIR:PATH=OSGSIM_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGSIM_LIBRARY_DEBUG:FILEPATH=OSGSIM_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGSIM_LIBRARY_RELEASE:FILEPATH=OSGSIM_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGTEXT_INCLUDE_DIR:PATH=OSGTEXT_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGTEXT_LIBRARY_DEBUG:FILEPATH=OSGTEXT_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGTEXT_LIBRARY_RELEASE:FILEPATH=OSGTEXT_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGUTIL_INCLUDE_DIR:PATH=OSGUTIL_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGUTIL_LIBRARY_DEBUG:FILEPATH=OSGUTIL_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGUTIL_LIBRARY_RELEASE:FILEPATH=OSGUTIL_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSGVIEWER_INCLUDE_DIR:PATH=OSGVIEWER_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSGVIEWER_LIBRARY_DEBUG:FILEPATH=OSGVIEWER_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSGVIEWER_LIBRARY_RELEASE:FILEPATH=OSGVIEWER_LIBRARY_RELEASE-NOTFOUND

//Path to a file.
OSG_INCLUDE_DIR:PATH=OSG_INCLUDE_DIR-NOTFOUND

//Path to a library.
OSG_LIBRARY_DEBUG:FILEPATH=OSG_LIBRARY_DEBUG-NOTFOUND

//Path to a library.
OSG_LIBRARY_RELEASE:FILEPATH=OSG_LIBRARY_RELEASE-NOTFOUND


########################
# INTERNAL cache entries
########################

//ADVANCED property for variable: CMAKE_ADDR2LINE
CMAKE_ADDR2LINE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_AR
CMAKE_AR-ADVANCED:INTERNAL=1
//This is the directory where this CMakeCache.txt was created
CMAKE_CACHEFILE_DIR:INTERNAL=f:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth/build_wasm1
//Major version of cmake used to create the current loaded cache
CMAKE_CACHE_MAJOR_VERSION:INTERNAL=3
//Minor version of cmake used to create the current loaded cache
CMAKE_CACHE_MINOR_VERSION:INTERNAL=26
//Patch version of cmake used to create the current loaded cache
CMAKE_CACHE_PATCH_VERSION:INTERNAL=4
//Path to CMake executable.
CMAKE_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake.exe
//Path to cpack program executable.
CMAKE_CPACK_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cpack.exe
//Path to ctest program executable.
CMAKE_CTEST_COMMAND:INTERNAL=C:/Program Files/CMake/bin/ctest.exe
//ADVANCED property for variable: CMAKE_CXX_COMPILER
CMAKE_CXX_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_AR
CMAKE_CXX_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS
CMAKE_CXX_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_COMPILER_RANLIB
CMAKE_CXX_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS
CMAKE_CXX_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_DEBUG
CMAKE_CXX_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_MINSIZEREL
CMAKE_CXX_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELEASE
CMAKE_CXX_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_FLAGS_RELWITHDEBINFO
CMAKE_CXX_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_CXX_STANDARD_LIBRARIES
CMAKE_CXX_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER
CMAKE_C_COMPILER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_AR
CMAKE_C_COMPILER_AR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_CLANG_SCAN_DEPS
CMAKE_C_COMPILER_CLANG_SCAN_DEPS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_COMPILER_RANLIB
CMAKE_C_COMPILER_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS
CMAKE_C_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_DEBUG
CMAKE_C_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_MINSIZEREL
CMAKE_C_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELEASE
CMAKE_C_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_FLAGS_RELWITHDEBINFO
CMAKE_C_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_C_STANDARD_LIBRARIES
CMAKE_C_STANDARD_LIBRARIES-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_DLLTOOL
CMAKE_DLLTOOL-ADVANCED:INTERNAL=1
//Path to cache edit program executable.
CMAKE_EDIT_COMMAND:INTERNAL=C:/Program Files/CMake/bin/cmake-gui.exe
//Executable file format
CMAKE_EXECUTABLE_FORMAT:INTERNAL=Unknown
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS
CMAKE_EXE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_DEBUG
CMAKE_EXE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_MINSIZEREL
CMAKE_EXE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELEASE
CMAKE_EXE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_EXE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_EXPORT_COMPILE_COMMANDS
CMAKE_EXPORT_COMPILE_COMMANDS-ADVANCED:INTERNAL=1
//Name of external makefile project generator.
CMAKE_EXTRA_GENERATOR:INTERNAL=
//Name of generator.
CMAKE_GENERATOR:INTERNAL=Ninja
//Generator instance identifier.
CMAKE_GENERATOR_INSTANCE:INTERNAL=
//Name of generator platform.
CMAKE_GENERATOR_PLATFORM:INTERNAL=
//Name of generator toolset.
CMAKE_GENERATOR_TOOLSET:INTERNAL=
//Source directory with the top level CMakeLists.txt file for this
// project
CMAKE_HOME_DIRECTORY:INTERNAL=F:/cmo-dev/my_osgearth_web/osgearth_simple2/myosgearth
//ADVANCED property for variable: CMAKE_INSTALL_BINDIR
CMAKE_INSTALL_BINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATADIR
CMAKE_INSTALL_DATADIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DATAROOTDIR
CMAKE_INSTALL_DATAROOTDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_DOCDIR
CMAKE_INSTALL_DOCDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INCLUDEDIR
CMAKE_INSTALL_INCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_INFODIR
CMAKE_INSTALL_INFODIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBDIR
CMAKE_INSTALL_LIBDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LIBEXECDIR
CMAKE_INSTALL_LIBEXECDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALEDIR
CMAKE_INSTALL_LOCALEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_LOCALSTATEDIR
CMAKE_INSTALL_LOCALSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_MANDIR
CMAKE_INSTALL_MANDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_OLDINCLUDEDIR
CMAKE_INSTALL_OLDINCLUDEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_RUNSTATEDIR
CMAKE_INSTALL_RUNSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SBINDIR
CMAKE_INSTALL_SBINDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SHAREDSTATEDIR
CMAKE_INSTALL_SHAREDSTATEDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_INSTALL_SYSCONFDIR
CMAKE_INSTALL_SYSCONFDIR-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_LINKER
CMAKE_LINKER-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MAKE_PROGRAM
CMAKE_MAKE_PROGRAM-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS
CMAKE_MODULE_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_DEBUG
CMAKE_MODULE_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL
CMAKE_MODULE_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELEASE
CMAKE_MODULE_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_MODULE_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_NM
CMAKE_NM-ADVANCED:INTERNAL=1
//number of local generators
CMAKE_NUMBER_OF_MAKEFILES:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJCOPY
CMAKE_OBJCOPY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_OBJDUMP
CMAKE_OBJDUMP-ADVANCED:INTERNAL=1
//Platform information initialized
CMAKE_PLATFORM_INFO_INITIALIZED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RANLIB
CMAKE_RANLIB-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_COMPILER
CMAKE_RC_COMPILER-ADVANCED:INTERNAL=1
CMAKE_RC_COMPILER_WORKS:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS
CMAKE_RC_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_DEBUG
CMAKE_RC_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_MINSIZEREL
CMAKE_RC_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELEASE
CMAKE_RC_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_RC_FLAGS_RELWITHDEBINFO
CMAKE_RC_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_READELF
CMAKE_READELF-ADVANCED:INTERNAL=1
//Path to CMake installation.
CMAKE_ROOT:INTERNAL=C:/Program Files/CMake/share/cmake-3.26
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS
CMAKE_SHARED_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_DEBUG
CMAKE_SHARED_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL
CMAKE_SHARED_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELEASE
CMAKE_SHARED_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_SHARED_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_INSTALL_RPATH
CMAKE_SKIP_INSTALL_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_SKIP_RPATH
CMAKE_SKIP_RPATH-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS
CMAKE_STATIC_LINKER_FLAGS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_DEBUG
CMAKE_STATIC_LINKER_FLAGS_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL
CMAKE_STATIC_LINKER_FLAGS_MINSIZEREL-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELEASE
CMAKE_STATIC_LINKER_FLAGS_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO
CMAKE_STATIC_LINKER_FLAGS_RELWITHDEBINFO-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_STRIP
CMAKE_STRIP-ADVANCED:INTERNAL=1
//ADVANCED property for variable: CMAKE_VERBOSE_MAKEFILE
CMAKE_VERBOSE_MAKEFILE-ADVANCED:INTERNAL=1
//Details about finding OpenGL
FIND_PACKAGE_MESSAGE_DETAILS_OpenGL:INTERNAL=[opengl32][c ][v()]
//ADVANCED property for variable: GIT_EXECUTABLE
GIT_EXECUTABLE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_gl_LIBRARY
OPENGL_gl_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENGL_glu_LIBRARY
OPENGL_glu_LIBRARY-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENTHREADS_LIBRARY_DEBUG
OPENTHREADS_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OPENTHREADS_LIBRARY_RELEASE
OPENTHREADS_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_DEBUG
OSGDB_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGDB_LIBRARY_RELEASE
OSGDB_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ASSUME_SINGLE_GL_CONTEXT
OSGEARTH_ASSUME_SINGLE_GL_CONTEXT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ASSUME_SINGLE_THREADED_OSG
OSGEARTH_ASSUME_SINGLE_THREADED_OSG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_DOCS
OSGEARTH_BUILD_DOCS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_LEGACY_CONTROLS_API
OSGEARTH_BUILD_LEGACY_CONTROLS_API-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT
OSGEARTH_BUILD_LEGACY_SPLAT_NODEKIT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_PROCEDURAL_NODEKIT
OSGEARTH_BUILD_PROCEDURAL_NODEKIT-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_TESTS
OSGEARTH_BUILD_TESTS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_BUILD_ZIP_PLUGIN
OSGEARTH_BUILD_ZIP_PLUGIN-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_ENABLE_PROFILING
OSGEARTH_ENABLE_PROFILING-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGEARTH_INSTALL_SHADERS
OSGEARTH_INSTALL_SHADERS-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_DEBUG
OSGGA_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGGA_LIBRARY_RELEASE
OSGGA_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGMANIPULATOR_LIBRARY_DEBUG
OSGMANIPULATOR_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGMANIPULATOR_LIBRARY_RELEASE
OSGMANIPULATOR_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSHADOW_LIBRARY_DEBUG
OSGSHADOW_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSHADOW_LIBRARY_RELEASE
OSGSHADOW_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSIM_LIBRARY_DEBUG
OSGSIM_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGSIM_LIBRARY_RELEASE
OSGSIM_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGTEXT_LIBRARY_DEBUG
OSGTEXT_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGTEXT_LIBRARY_RELEASE
OSGTEXT_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_DEBUG
OSGUTIL_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGUTIL_LIBRARY_RELEASE
OSGUTIL_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_DEBUG
OSGVIEWER_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSGVIEWER_LIBRARY_RELEASE
OSGVIEWER_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_DEBUG
OSG_LIBRARY_DEBUG-ADVANCED:INTERNAL=1
//ADVANCED property for variable: OSG_LIBRARY_RELEASE
OSG_LIBRARY_RELEASE-ADVANCED:INTERNAL=1
//CMAKE_INSTALL_PREFIX during last run
_GNUInstallDirs_LAST_CMAKE_INSTALL_PREFIX:INTERNAL=C:/Program Files (x86)/OSGEARTH

