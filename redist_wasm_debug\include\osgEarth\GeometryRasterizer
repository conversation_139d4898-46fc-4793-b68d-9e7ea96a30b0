/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHSYMBOLOGY_GEOMETRY_RASTERIZER_H
#define OSGEARTHSYMBOLOGY_GEOMETRY_RASTERIZER_H 1

#include <osgEarth/Common>
#include <osgEarth/Geometry>
#include <osgEarth/Style>
#include <osg/Image>

namespace osgEarth
{
    /**
     * Draws geometry onto an Image canvas using software path-rendering.
     */
    class OSGEARTH_EXPORT GeometryRasterizer
    {
    public:
        GeometryRasterizer( int width, int height, const Style& style =Style() );

        GeometryRasterizer( osg::Image* image, const Style& style =Style() );

        /** dtor */
        virtual ~GeometryRasterizer();

        /** draws the geometry to the image. */
        void draw( const Geometry* geom, const osg::Vec4f& color =osg::Vec4f(1,1,1,1) );

        /** finishes the image and returns it. calls to draw() after this will have no effect. */
        osg::Image* finalize();

    private:
        osg::ref_ptr<osg::Image>      _image;
        Style                         _style;
        osg::ref_ptr<osg::Referenced> _state;
    };
} // namespace osgEarth

#endif // OSGEARTHSYMBOLOGY_GEOMETRY_RASTERIZER_H
