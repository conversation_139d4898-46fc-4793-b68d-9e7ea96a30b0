/**
 * 最简单的WebAssembly测试程序
 */

#include <iostream>
#include <cstdlib>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#endif

int main() {
    std::cout << "🌐 Hello WebAssembly!" << std::endl;
    std::cout << "这是一个最简单的WebAssembly测试程序" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "✅ Emscripten环境检测成功" << std::endl;
    
    // 测试环境变量设置
    setenv("TEST_VAR", "hello", 1);
    const char* test_val = getenv("TEST_VAR");
    if (test_val) {
        std::cout << "✅ 环境变量测试成功: " << test_val << std::endl;
    } else {
        std::cout << "❌ 环境变量测试失败" << std::endl;
    }
#else
    std::cout << "桌面环境" << std::endl;
#endif
    
    std::cout << "程序执行完成" << std::endl;
    return 0;
}
