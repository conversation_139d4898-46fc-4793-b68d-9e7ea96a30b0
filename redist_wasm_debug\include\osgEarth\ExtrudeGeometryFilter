/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Filter>
#include <osgEarth/Expression>
#include <osgEarth/Style>
#include <osg/Geode>
#include <vector>
#include <list>

namespace osgEarth
{
    class FeatureSourceIndex;
    class FeatureIndexBuilder;
}

namespace osgEarth { namespace Util
{
    using namespace osgEarth;

    class ResourceLibrary;

    /**
     * Extrudes footprint geometry into 3D geometry
     */
    class OSGEARTH_EXPORT ExtrudeGeometryFilter : public FeaturesToNodeFilter
    {
    public:
        struct HeightCallback : public osg::Referenced
        {
            virtual float operator()( Feature* input, const FilterContext& cx ) =0;
        };

    public:

        /** Constructs a new filter that will extrude footprints */
        ExtrudeGeometryFilter();

        virtual ~ExtrudeGeometryFilter() { }

        /**
         * Sets the style that will govern the geometry generation.
         */
        void setStyle( const Style& style );

        /**
         * Pushes a list of features through the filter.
         */
        osg::Node* push( FeatureList& input, FilterContext& context );

    public: // properties

        /**
         * Sets the maximum wall angle that doesn't require a new normal vector
         */
        void setWallAngleThreshold(float value) { _wallAngleThresh_deg = value; }
        float getWallAngleThreshold() const { return _wallAngleThresh_deg; }

        /**
         * Sets whether to render a bottom top. Useful for creating stencil volumes.
         */
        void setMakeStencilVolume( bool value ) { _makeStencilVolume = value; }
        
        /**
         * Sets the expression to evaluate when setting a feature name.
         * NOTE: setting this forces geometry-merging to OFF
         */
        void setFeatureNameExpr( const StringExpression& expr ) { _featureNameExpr = expr; }
        const StringExpression& getFeatureNameExpr() const { return _featureNameExpr; }

        /**
         * Whether to merge geometry for better rendering performance
         */
        void setMergeGeometry(bool value) { _mergeGeometry = value; }
        bool getMergeGeometry() const { return _mergeGeometry; }


    protected:

        // A Corner is one vertex in the source geometry, extrude from base to roof.
        struct Corner
        {
            osg::Vec3d base;
            osg::Vec3d roof;
            float roofTexU = 0.0f;
            float roofTexV = 0.0f;
            double offsetX = 0.0;
            float wallTexHeightAdjusted = 0.0f;
            bool isFromSource = true;
            float cosAngle = 0.0f;
            float height = 0.0f;
        };
        using Corners = std::list<Corner>; // use a list to prevent iterator invalidation

        // A Face joins to Corners.
        struct Face
        {
            Corner left;
            Corner right;
            double widthM = 1.0f;
        };
        using Faces = std::vector<Face>;
        
        // An Elevation is a series of related Faces.
        struct Elevation
        {
            Faces  faces;
            double texHeightAdjustedM = 0.0;

            unsigned getNumPoints() const {
                return faces.size() * 6;
            }
        };
        using Elevations = std::vector<Elevation>;

        // A Structure is a collection of related Elevations.
        struct Structure
        {
            Elevations elevations;
            bool isPolygon = true;
            osg::Vec3d baseCentroid;
            float verticalOffset = 0.0f;
            bool isInverted = false;

            unsigned getNumPoints() const {
                unsigned c = 0;
                for(Elevations::const_iterator e = elevations.begin(); e != elevations.end(); ++e ) {
                    c += e->getNumPoints();
                }
                return c;
            }
        };

        // a set of geodes indexed by stateset pointer, for pre-sorting geodes based on 
        // their texture usage
        typedef std::map<osg::StateSet*, osg::ref_ptr<osg::Group> > SortedGeodeMap;
        SortedGeodeMap                 _geodes;
        SortedGeodeMap                 _lineGroups;
        osg::ref_ptr<osg::StateSet>    _noTextureStateSet;

        typedef std::map < osg::StateSet*, osg::ref_ptr< osg::Geometry> > GeometryMap;
        GeometryMap                    _wallGeometries;
        GeometryMap                    _roofGeometries;
        GeometryMap                    _baselineGeometries;


        bool                           _mergeGeometry = true;
        float                          _wallAngleThresh_deg = 60.0f;
        float                          _cosWallAngleThresh = 0.0f;
        StringExpression               _featureNameExpr;
        osg::ref_ptr<HeightCallback>   _heightCallback;
        optional<NumericExpression>    _heightExpr;
        bool                           _makeStencilVolume = false;

        Style                          _style;
        bool                           _styleDirty = true;
        bool                           _gpuClamping = false;

        osg::ref_ptr<const ExtrusionSymbol> _extrusionSymbol;
        osg::ref_ptr<const PolygonSymbol>   _polySymbol;
        osg::ref_ptr<const SkinSymbol>      _wallSkinSymbol;
        osg::ref_ptr<const PolygonSymbol>   _wallPolygonSymbol;
        osg::ref_ptr<const SkinSymbol>      _roofSkinSymbol;
        osg::ref_ptr<const PolygonSymbol>   _roofPolygonSymbol;
        osg::ref_ptr<const LineSymbol>      _outlineSymbol;
        osg::ref_ptr<ResourceLibrary>       _wallResLib;
        osg::ref_ptr<ResourceLibrary>       _roofResLib;

        void reset( const FilterContext& context );
        
        void addDrawable( 
            osg::Drawable*       drawable, 
            osg::StateSet*       stateSet, 
            const std::string&   name,
            Feature*             feature,
            FeatureIndexBuilder* index);
        
        bool process( 
            FeatureList&     input,
            FilterContext&   context );
        
        bool buildStructure(const Geometry*         input,
                            double                  height,
                            double                  heightOffset,
                            bool                    flatten,
                            float                   verticalOffset,
                            const SkinResource*     wallSkin,
                            const SkinResource*     roofSkin,
                            Structure&              out_structure,
                            FilterContext&          cx );

        bool buildWallGeometry(const Structure&     structure,
                               Feature*             feature,
                               osg::Geometry*       walls,
                               const osg::Vec4&     wallColor,
                               const osg::Vec4&     wallBaseColor,
                               const SkinResource*  wallSkin,
                               float                shadeMin,
                               FeatureIndexBuilder* index);

        bool buildRoofGeometry(const Structure&     structure,
                               Feature* feature,
                               osg::Geometry*       roof,
                               const osg::Vec4&     roofColor,
                               const SkinResource*  roofSkin,
                               FeatureIndexBuilder* index);

        osg::Drawable* buildOutlineGeometry(const Structure& structure);
    };
} }
