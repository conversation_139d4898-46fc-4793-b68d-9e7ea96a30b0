#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
OSG和osgEarth WebAssembly测试程序编译器
"""

import os
import subprocess

def run_command(cmd, description):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print("-" * 50)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=os.getcwd())
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🚀 OSG和osgEarth WebAssembly测试程序编译器")
    print("==========================================")
    
    # 检查Emscripten
    emcc_check = """C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"""
    
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return
    
    # 设置第三方库路径
    third_party_path = "F:\\cmo-dev\\my_osgearth_web\\osgearth_third_party\\wasm_dep"
    
    # OSG库路径
    osg_include = f"{third_party_path}/include"
    osg_lib = f"{third_party_path}/lib"
    
    # 编译OSG球体测试程序
    osg_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_osg_sphere.cpp -o test_osg_sphere.js \\
        -I{osg_include} \\
        -L{osg_lib} \\
        -losg -losgViewer -losgGA -losgDB -losgUtil \\
        -s USE_SDL=2 \\
        -s USE_WEBGL2=1 \\
        -s FULL_ES3=1 \\
        -s ALLOW_MEMORY_GROWTH=1 \\
        -s LEGACY_GL_EMULATION=1 \\
        -s EXPORTED_FUNCTIONS="['_main']" \\
        -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']" \\
        -O2"""
    
    if run_command(osg_cmd, "编译OSG球体测试程序"):
        print("✅ OSG球体测试程序编译成功")
    else:
        print("❌ OSG球体测试程序编译失败")
    
    # 编译osgEarth完整测试程序
    osgearth_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcc ../src/applications/osgearth_myviewer/test_osgearth_full.cpp -o test_osgearth_full.js \\
        -I{osg_include} \\
        -I../src \\
        -L{osg_lib} \\
        -losgEarth -losgEarthUtil -losgEarthFeatures -losgEarthSymbology \\
        -losg -losgViewer -losgGA -losgDB -losgUtil -losgText \\
        -lweejobs \\
        -s USE_SDL=2 \\
        -s USE_WEBGL2=1 \\
        -s FULL_ES3=1 \\
        -s ALLOW_MEMORY_GROWTH=1 \\
        -s LEGACY_GL_EMULATION=1 \\
        -s USE_PTHREADS=1 \\
        -s PTHREAD_POOL_SIZE=4 \\
        -s SHARED_MEMORY=1 \\
        -s PROXY_TO_PTHREAD=1 \\
        -s EXPORTED_FUNCTIONS="['_main']" \\
        -s EXPORTED_RUNTIME_METHODS="['ccall', 'cwrap']" \\
        -O2"""
    
    if run_command(osgearth_cmd, "编译osgEarth完整测试程序"):
        print("✅ osgEarth完整测试程序编译成功")
    else:
        print("❌ osgEarth完整测试程序编译失败")
    
    print("\n🎯 编译完成！")
    print("测试地址:")
    print("  • OSG球体测试: http://localhost:8080/test_osg_sphere.html")
    print("  • osgEarth完整测试: http://localhost:8080/test_osgearth_full.html")
    print("\n注意事项:")
    print("  • 确保第三方库路径正确")
    print("  • 确保OSG和osgEarth库已编译为WebAssembly版本")
    print("  • 确保weejobs库已正确链接")

if __name__ == "__main__":
    main()
