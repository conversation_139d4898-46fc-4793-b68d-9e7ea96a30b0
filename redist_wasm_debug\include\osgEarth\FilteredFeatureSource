/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/FeatureSource>
#include <osgEarth/LayerReference>

namespace osgEarth
{    

    /**
     * Feature Layer that acts as a pass through to another feature source but applies a filter list to it.
     * This can be useful for limiting network requests to an online source.  For example you could have
     * an OSM layer that contains everything in OSM but then separate FilterFeatures layers
     * that filter out buildings for one and roads for another.
     */
    class OSGEARTH_EXPORT FilteredFeatureSource : public FeatureSource
    {
    public: // serialization
        class OSGEARTH_EXPORT Options : public FeatureSource::Options
        {
        public:
            META_LayerOptions(osgEarth, Options, FeatureSource::Options);
            OE_OPTION_LAYER(FeatureSource, featureSource);
            OE_OPTION_VECTOR(ConfigOptions, filters);
            virtual Config getConfig() const;
        private:
            void fromConfig(const Config& conf);
        };

    public:
        META_Layer(osgEarth, FilteredFeatureSource, Options, FeatureSource, FilteredFeatures);

        //! The feature source from which to read geometry.
        void setFeatureSource(FeatureSource* layer);
        FeatureSource* getFeatureSource() const;

        Status openImplementation() override;

        void addedToMap(const Map*) override;

        void removedFromMap(const Map*) override;

    protected:   
        FeatureCursor* createFeatureCursorImplementation(
            const Query& query,
            ProgressCallback* progress) const override;

        FeatureFilterChain _filters;
    }; 
}

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::FilteredFeatureSource::Options);
