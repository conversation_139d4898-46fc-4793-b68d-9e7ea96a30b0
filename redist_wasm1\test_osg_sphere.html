<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>OSG球体测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #1a1a2e, #16213e);
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .warning { border-left-color: #ffa500; background-color: #4a3a1a; }
        .canvas-container {
            background: linear-gradient(135deg, #0a0a2e, #1a1a3e);
            padding: 20px;
            border-radius: 10px;
            margin: 10px 0;
            text-align: center;
            border: 2px solid #333;
        }
        .controls {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        .control-group {
            text-align: center;
        }
        .control-group h4 {
            margin: 0 0 10px 0;
            color: #00d4ff;
        }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
        .log-success { color: #90ee90; }
        canvas {
            border: 3px solid #444;
            border-radius: 8px;
            box-shadow: 0 0 20px rgba(0, 212, 255, 0.3);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌍 OSG球体测试</h1>
        
        <div id="status" class="status">状态: 准备加载OSG球体测试...</div>
        
        <div class="status">
            <h3>🎯 测试目标</h3>
            <p>测试OSG库在WebAssembly中的基础3D渲染功能：</p>
            <ul>
                <li>OSG几何体创建和渲染</li>
                <li>3D球体显示</li>
                <li>坐标轴显示</li>
                <li>鼠标键盘交互</li>
                <li>TrackballManipulator相机控制</li>
            </ul>
        </div>
        
        <div class="canvas-container">
            <h3>🌍 OSG 3D渲染视窗</h3>
            <p>OSG库渲染的3D球体，支持完整的鼠标键盘交互</p>
            <canvas id="canvas" width="800" height="600"></canvas>
        </div>
        
        <div class="controls">
            <div class="control-group">
                <h4>🖱️ 鼠标控制</h4>
                <p>• 左键拖拽: 旋转视角</p>
                <p>• 右键拖拽: 缩放</p>
                <p>• 中键拖拽: 平移</p>
                <p>• 滚轮: 缩放</p>
            </div>
            
            <div class="control-group">
                <h4>⌨️ 键盘控制</h4>
                <p>• +/- 键: 调整旋转速度</p>
                <p>• 空格键: 重置视角</p>
                <p>• ESC 键: 退出</p>
            </div>
            
            <div class="control-group">
                <h4>🎨 渲染特性</h4>
                <p>• 3D球体几何体</p>
                <p>• 材质和光照</p>
                <p>• 坐标轴显示</p>
                <p>• 自动旋转动画</p>
            </div>
            
            <div class="control-group">
                <h4>🚀 技术特性</h4>
                <p>• OSG 3D引擎</p>
                <p>• WebAssembly + WebGL</p>
                <p>• 实时渲染</p>
                <p>• 事件处理</p>
            </div>
        </div>
        
        <div class="log">
            <h3>📝 系统日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">[${timestamp}]</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        // Module配置
        var Module = {
            canvas: document.getElementById('canvas'),
            
            print: function(text) {
                console.log('OSG Test:', text);
                addLog('输出: ' + text, 'info');
                
                // 检查成功和失败标记
                if (text.includes('✅')) {
                    addLog(text, 'success');
                } else if (text.includes('❌')) {
                    addLog(text, 'error');
                } else if (text.includes('⚠️')) {
                    addLog(text, 'warn');
                }
                
                // 检查特定事件
                if (text.includes('[event]')) {
                    addLog(text, 'info');
                } else if (text.includes('[osg]')) {
                    addLog(text, 'success');
                }
            },
            
            printErr: function(text) {
                console.error('OSG Test Error:', text);
                addLog('错误: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('OSG球体测试模块初始化完成');
                addLog('OSG球体测试模块初始化完成', 'success');
                updateStatus('OSG球体测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('OSG球体测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'success' : 'error');
                
                if (status === 0) {
                    updateStatus('🎉 OSG球体测试完成！', false, true);
                } else {
                    updateStatus('❌ OSG球体测试失败', true);
                }
            }
        };
        
        // 页面加载完成后初始化
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始OSG球体测试', 'info');
            
            updateStatus('加载OSG球体测试模块...');
            addLog('开始加载OSG球体测试模块', 'info');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_osg_sphere.js';
            script.onload = function() {
                addLog('OSG球体测试脚本加载成功', 'success');
                updateStatus('OSG球体测试脚本已加载');
            };
            script.onerror = function() {
                addLog('OSG球体测试脚本加载失败', 'error');
                updateStatus('OSG球体测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
