/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/Filter>
#include <osgEarth/Style>
#include <osg/Geode>

namespace osgEarth
{
    /**
     * Crops feature geometry to an extent, either by centroid or by actually
     * cutting the geometry.
     */
    class OSGEARTH_EXPORT CropFilter : public FeatureFilter
    {
    public:
        enum Method 
        {
            METHOD_CENTROID,       // include an entire feature if its centroid is included
            METHOD_BBOX,           // include an entire feature if its bounding box overlaps
            METHOD_CROP_TO_EXTENT, // crop a feature's geometry to the target extent

            METHOD_CROPPING = METHOD_CROP_TO_EXTENT // backwards compatibility
        };

    public:
        CropFilter(Method method = METHOD_CENTROID);
        virtual ~CropFilter() { }

        optional<Method>& method() { return _method; }
        const optional<Method>& method() const { return _method; }

    public:
        virtual FilterContext push( FeatureList& input, FilterContext& context );

    protected:
        optional<Method> _method;
    };
} // namespace osgEarth

