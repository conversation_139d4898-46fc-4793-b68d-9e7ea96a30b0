@echo off
REM 带有调试信息的WebAssembly编译脚本
REM 用于诊断和修复abort问题

setlocal enabledelayedexpansion

echo ===========================================
echo osgEarth WebAssembly Debug 编译脚本
echo ===========================================

REM 设置编码为UTF-8
chcp 65001 > nul

REM 检查Emscripten环境
if not exist "C:\dev\emsdk" (
    echo 错误: Emscripten SDK未找到，请确保已安装到C:\dev\emsdk
    pause
    exit /b 1
)

REM 激活Emscripten环境
echo 激活Emscripten环境...
call "C:\dev\emsdk\emsdk_env.bat"

if %errorlevel% neq 0 (
    echo 错误: 无法激活Emscripten环境
    pause
    exit /b 1
)

REM 检查emcc版本
echo 检查Emscripten版本...
emcc --version
if %errorlevel% neq 0 (
    echo 错误: emcc不可用
    pause
    exit /b 1
)

REM 设置编译选项
set "CMAKE_BUILD_TYPE=Debug"
set "BUILD_DIR=build_wasm_debug"
set "INSTALL_DIR=redist_wasm_debug"

REM 创建编译目录
echo 创建编译目录...
if exist "%BUILD_DIR%" (
    echo 清理旧的编译目录...
    rmdir /s /q "%BUILD_DIR%"
)
mkdir "%BUILD_DIR%"

REM 创建输出目录
if exist "%INSTALL_DIR%" (
    echo 清理旧的输出目录...
    rmdir /s /q "%INSTALL_DIR%"
)
mkdir "%INSTALL_DIR%"

REM 进入编译目录
cd "%BUILD_DIR%"

echo 开始CMake配置...
echo 使用调试设置来获取详细错误信息...

cmake -G "Ninja" ^
    -DCMAKE_BUILD_TYPE=Debug ^
    -DCMAKE_TOOLCHAIN_FILE=../cmake/emscripten.cmake ^
    -DCMAKE_INSTALL_PREFIX="../%INSTALL_DIR%" ^
    -DOSGEARTH_BUILD_SHARED_LIBS=OFF ^
    -DOSGEARTH_BUILD_EXAMPLES=OFF ^
    -DOSGEARTH_BUILD_TOOLS=OFF ^
    -DOSGEARTH_BUILD_TESTS=OFF ^
    -DOSGEARTH_BUILD_DOCS=OFF ^
    -DOSGEARTH_ENABLE_FASTDXT_DRIVER=OFF ^
    -DOSGEARTH_WEBASSEMBLY=ON ^
    -DCMAKE_CXX_FLAGS="-g -O1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s NO_EXIT_RUNTIME=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -std=c++17 -D__EMSCRIPTEN__ -Wno-deprecated-declarations -frtti -fexceptions" ^
    -DCMAKE_EXE_LINKER_FLAGS="-g -O1 -s WASM=1 -s ASSERTIONS=2 -s DEMANGLE_SUPPORT=1 -s NO_EXIT_RUNTIME=1 -s USE_SDL=2 -s USE_WEBGL2=1 -s FULL_ES3=1 -s ALLOW_MEMORY_GROWTH=1 -s INITIAL_MEMORY=128MB -s MAXIMUM_MEMORY=512MB -s FETCH=1 -s ASYNCIFY=1 -s USE_ZLIB=1 -s STACK_SIZE=1MB -s DISABLE_EXCEPTION_CATCHING=0 --shell-file ../html/shell.html" ^
    ..

if %errorlevel% neq 0 (
    echo 错误: CMake配置失败
    cd ..
    pause
    exit /b 1
)

echo CMake配置完成，开始编译...

REM 编译项目
ninja -v

if %errorlevel% neq 0 (
    echo 错误: 编译失败
    cd ..
    pause
    exit /b 1
)

echo 编译完成，安装文件...

REM 安装到输出目录
ninja install

if %errorlevel% neq 0 (
    echo 警告: 安装失败，但编译成功
)

cd ..

REM 复制HTML测试文件
echo 复制测试文件...
copy "debug.html" "%INSTALL_DIR%\debug.html"
copy "minimal.html" "%INSTALL_DIR%\minimal.html"
copy "simple.html" "%INSTALL_DIR%\simple.html"

REM 复制HTTP服务器
copy "redist_wasm\http_server.py" "%INSTALL_DIR%\http_server.py"

REM 创建启动脚本
echo 创建启动脚本...
echo @echo off > "%INSTALL_DIR%\start_debug.bat"
echo REM WebAssembly Debug 启动脚本 >> "%INSTALL_DIR%\start_debug.bat"
echo chcp 65001 >> "%INSTALL_DIR%\start_debug.bat"
echo echo 启动WebAssembly调试服务器... >> "%INSTALL_DIR%\start_debug.bat"
echo python http_server.py --port 8002 --dir . >> "%INSTALL_DIR%\start_debug.bat"
echo pause >> "%INSTALL_DIR%\start_debug.bat"

REM 显示编译结果
echo.
echo ===========================================
echo 调试版本编译完成!
echo ===========================================
echo 输出目录: %INSTALL_DIR%
echo 测试文件:
echo   - debug.html      (详细调试信息)
echo   - minimal.html    (最小配置)
echo   - simple.html     (简单配置)
echo.
echo 启动方式:
echo   1. 运行 %INSTALL_DIR%\start_debug.bat
echo   2. 在浏览器中打开 http://localhost:8002/debug.html
echo.
echo 调试特性:
echo   - 启用了详细断言 (ASSERTIONS=2)
echo   - 启用了符号反混淆 (DEMANGLE_SUPPORT=1)
echo   - 启用了异常处理 (DISABLE_EXCEPTION_CATCHING=0)
echo   - 减少了内存限制 (INITIAL_MEMORY=128MB, MAXIMUM_MEMORY=512MB)
echo   - 启用了调试信息 (-g)
echo   - 降低了优化级别 (-O1)
echo ===========================================

pause 