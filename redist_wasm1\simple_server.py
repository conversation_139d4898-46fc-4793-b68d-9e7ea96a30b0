#!/usr/bin/env python3
"""
简单稳定的HTTP服务器 - 支持SharedArrayBuffer
"""

import http.server
import socketserver
import os
import sys

class CORSHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def end_headers(self):
        # 添加CORS和SharedArrayBuffer支持的HTTP头
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', '*')
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 缓存控制
        if self.path.endswith(('.wasm', '.js')):
            self.send_header('Cache-Control', 'no-cache')
        
        super().end_headers()
    
    def do_OPTIONS(self):
        self.send_response(200)
        self.end_headers()
    
    def log_message(self, format, *args):
        # 简化日志输出
        print(f"[HTTP] {format % args}")

def main():
    port = 8084
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"无效端口号: {sys.argv[1]}")
            sys.exit(1)
    
    print(f"🌐 启动简单HTTP服务器")
    print(f"端口: {port}")
    print(f"目录: {os.getcwd()}")
    print(f"测试地址: http://localhost:{port}/")
    print("按 Ctrl+C 停止服务器")
    print("-" * 40)
    
    try:
        with socketserver.TCPServer(("", port), CORSHTTPRequestHandler) as httpd:
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n服务器已停止")
    except Exception as e:
        print(f"服务器错误: {e}")

if __name__ == "__main__":
    main()
