/**
 * SDL2 + osgEarth 数字地球显示程序
 * 专门为WebAssembly环境设计的简化版本
 */

#include <iostream>
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/XYZImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/Threading>

#include <osgViewer/Viewer>
#include <osg/DisplaySettings>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#include <SDL2/SDL.h>
#endif

using namespace osgEarth;
using namespace osgEarth::Util;

// 全局变量
static osgViewer::Viewer* g_viewer = nullptr;
static bool g_running = true;

// 创建简化的地图
osg::ref_ptr<Map> createSimpleMap() {
    std::cout << "[map] 创建简化数字地球地图..." << std::endl;
    
    auto map = new Map();
    
    try {
        // 添加Google卫星图像层
        auto imagery = new XYZImageLayer();
        imagery->setName("Google Satellite");
        imagery->setURL("https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}");
        imagery->setProfile(Profile::create("spherical-mercator"));
        map->addLayer(imagery);
        std::cout << "[map] ✅ Google卫星图像层添加成功" << std::endl;
        
        // 添加AWS地形高程层
        auto elevation = new XYZElevationLayer();
        elevation->setName("AWS Terrain");
        elevation->setURL("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png");
        elevation->setProfile(Profile::create("spherical-mercator"));
        map->addLayer(elevation);
        std::cout << "[map] ✅ AWS地形高程层添加成功" << std::endl;
        
    } catch (const std::exception& e) {
        std::cout << "[map] ⚠️ 图层添加异常: " << e.what() << std::endl;
    }
    
    std::cout << "[map] 地图创建完成，图层数: " << map->getNumLayers() << std::endl;
    return map;
}

// 配置查看器
void configureViewer(osgViewer::Viewer* viewer) {
    std::cout << "[viewer] 配置查看器..." << std::endl;
    
    // 设置单线程模式（WebAssembly推荐）
    viewer->setThreadingModel(osgViewer::Viewer::SingleThreaded);
    std::cout << "[viewer] ✅ 设置单线程模式" << std::endl;
    
    // 设置地球操控器
    auto manipulator = new EarthManipulator();
    viewer->setCameraManipulator(manipulator);
    std::cout << "[viewer] ✅ 设置地球操控器" << std::endl;
    
    // 配置显示设置
    auto ds = osg::DisplaySettings::instance();
    ds->setNumMultiSamples(0); // 禁用多重采样以提高性能
    std::cout << "[viewer] ✅ 配置显示设置" << std::endl;
    
#ifdef EMSCRIPTEN
    // WebAssembly特定配置
    viewer->getCamera()->setViewport(0, 0, 800, 600);
    std::cout << "[viewer] ✅ 设置WebAssembly视口: 800x600" << std::endl;
#endif
}

// 主循环函数
void main_loop() {
    if (!g_viewer || !g_running) {
        return;
    }
    
    try {
        if (!g_viewer->done()) {
            g_viewer->frame();
        } else {
            g_running = false;
            std::cout << "[loop] 查看器完成，停止主循环" << std::endl;
        }
    } catch (const std::exception& e) {
        std::cout << "[loop] ❌ 主循环异常: " << e.what() << std::endl;
        g_running = false;
    }
}

int main(int argc, char** argv) {
    std::cout << "🌍 SDL2 + osgEarth 数字地球查看器" << std::endl;
    std::cout << "=================================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
    
    // 检查多线程支持
    if (emscripten_has_threading_support()) {
        std::cout << "[main] ✅ 多线程支持已启用" << std::endl;
        // 设置环境变量减少线程使用
        setenv("OSGEARTH_NODEPAGER_CONCURRENCY", "1", 1);
        setenv("OSGEARTH_TERRAIN_CONCURRENCY", "1", 1);
    } else {
        std::cout << "[main] ⚠️ 多线程支持未启用，使用单线程模式" << std::endl;
    }
    
    // 初始化SDL2
    if (SDL_Init(SDL_INIT_VIDEO) < 0) {
        std::cout << "[main] ❌ SDL2初始化失败: " << SDL_GetError() << std::endl;
        return 1;
    }
    std::cout << "[main] ✅ SDL2初始化成功" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    try {
        // 创建地图
        std::cout << "[main] 创建地图..." << std::endl;
        auto map = createSimpleMap();
        if (!map.valid()) {
            std::cout << "[main] ❌ 地图创建失败" << std::endl;
            return 1;
        }
        
        // 创建地图节点
        std::cout << "[main] 创建地图节点..." << std::endl;
        auto mapNode = new MapNode(map.get());
        if (!mapNode) {
            std::cout << "[main] ❌ 地图节点创建失败" << std::endl;
            return 1;
        }
        std::cout << "[main] ✅ 地图节点创建成功" << std::endl;
        
        // 创建查看器
        std::cout << "[main] 创建查看器..." << std::endl;
        auto viewer = new osgViewer::Viewer();
        g_viewer = viewer;
        
        // 配置查看器
        configureViewer(viewer);
        
        // 设置场景
        viewer->setSceneData(mapNode);
        std::cout << "[main] ✅ 场景设置完成" << std::endl;
        
        // 实现查看器
        viewer->realize();
        std::cout << "[main] ✅ 查看器实现完成" << std::endl;
        
        // 设置初始视点（中国上空）
        auto manipulator = dynamic_cast<EarthManipulator*>(viewer->getCameraManipulator());
        if (manipulator) {
            Viewpoint vp;
            vp.focalPoint() = GeoPoint(SpatialReference::get("wgs84"), 116.3, 39.9, 0.0); // 北京
            vp.heading() = 0.0;
            vp.pitch() = -45.0;
            vp.range() = 2000000.0; // 2000km高度
            manipulator->setViewpoint(vp);
            std::cout << "[main] ✅ 设置初始视点：北京上空" << std::endl;
        }
        
#ifdef EMSCRIPTEN
        // WebAssembly主循环
        std::cout << "[main] 启动WebAssembly主循环..." << std::endl;
        emscripten_set_main_loop(main_loop, 60, 1); // 60 FPS
#else
        // 桌面主循环
        std::cout << "[main] 启动桌面主循环..." << std::endl;
        while (!viewer->done()) {
            viewer->frame();
        }
#endif
        
    } catch (const std::exception& e) {
        std::cout << "[main] ❌ 程序异常: " << e.what() << std::endl;
        return 1;
    }
    
#ifdef EMSCRIPTEN
    SDL_Quit();
#endif
    
    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
