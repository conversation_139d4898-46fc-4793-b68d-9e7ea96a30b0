/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/

#ifndef OSGEARTHUTIL_CLIPSPACE_H
#define OSGEARTHUTIL_CLIPSPACE_H

#include <osgEarth/Common>
#include <osgEarth/GeoData>

namespace osgEarth { namespace Util
{
    /**
    * Utility class for perform operations in clip space
    */
    class OSGEARTH_EXPORT ClipSpace
    {
    public:
        osg::Matrix _worldToClip, _clipToWorld;

        ClipSpace(const osg::Matrix& MVP, const osg::Matrix& MVPinv);

        // Moves the input point to the bottom edge of the viewport.
        void clampToBottom(GeoPoint& p);

        //! Moves the input point to bottom edge of the viewport
        //! along the vector from eye to p.
        void clampToBottom(GeoPoint& p, const GeoPoint& eye);

        // Moves the input point to left edge of the viewport.
        void clampToLeft(GeoPoint& p);

        //! Moves the input point to left edge of the viewport
        //! along the vector from eye to p.
        void clampToLeft(GeoPoint& p, const GeoPoint& eye);
    };

} } // namespace osgEarth::Util

#endif //OSGEARTHUTIL_CLIPSPACE_H
