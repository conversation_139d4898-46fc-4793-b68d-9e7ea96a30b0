/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_KML_OPTIONS
#define OSGEARTH_DRIVER_KML_OPTIONS 1

#include <osgEarth/Common>
#include <osgEarth/URI>
#include <osgEarth/Style>
#include <osg/Image>

namespace osgEarth { namespace KML
{
    using namespace osgEarth;

    /**
     * Options for the KML loader. You can pass an instance of this class
     * to KML::load()
     */
    class KMLOptions // NO EXPORT; header only
    {
    public:
        /** TextSymbol to use when no styles are set in the KML. */
        osg::ref_ptr<TextSymbol>& defaultTextSymbol() { return _defaultTextSymbol; }
        const osg::ref_ptr<TextSymbol>& defaultTextSymbol() const { return _defaultTextSymbol; }

        /** Default IconSymbol to use for placemarks that don't specify an icon or a model */
        osg::ref_ptr<IconSymbol>& defaultIconSymbol() { return _defaultIconSymbol; }
        const osg::ref_ptr<IconSymbol>& defaultIconSymbol() const { return _defaultIconSymbol; }

        /** Default base scale to apply to marker Icons. */
        optional<float>& iconBaseScale() { return _iconBaseScale; }
        const optional<float>& iconBaseScale() const { return _iconBaseScale; }

        /** Maximum size (either dimension) of placemarks icons */
        optional<unsigned>& iconMaxSize() { return _iconMaxSize; }
        const optional<unsigned>& iconMaxSize() const { return _iconMaxSize; }

        /** Automatically assign KML icons and labels to a decluttering bin */
        optional<bool>& declutter() { return _declutter; }
        const optional<bool>& declutter() const { return _declutter; }

        /** Specify a group to which to add screen-space items (2D icons and labels) */
        osg::ref_ptr<osg::Group> iconAndLabelGroup() { return _iconAndLabelGroup; }
        const osg::ref_ptr<osg::Group> iconAndLabelGroup() const { return _iconAndLabelGroup; }

        /** Default scale factor to apply to embedded 3D models */
        optional<float>& modelScale() { return _modelScale; }
        const optional<float>& modelScale() const { return _modelScale; }

        /** Default rotation to apply to embedded 3D models */
        optional<osg::Quat>& modelRotation() { return _modelRotation; }
        const optional<osg::Quat>& modelRotation() const { return _modelRotation; }

    public:
        KMLOptions() : _declutter(true), _iconBaseScale(1.0f), _iconMaxSize(32), _modelScale(1.0f)
        {
            _defaultTextSymbol = new TextSymbol();
            _defaultTextSymbol->size() = 18.0f;
            _defaultTextSymbol->halo() = Stroke(0.3f, 0.3f, 0.3f, 1.0f);

            _defaultIconSymbol = new IconSymbol();
            _defaultIconSymbol->url().mutable_value().setLiteral("https://github.com/gwaldron/osgearth/blob/master/data/placemark32.png?raw=true");
        }
    
        virtual ~KMLOptions() { }

    protected:
        osg::ref_ptr<IconSymbol> _defaultIconSymbol;
        osg::ref_ptr<TextSymbol> _defaultTextSymbol;
        optional<bool>           _declutter;
        optional<float>          _iconBaseScale;
        optional<unsigned>       _iconMaxSize;
        optional<float>          _modelScale;
        optional<osg::Quat>      _modelRotation;
        osg::ref_ptr<osg::Group> _iconAndLabelGroup;
    };

} } // namespace osgEarth::KML

#endif // OSGEARTH_DRIVER_KML_OPTIONS
