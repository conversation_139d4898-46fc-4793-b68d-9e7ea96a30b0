/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#pragma once

#include <osgEarth/Config>
#include <osgEarth/URI>
#include <osgEarth/Units>
#include <vector>
#include <stack>

namespace osgEarth
{
    struct String : public std::string {
    };

    /**
     * Simple numeric expression evaluator with variables.
     */
    class OSGEARTH_EXPORT NumericExpression
    {
    public:
        using Variable = std::pair<std::string, unsigned>;
        using Variables = std::vector<Variable>;

    public:
        //! Default constructor
        NumericExpression() = default;

        //! Copy constructor
        NumericExpression(const NumericExpression& rhs) = default;

        //! Deserializing constructor
        NumericExpression(const Config& conf);

        //! Construct a new expression from the infix string
        NumericExpression(const std::string& expr);

        //! Construct a new static expression from a value
        NumericExpression(double staticValue);

        //! Set the result to a literal value.
        void setLiteral(double staticValue);

        //! Access the expression variables.
        const Variables& variables() const { return _vars; }

        //! Set the value of a variable.
        void set(const Variable& var, double value);

        //! Evaluate the expression
        double eval() const;

        //! Gets the expression string
        const std::string& expr() const { return _src; }

        //! Whether the expression is empty
        bool empty() const { return _src.empty(); }

    public:
        Config getConfig() const;
        void mergeConfig(const Config& conf);

    private:
        enum Op { OPERAND, VARIABLE, ADD, SUB, MULT, DIV, MOD, MIN, MAX, LPAREN, RPAREN, COMMA }; // in low-high precedence order
        using Atom = std::pair<Op, double>;
        using AtomVector = std::vector<Atom>;
        using AtomStack = std::stack<Atom>;

        std::string _src;
        AtomVector _rpn;
        Variables _vars;
        double _value = 0.0;
        bool _dirty = true;

        void init();
    };

    //--------------------------------------------------------------------

    /**
     * Simple string expression evaluator with variables.
     */
    class OSGEARTH_EXPORT StringExpression
    {
    public:
        typedef std::pair<std::string, unsigned> Variable;
        typedef std::vector<Variable> Variables;

    public:
        //! Default construct
        StringExpression() = default;

        //! Copy construct
        StringExpression(const StringExpression& rhs) = default;

        //! Deserializing constructor
        StringExpression(const Config& conf);

        //! Construct a new expression from the infix string
        StringExpression(const std::string& expr);

        //! Construct an expression from the infix string and a URI context
        StringExpression(const std::string& expr, const URIContext& uriContext);

        //! Construct from a numeric value
        StringExpression(float value) :
            StringExpression(std::to_string(value)) { }

        //! Construct from a numeric value
        StringExpression(double value) :
            StringExpression(std::to_string(value)) { }

        //! Construct from a numeric value
        StringExpression(int value) :
            StringExpression(std::to_string(value)) { }

        /** Set the infix expr. */
        void setInfix(const std::string& infix);

        /** Set the infix expr to a literal string */
        void setLiteral(const std::string& value);

        /** Access the expression variables. */
        const Variables& variables() const { return _vars; }

        /** Set the value of a variable. */
        void set(const Variable& var, const std::string& value);

        /** Set the value of a names variable if it exists */
        void set(const std::string& varName, const std::string& value);

        /** Evaluate the expression. */
        const std::string& eval() const;

        /** Evaluate the expression as a URI.
            TODO: it would be better to have a whole new subclass URIExpression */
        URI evalURI() const;

        /** Gets the expression string. */
        const std::string& expr() const { return _src; }

        /** Whether the expression is empty */
        bool empty() const { return _src.empty(); }

        void setURIContext(const URIContext& uriContext) { _uriContext = uriContext; }
        const URIContext& uriContext() const { return _uriContext; }

    public:
        Config getConfig() const;
        void mergeConfig(const Config& conf);

    private:
        enum Op { OPERAND, VARIABLE }; // in low-high precedence order
        typedef std::pair<Op, std::string> Atom;
        typedef std::vector<Atom> AtomVector;

        std::string  _src;
        AtomVector   _infix;
        Variables    _vars;
        std::string  _value = {};
        bool         _dirty = true;
        URIContext   _uriContext;

        void init();
    };


    /**
    * A container for a value that can either be a literal value or
    * an evaluated expression.
    */
    template<typename T>
    class Expression
    {
    public:
        //! Construct empty expression
        Expression() = default;

        //! Construct with an expression string that will be evaluated
        Expression(const std::string& input) :
            _expression(input)
        {
            preevaluate();
        }

        //! Construct with an expression string that will be evaluated
        Expression(const std::string& input, const std::string& referrer) :
            _expression(input),
            _referrer(referrer)
        {
            preevaluate();
        }

        //! Construct with a literal (pre-evaluated) value.
        Expression(const T& literal_value)
        {
            _expression = {};
            _literal = literal_value;
            preevaluate();
        }

        //! Deserialize
        Expression(const Config& conf)
        {
            conf.get("expression", _expression);
            conf.get("literal", _literal);
            conf.get("default_units", _defaultUnits);
            _referrer = conf.referrer();
            preevaluate();
        }

        //! Sets the units to use when calculating a value from either 
        //! a unit-less literal or a unit-less expression result.
        inline void setDefaultUnits(const UnitsType& value)
        {
            _defaultUnits = value;
            preevaluate();
        }

        //! Serialize
        inline Config getConfig() const
        {
            return Config()
                .set("expression", _expression)
                .set("literal", _literal)
                .set("default_units", _defaultUnits);
        }

        //! Evaluate the expression in the context of a feature.
        //! This method is capable of invoking any global evaluateExpression(...)
        //! function that takes a string as its first argument.
        template<typename... Args>
        inline T eval(Args... args) const
        {
            if (_literal.isSet())
                return _literal.value();

            if (_preevaluated.isSet())
                return _preevaluated.value();

            return construct(evaluateExpression(_expression, args...));
        }

        //! Return a copy of the literal value
        inline const T& literal() const
        {
            if (_literal.isSet())
                return _literal.value();
            else
                return _preevaluated.value();
        }

        //! Attempt to pre-evaluate a simple expression
        inline void preevaluate()
        {
            if (!_literal.isSet())
            {
                _preevaluated.unset();
                auto a = osgEarth::isValidNumber(_expression);
                if (a.first == true)
                    _preevaluated = a.second;
            }
        }        

    private:
        std::string _expression;
        optional<T> _literal;
        optional<UnitsType> _defaultUnits = Units::METERS;
        optional<T> _preevaluated;
        std::string _referrer;

        inline T construct(const std::string& r) const
        {
            return T(r);
        }
    };

} // namespace osgEarth

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::NumericExpression);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::StringExpression);

OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<float>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<double>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<int>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<unsigned>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<bool>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::String>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::Distance>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::Angle>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::Duration>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::Speed>);
OSGEARTH_SPECIALIZE_CONFIG(osgEarth::Expression<osgEarth::URI>);

template<> inline bool osgEarth::Expression<bool>::construct(const std::string& r) const {
    return r == "true" || r == "1" || r == "yes" || r == "on";
}
template<> inline float osgEarth::Expression<float>::construct(const std::string& r) const {
    return std::atof(r.c_str());
}
template<> inline double osgEarth::Expression<double>::construct(const std::string& r) const {
    return std::atof(r.c_str());
}
template<> inline int osgEarth::Expression<int>::construct(const std::string& r) const {
    return std::atoi(r.c_str());
}
template<> inline unsigned osgEarth::Expression<unsigned>::construct(const std::string& r) const {
    return std::atoi(r.c_str());
}
template<> inline osgEarth::String osgEarth::Expression<osgEarth::String>::construct(const std::string& r) const {
    return osgEarth::String{ r };
}
template<> inline osgEarth::Distance osgEarth::Expression<osgEarth::Distance>::construct(const std::string& r) const {
    return _defaultUnits.isSet() ? osgEarth::Distance(r, _defaultUnits.value()) : osgEarth::Distance(r, osgEarth::Units::METERS);
}
template<> inline osgEarth::Angle osgEarth::Expression<osgEarth::Angle>::construct(const std::string& r) const {
    return _defaultUnits.isSet() ? osgEarth::Angle(r, _defaultUnits.value()) : osgEarth::Angle(r, osgEarth::Units::DEGREES);
}
template<> inline osgEarth::Duration osgEarth::Expression<osgEarth::Duration>::construct(const std::string& r) const {
    return _defaultUnits.isSet() ? osgEarth::Duration(r, _defaultUnits.value()) : osgEarth::Duration(r, osgEarth::Units::DEGREES);
}
template<> inline osgEarth::Speed osgEarth::Expression<osgEarth::Speed>::construct(const std::string& r) const {
    return _defaultUnits.isSet() ? osgEarth::Speed(r, _defaultUnits.value()) : osgEarth::Speed(r, osgEarth::Units::DEGREES);
}
template<> inline osgEarth::URI osgEarth::Expression<osgEarth::URI>::construct(const std::string& r) const {
    return osgEarth::URI(r, osgEarth::URIContext(_referrer));
}

template<> inline void osgEarth::Expression<bool>::preevaluate() {
    if (!_literal.isSet()) {
        _preevaluated.unset(); 
        if (_expression == "true" || _expression == "1" || _expression == "yes" || _expression == "on" || _expression == "TRUE" || _expression == "YES" || _expression == "ON")
            _preevaluated = true;
        else if (_expression == "false" || _expression == "0" || _expression == "no" || _expression == "off" || _expression == "FALSE" || _expression == "NO" || _expression == "OFF")
            _preevaluated = false;
    }
}
template<> inline void osgEarth::Expression<osgEarth::String>::preevaluate() {
    //nop
}
template<> inline void osgEarth::Expression<osgEarth::Distance>::preevaluate() {
    if (!_literal.isSet()) {
        _preevaluated.unset();
        auto a = construct(_expression);
        if (a.fullyParsed()) _preevaluated = a;
    }
}
template<> inline void osgEarth::Expression<osgEarth::Angle>::preevaluate() {
    if (!_literal.isSet()) {
        _preevaluated.unset();
        auto a = construct(_expression);
        if (a.fullyParsed()) _preevaluated = a;
    }
}
template<> inline void osgEarth::Expression<osgEarth::Duration>::preevaluate() {
    if (!_literal.isSet()) {
        _preevaluated.unset();
        auto a = construct(_expression);
        if (a.fullyParsed()) _preevaluated = a;
    }
}
template<> inline void osgEarth::Expression<osgEarth::Speed>::preevaluate() {
    if (!_literal.isSet()) {
        _preevaluated.unset();
        auto a = construct(_expression);
        if (a.fullyParsed()) _preevaluated = a;
    }
}
template<> inline void osgEarth::Expression<osgEarth::URI>::preevaluate() {
    //nop
}
