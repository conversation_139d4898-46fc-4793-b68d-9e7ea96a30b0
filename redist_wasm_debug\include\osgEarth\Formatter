/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_UTIL_FORMATTER_H
#define OSGEARTH_UTIL_FORMATTER_H

#include <osgEarth/Common>
#include <osgEarth/GeoData>

namespace osgEarth { namespace Util
{
    /**
     * Interface class for coordinate formatters.
     */
    class Formatter : public osg::Referenced
    {
    public:
        virtual std::string format( const GeoPoint& mapCoords ) const =0;
        std::string operator()(const GeoPoint& p) const { return format(p); }

        /** dtor */
        virtual ~Formatter() { }
    };
} }

#endif // OSGEARTH_UTIL_FORMATTER_H
