/* osgEarth
* Copyright 2025 Pelican Mapping
* MIT License
*/
#pragma once

#include <osgEarth/Common>
#include <osg/BoundingBox>

namespace osgEarth
{
    using Bounds = osg::BoundingBoxd;

    inline double width(const Bounds& b) {
        return b.xMax() - b.xMin();
    }

    inline double height(const Bounds& b) {
        return b.yMax() - b.yMin();
    }

    inline double area2d(const Bounds& b) {
        return b.valid() ? width(b) * height(b) : -1.0;
    }

    inline bool contains(const Bounds& b, double x, double y) {
        return
            b.valid() &&
            x >= b.xMin() && x <= b.xMax() &&
            y >= b.yMin() && y <= b.yMax();
    }

    inline bool intersects2d(const Bounds& lhs, const Bounds& rhs) {
        return
            lhs.xMax() >= rhs.xMin() && lhs.xMin() <= rhs.xMax() &&
            lhs.yMax() >= rhs.yMin() && lhs.yMin() <= rhs.yMax();
    }

    extern OSGEARTH_EXPORT bool contains(const Bounds& lhs, const Bounds& rhs);

    extern OSGEARTH_EXPORT Bounds intersectionOf(const Bounds& lhs, const Bounds& rhs);

    extern OSGEARTH_EXPORT Bounds unionOf(const Bounds& lhs, const Bounds& rhs);
}
