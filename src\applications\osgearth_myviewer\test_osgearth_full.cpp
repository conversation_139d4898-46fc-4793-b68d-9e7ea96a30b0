/**
 * osgEarth完整数字地球测试程序
 * 测试WebAssembly环境下osgEarth库的完整功能
 * 包含weejobs线程池支持
 */

#include <iostream>
#include <memory>

// osgEarth核心头文件
#include <osgEarth/Map>
#include <osgEarth/MapNode>
#include <osgEarth/ImageLayer>
#include <osgEarth/ElevationLayer>
#include <osgEarth/ModelLayer>
#include <osgEarth/XYZImageLayer>
#include <osgEarth/TerrainEngineNode>

// OSG核心头文件
#include <osgViewer/Viewer>
#include <osgGA/TrackballManipulator>
#include <osgGA/StateSetManipulator>
#include <osgViewer/ViewerEventHandlers>

// osgEarth工具头文件
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/Sky>

// weejobs线程池
#include <weejobs/weejobs.h>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/html5.h>
#endif

using namespace osgEarth;
using namespace osgEarth::Util;

// 全局变量
static osgViewer::Viewer* g_viewer = nullptr;
static osg::ref_ptr<MapNode> g_mapNode = nullptr;
static bool g_running = true;
static std::shared_ptr<weejobs::job_pool> g_thread_pool = nullptr;

// 创建Google卫星图像层
osg::ref_ptr<ImageLayer> createGoogleSatelliteLayer() {
    std::cout << "[earth] 创建Google卫星图像层..." << std::endl;
    
    XYZImageLayer::Options options;
    options.url() = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
    options.profile() = ProfileOptions("spherical-mercator");
    
    osg::ref_ptr<XYZImageLayer> layer = new XYZImageLayer(options);
    layer->setName("Google Satellite");
    
    std::cout << "[earth] ✅ Google卫星图像层创建成功" << std::endl;
    return layer;
}

// 创建AWS地形高程层
osg::ref_ptr<ElevationLayer> createAWSElevationLayer() {
    std::cout << "[earth] 创建AWS地形高程层..." << std::endl;
    
    XYZElevationLayer::Options options;
    options.url() = "https://s3.amazonaws.com/elevation-tiles-prod/terrarium/{z}/{x}/{y}.png";
    options.profile() = ProfileOptions("spherical-mercator");
    
    osg::ref_ptr<XYZElevationLayer> layer = new XYZElevationLayer(options);
    layer->setName("AWS Terrarium");
    
    std::cout << "[earth] ✅ AWS地形高程层创建成功" << std::endl;
    return layer;
}

// 创建地图
osg::ref_ptr<Map> createMap() {
    std::cout << "[earth] 创建地图..." << std::endl;
    
    osg::ref_ptr<Map> map = new Map();
    
    // 添加图像层
    map->addLayer(createGoogleSatelliteLayer());
    
    // 添加高程层
    map->addLayer(createAWSElevationLayer());
    
    std::cout << "[earth] ✅ 地图创建成功" << std::endl;
    return map;
}

// 自定义事件处理器
class EarthEventHandler : public osgGA::GUIEventHandler {
public:
    virtual bool handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa) {
        switch (ea.getEventType()) {
            case osgGA::GUIEventAdapter::KEYDOWN:
                std::cout << "[event] 按键按下: " << ea.getKey() << std::endl;
                
                switch (ea.getKey()) {
                    case osgGA::GUIEventAdapter::KEY_Escape:
                        std::cout << "[event] ESC键，准备退出" << std::endl;
                        g_running = false;
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_Space:
                        std::cout << "[event] 空格键，重置视角到地球" << std::endl;
                        if (g_mapNode.valid()) {
                            osgEarth::Util::EarthManipulator* manip = 
                                dynamic_cast<osgEarth::Util::EarthManipulator*>(aa.getCameraManipulator());
                            if (manip) {
                                manip->setViewpoint(Viewpoint("", 116.3, 39.9, 0.0, -90.0, -90.0, 15000000.0));
                            }
                        }
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_1:
                        std::cout << "[event] 1键，飞到北京" << std::endl;
                        if (g_mapNode.valid()) {
                            osgEarth::Util::EarthManipulator* manip = 
                                dynamic_cast<osgEarth::Util::EarthManipulator*>(aa.getCameraManipulator());
                            if (manip) {
                                manip->setViewpoint(Viewpoint("Beijing", 116.3, 39.9, 0.0, -90.0, -30.0, 50000.0));
                            }
                        }
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_2:
                        std::cout << "[event] 2键，飞到纽约" << std::endl;
                        if (g_mapNode.valid()) {
                            osgEarth::Util::EarthManipulator* manip = 
                                dynamic_cast<osgEarth::Util::EarthManipulator*>(aa.getCameraManipulator());
                            if (manip) {
                                manip->setViewpoint(Viewpoint("New York", -74.0, 40.7, 0.0, -90.0, -30.0, 50000.0));
                            }
                        }
                        return true;
                        
                    case osgGA::GUIEventAdapter::KEY_T:
                        std::cout << "[event] T键，测试线程池任务" << std::endl;
                        testThreadPool();
                        return true;
                }
                break;
                
            case osgGA::GUIEventAdapter::PUSH:
                std::cout << "[event] 鼠标按下: (" << ea.getX() << ", " << ea.getY() << ") 按钮: " << ea.getButton() << std::endl;
                return false;
                
            case osgGA::GUIEventAdapter::RELEASE:
                std::cout << "[event] 鼠标释放: (" << ea.getX() << ", " << ea.getY() << ") 按钮: " << ea.getButton() << std::endl;
                return false;
                
            case osgGA::GUIEventAdapter::SCROLL:
                std::cout << "[event] 鼠标滚轮: " << ea.getScrollingMotion() << std::endl;
                return false;
        }
        
        return false;
    }
    
private:
    void testThreadPool() {
        if (!g_thread_pool) {
            std::cout << "[thread] 线程池未初始化" << std::endl;
            return;
        }
        
        std::cout << "[thread] 提交测试任务到线程池..." << std::endl;
        
        // 提交一些测试任务
        for (int i = 0; i < 5; ++i) {
            g_thread_pool->dispatch([i]() {
                std::cout << "[thread] 执行任务 " << i << " 在线程中..." << std::endl;
                
                // 模拟一些工作
                for (int j = 0; j < 1000000; ++j) {
                    volatile int dummy = j * j;
                }
                
                std::cout << "[thread] 任务 " << i << " 完成" << std::endl;
            });
        }
        
        std::cout << "[thread] 所有测试任务已提交" << std::endl;
    }
};

// 初始化线程池
void initializeThreadPool() {
    std::cout << "[thread] 初始化weejobs线程池..." << std::endl;
    
    try {
        // 创建线程池，使用4个工作线程
        g_thread_pool = std::make_shared<weejobs::job_pool>(4);
        
        std::cout << "[thread] ✅ weejobs线程池初始化成功，工作线程数: 4" << std::endl;
        
        // 测试线程池
        g_thread_pool->dispatch([]() {
            std::cout << "[thread] 线程池测试任务执行成功" << std::endl;
        });
        
    } catch (const std::exception& e) {
        std::cout << "[thread] ❌ 线程池初始化失败: " << e.what() << std::endl;
    }
}

// 主循环函数
void main_loop() {
    if (!g_running || !g_viewer) {
        return;
    }
    
    // 渲染一帧
    g_viewer->frame();
}

int main(int argc, char* argv[]) {
    std::cout << "🌍 osgEarth完整数字地球测试程序" << std::endl;
    std::cout << "================================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境" << std::endl;
#else
    std::cout << "[main] 桌面环境" << std::endl;
#endif
    
    try {
        // 初始化线程池
        initializeThreadPool();
        
        // 创建查看器
        g_viewer = new osgViewer::Viewer();
        
        // 创建地图和地图节点
        osg::ref_ptr<Map> map = createMap();
        g_mapNode = new MapNode(map);
        
        // 设置场景
        g_viewer->setSceneData(g_mapNode);
        
        // 设置地球操作器
        osg::ref_ptr<osgEarth::Util::EarthManipulator> earthManip = new osgEarth::Util::EarthManipulator();
        g_viewer->setCameraManipulator(earthManip);
        
        // 设置初始视点（北京上空）
        earthManip->setViewpoint(Viewpoint("Beijing", 116.3, 39.9, 0.0, -90.0, -30.0, 15000000.0));
        
        // 添加事件处理器
        g_viewer->addEventHandler(new EarthEventHandler());
        g_viewer->addEventHandler(new osgGA::StateSetManipulator(g_viewer->getCamera()->getOrCreateStateSet()));
        g_viewer->addEventHandler(new osgViewer::StatsHandler());
        g_viewer->addEventHandler(new osgViewer::WindowSizeHandler());
        
        // 设置窗口大小
        g_viewer->setUpViewInWindow(100, 100, 800, 600);
        
        // 初始化查看器
        g_viewer->realize();
        
        std::cout << "[main] ✅ osgEarth查看器初始化成功" << std::endl;
        std::cout << "[main] 控制说明:" << std::endl;
        std::cout << "[main] - 鼠标左键拖拽: 旋转地球" << std::endl;
        std::cout << "[main] - 鼠标右键拖拽: 缩放" << std::endl;
        std::cout << "[main] - 鼠标中键拖拽: 平移" << std::endl;
        std::cout << "[main] - 空格键: 重置视角" << std::endl;
        std::cout << "[main] - 1键: 飞到北京" << std::endl;
        std::cout << "[main] - 2键: 飞到纽约" << std::endl;
        std::cout << "[main] - T键: 测试线程池" << std::endl;
        std::cout << "[main] - ESC键: 退出" << std::endl;
        
#ifdef EMSCRIPTEN
        // WebAssembly主循环
        emscripten_set_main_loop(main_loop, 60, 1);
#else
        // 桌面主循环
        while (g_running && !g_viewer->done()) {
            main_loop();
        }
#endif
        
    } catch (const std::exception& e) {
        std::cout << "[main] ❌ 异常: " << e.what() << std::endl;
        return 1;
    }
    
    // 清理线程池
    if (g_thread_pool) {
        std::cout << "[thread] 清理线程池..." << std::endl;
        g_thread_pool.reset();
    }
    
    std::cout << "[main] 程序正常结束" << std::endl;
    return 0;
}
