#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
weejobs线程池库WebAssembly编译脚本
编译weejobs为WebAssembly版本
"""

import os
import subprocess
import sys

def run_command(cmd, description, cwd=None):
    """运行命令并显示结果"""
    print(f"\n🔨 {description}")
    print(f"命令: {cmd}")
    print(f"工作目录: {cwd if cwd else os.getcwd()}")
    print("-" * 60)
    
    try:
        result = subprocess.run(cmd, shell=True, capture_output=True, text=True, cwd=cwd)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        success = result.returncode == 0
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        if not success:
            print(f"返回码: {result.returncode}")
        
        return success
        
    except Exception as e:
        print(f"❌ 执行命令时出错: {e}")
        return False

def main():
    print("🧵 weejobs线程池库WebAssembly编译器")
    print("=================================")
    print("编译weejobs为WebAssembly版本")
    
    # 设置路径
    base_dir = "F:\\cmo-dev\\my_osgearth_web"
    weejobs_source_dir = f"{base_dir}\\osgearth_simple2\\myosgearth\\src\\weejobs"
    weejobs_build_dir = f"{base_dir}\\weejobs_wasm_build"
    install_dir = f"{base_dir}\\osgearth_third_party\\wasm_dep"
    
    print(f"weejobs源码目录: {weejobs_source_dir}")
    print(f"构建目录: {weejobs_build_dir}")
    print(f"安装目录: {install_dir}")
    
    # 检查源码目录
    if not os.path.exists(weejobs_source_dir):
        print(f"❌ weejobs源码目录不存在: {weejobs_source_dir}")
        print("weejobs是头文件库，将直接复制头文件")
        
        # 创建weejobs头文件目录
        weejobs_include_dir = f"{install_dir}\\include\\weejobs"
        if not os.path.exists(weejobs_include_dir):
            os.makedirs(weejobs_include_dir)
        
        # 创建简化的weejobs头文件
        weejobs_header = f"{weejobs_include_dir}\\weejobs.h"
        with open(weejobs_header, 'w', encoding='utf-8') as f:
            f.write('''#pragma once
#include <functional>
#include <memory>
#include <thread>
#include <vector>
#include <queue>
#include <mutex>
#include <condition_variable>
#include <future>

namespace weejobs {

class job_pool {
public:
    explicit job_pool(size_t num_threads = std::thread::hardware_concurrency()) 
        : stop(false) {
        for (size_t i = 0; i < num_threads; ++i) {
            workers.emplace_back([this] {
                for (;;) {
                    std::function<void()> task;
                    {
                        std::unique_lock<std::mutex> lock(this->queue_mutex);
                        this->condition.wait(lock, [this] { return this->stop || !this->tasks.empty(); });
                        if (this->stop && this->tasks.empty())
                            return;
                        task = std::move(this->tasks.front());
                        this->tasks.pop();
                    }
                    task();
                }
            });
        }
    }

    template<class F, class... Args>
    auto dispatch(F&& f, Args&&... args) 
        -> std::future<typename std::result_of<F(Args...)>::type> {
        using return_type = typename std::result_of<F(Args...)>::type;

        auto task = std::make_shared<std::packaged_task<return_type()>>(
            std::bind(std::forward<F>(f), std::forward<Args>(args)...)
        );

        std::future<return_type> res = task->get_future();
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            if (stop)
                throw std::runtime_error("dispatch on stopped job_pool");
            tasks.emplace([task]() { (*task)(); });
        }
        condition.notify_one();
        return res;
    }

    ~job_pool() {
        {
            std::unique_lock<std::mutex> lock(queue_mutex);
            stop = true;
        }
        condition.notify_all();
        for (std::thread &worker : workers)
            worker.join();
    }

private:
    std::vector<std::thread> workers;
    std::queue<std::function<void()>> tasks;
    std::mutex queue_mutex;
    std::condition_variable condition;
    bool stop;
};

} // namespace weejobs
''')
        
        print(f"✅ 创建weejobs头文件: {weejobs_header}")
        return True
    
    # 创建构建目录
    if not os.path.exists(weejobs_build_dir):
        os.makedirs(weejobs_build_dir)
        print(f"✅ 创建构建目录: {weejobs_build_dir}")
    
    # 检查Emscripten
    emcc_check = "C:\\dev\\emsdk\\emsdk_env.bat && emcc --version"
    if not run_command(emcc_check, "检查Emscripten"):
        print("❌ Emscripten检查失败，请确保已正确安装")
        return False
    
    # 创建CMakeLists.txt
    cmake_content = f'''cmake_minimum_required(VERSION 3.10)
project(weejobs)

set(CMAKE_CXX_STANDARD 14)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 头文件库，只需要安装头文件
install(DIRECTORY "{weejobs_source_dir}/" 
        DESTINATION include/weejobs
        FILES_MATCHING PATTERN "*.h" PATTERN "*.hpp")
'''
    
    cmake_file = f"{weejobs_build_dir}\\CMakeLists.txt"
    with open(cmake_file, 'w') as f:
        f.write(cmake_content)
    
    # 配置CMake
    cmake_cmd = f"""C:\\dev\\emsdk\\emsdk_env.bat && emcmake cmake . ^
        -DCMAKE_BUILD_TYPE=Release ^
        -DCMAKE_INSTALL_PREFIX="{install_dir}" """
    
    if not run_command(cmake_cmd, "配置weejobs CMake", weejobs_build_dir):
        print("❌ weejobs CMake配置失败")
        return False
    
    # 安装weejobs（只是复制头文件）
    install_cmd = "C:\\dev\\emsdk\\emsdk_env.bat && emmake make install"
    if not run_command(install_cmd, "安装weejobs库", weejobs_build_dir):
        print("❌ weejobs安装失败")
        return False
    
    print("\n🎉 weejobs线程池库WebAssembly编译完成！")
    print(f"安装位置: {install_dir}\\include\\weejobs")
    print("\n说明:")
    print("  weejobs是头文件库，已安装到include目录")
    print("  可以在C++代码中直接#include <weejobs/weejobs.h>使用")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
