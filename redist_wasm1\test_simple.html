<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>简单LOD逻辑测试</title>
    <style>
        body {
            font-family: 'Consolas', 'Monaco', monospace;
            margin: 0;
            padding: 20px;
            background-color: #1a1a1a;
            color: #ffffff;
            font-size: 14px;
        }
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        .status {
            background-color: #2d2d2d;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            border-left: 4px solid #007acc;
        }
        .error { border-left-color: #ff4444; background-color: #4a1a1a; }
        .success { border-left-color: #44ff44; background-color: #1a4a1a; }
        .log {
            background-color: #2d2d2d;
            padding: 10px;
            border-radius: 3px;
            margin: 10px 0;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Consolas', 'Monaco', monospace;
            font-size: 12px;
        }
        .log-entry {
            margin: 2px 0;
            padding: 2px 5px;
        }
        .log-info { color: #87ceeb; }
        .log-error { color: #ff6b6b; }
        .log-warn { color: #ffd93d; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 简单LOD逻辑测试</h1>
        
        <div id="status" class="status">状态: 初始化中...</div>
        
        <div class="status">
            <h3>📋 测试信息</h3>
            <p>这个测试使用 <code>test_lod_logic.js</code> 来验证基本的WebAssembly功能，不涉及复杂的3D渲染。</p>
        </div>
        
        <div class="log">
            <h3>📝 执行日志</h3>
            <div id="log-output"></div>
        </div>
    </div>

    <script>
        // 简单的Module配置
        var Module = {
            print: function(text) {
                console.log('LOD Test:', text);
                addLog('INFO: ' + text, 'info');
            },
            
            printErr: function(text) {
                console.error('LOD Test Error:', text);
                addLog('ERROR: ' + text, 'error');
                updateStatus('错误: ' + text, true);
            },
            
            onRuntimeInitialized: function() {
                console.log('LOD测试模块初始化完成');
                addLog('LOD测试模块初始化完成', 'info');
                updateStatus('LOD测试模块就绪', false, true);
            },
            
            onAbort: function(what) {
                console.error('LOD测试模块中止:', what);
                addLog('模块中止: ' + what, 'error');
                updateStatus('模块中止: ' + what, true);
            },
            
            onExit: function(status) {
                addLog('程序退出，状态码: ' + status, status === 0 ? 'info' : 'error');
                updateStatus('程序退出，状态码: ' + status, status !== 0);
            }
        };
        
        function updateStatus(text, isError = false, isSuccess = false) {
            const statusEl = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            statusEl.innerHTML = `<strong>${timestamp}</strong> - ${text}`;
            statusEl.className = 'status';
            if (isError) statusEl.className += ' error';
            if (isSuccess) statusEl.className += ' success';
        }
        
        function addLog(text, level = 'info') {
            const outputEl = document.getElementById('log-output');
            const div = document.createElement('div');
            const timestamp = new Date().toLocaleTimeString();
            div.className = `log-entry log-${level}`;
            div.innerHTML = `<span style="color: #888">${timestamp}</span> ${text}`;
            outputEl.appendChild(div);
            outputEl.scrollTop = outputEl.scrollHeight;
        }
        
        // 页面加载完成后加载WebAssembly
        window.addEventListener('load', function() {
            addLog('页面加载完成，开始LOD测试', 'info');
            updateStatus('加载LOD测试模块...');
            
            // 确保Module在全局作用域
            window.Module = Module;
            
            // 加载WebAssembly脚本
            const script = document.createElement('script');
            script.src = 'test_lod_logic.js';
            script.onload = function() {
                addLog('LOD测试脚本加载成功', 'info');
                updateStatus('LOD测试脚本已加载');
            };
            script.onerror = function() {
                addLog('LOD测试脚本加载失败', 'error');
                updateStatus('LOD测试脚本加载失败', true);
            };
            document.head.appendChild(script);
        });
        
        // 全局错误处理
        window.addEventListener('error', function(event) {
            addLog(`全局错误: ${event.message} at ${event.filename}:${event.lineno}`, 'error');
        });
        
        window.addEventListener('unhandledrejection', function(event) {
            addLog(`未处理的Promise拒绝: ${event.reason}`, 'error');
        });
    </script>
</body>
</html>
