/**
 * 测试std::thread创建 - 最底层的线程测试
 * 专门诊断WebAssembly中std::thread创建失败的问题
 */

#include <iostream>
#include <thread>
#include <vector>
#include <chrono>
#include <atomic>
#include <exception>

#ifdef EMSCRIPTEN
#include <emscripten.h>
#include <emscripten/threading.h>
#endif

std::atomic<int> g_counter{0};
std::atomic<bool> g_running{true};

// 简单的线程函数
void simple_thread_function(int thread_id) {
    std::cout << "[Thread " << thread_id << "] 线程启动成功" << std::endl;
    
    for (int i = 0; i < 10 && g_running.load(); ++i) {
        g_counter.fetch_add(1);
        std::this_thread::sleep_for(std::chrono::milliseconds(100));
    }
    
    std::cout << "[Thread " << thread_id << "] 线程正常结束" << std::endl;
}

// 测试单个std::thread创建
bool test_single_thread() {
    std::cout << "[test] 测试1: 创建单个std::thread..." << std::endl;
    
    try {
        bool thread_executed = false;
        
        std::thread test_thread([&thread_executed]() {
            std::cout << "[test] 单线程测试函数执行中..." << std::endl;
            thread_executed = true;
        });
        
        std::cout << "[test] std::thread创建成功，等待完成..." << std::endl;
        test_thread.join();
        
        if (thread_executed) {
            std::cout << "[test] ✅ 单线程测试成功" << std::endl;
            return true;
        } else {
            std::cout << "[test] ❌ 单线程测试失败：线程未执行" << std::endl;
            return false;
        }
    } catch (const std::system_error& e) {
        std::cout << "[test] ❌ std::system_error: " << e.what() << std::endl;
        std::cout << "[test] 错误代码: " << e.code() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ std::exception: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[test] ❌ 未知异常" << std::endl;
        return false;
    }
}

// 测试多个std::thread创建
bool test_multiple_threads() {
    std::cout << "[test] 测试2: 创建多个std::thread..." << std::endl;
    
    try {
        const int num_threads = 2; // 只创建2个线程
        std::vector<std::thread> threads;
        
        g_counter.store(0);
        g_running.store(true);
        
        std::cout << "[test] 创建 " << num_threads << " 个线程..." << std::endl;
        
        for (int i = 0; i < num_threads; ++i) {
            std::cout << "[test] 创建线程 " << i << "..." << std::endl;
            threads.emplace_back(simple_thread_function, i);
            std::cout << "[test] 线程 " << i << " 创建成功" << std::endl;
        }
        
        std::cout << "[test] 所有线程创建完成，等待执行..." << std::endl;
        
        // 让线程运行一段时间
        std::this_thread::sleep_for(std::chrono::seconds(2));
        
        // 停止线程
        g_running.store(false);
        
        std::cout << "[test] 等待所有线程完成..." << std::endl;
        for (auto& thread : threads) {
            thread.join();
        }
        
        int final_count = g_counter.load();
        std::cout << "[test] ✅ 多线程测试成功，最终计数: " << final_count << std::endl;
        
        return final_count > 0;
    } catch (const std::system_error& e) {
        std::cout << "[test] ❌ std::system_error: " << e.what() << std::endl;
        std::cout << "[test] 错误代码: " << e.code() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ std::exception: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[test] ❌ 未知异常" << std::endl;
        return false;
    }
}

// 测试lambda线程创建（模拟osgEarth的方式）
bool test_lambda_thread() {
    std::cout << "[test] 测试3: 创建lambda线程（模拟osgEarth方式）..." << std::endl;
    
    try {
        std::atomic<bool> lambda_executed{false};
        
        // 模拟osgEarth jobpool::start_threads()中的代码
        std::thread lambda_thread([&lambda_executed]() {
            std::cout << "[test] Lambda线程执行中..." << std::endl;
            lambda_executed.store(true);
        });
        
        std::cout << "[test] Lambda线程创建成功，等待完成..." << std::endl;
        lambda_thread.join();
        
        if (lambda_executed.load()) {
            std::cout << "[test] ✅ Lambda线程测试成功" << std::endl;
            return true;
        } else {
            std::cout << "[test] ❌ Lambda线程测试失败：线程未执行" << std::endl;
            return false;
        }
    } catch (const std::system_error& e) {
        std::cout << "[test] ❌ std::system_error: " << e.what() << std::endl;
        std::cout << "[test] 错误代码: " << e.code() << std::endl;
        return false;
    } catch (const std::exception& e) {
        std::cout << "[test] ❌ std::exception: " << e.what() << std::endl;
        return false;
    } catch (...) {
        std::cout << "[test] ❌ 未知异常" << std::endl;
        return false;
    }
}

int main() {
    std::cout << "🧵 std::thread创建测试程序" << std::endl;
    std::cout << "===========================" << std::endl;
    
#ifdef EMSCRIPTEN
    std::cout << "[main] WebAssembly环境检测" << std::endl;
    
    // 检查多线程支持
    if (emscripten_has_threading_support()) {
        std::cout << "[main] ✅ Emscripten多线程支持已启用" << std::endl;
    } else {
        std::cout << "[main] ❌ Emscripten多线程支持未启用" << std::endl;
        return 1;
    }
    
    // 检查线程池
    int pool_size = emscripten_num_logical_cores();
    std::cout << "[main] 逻辑核心数: " << pool_size << std::endl;
#endif
    
    bool all_passed = true;
    
    // 测试1: 单线程
    if (!test_single_thread()) {
        all_passed = false;
        std::cout << "[main] ⚠️ 单线程测试失败，这是最基础的问题" << std::endl;
    }
    
    // 测试2: 多线程
    if (all_passed && !test_multiple_threads()) {
        all_passed = false;
        std::cout << "[main] ⚠️ 多线程测试失败" << std::endl;
    }
    
    // 测试3: Lambda线程
    if (all_passed && !test_lambda_thread()) {
        all_passed = false;
        std::cout << "[main] ⚠️ Lambda线程测试失败" << std::endl;
    }
    
    if (all_passed) {
        std::cout << "[main] 🎉 所有std::thread测试通过！" << std::endl;
        std::cout << "[main] std::thread功能正常，问题可能在osgEarth的其他部分" << std::endl;
    } else {
        std::cout << "[main] ❌ std::thread测试失败" << std::endl;
        std::cout << "[main] 这解释了为什么osgEarth线程池创建失败" << std::endl;
    }
    
    return all_passed ? 0 : 1;
}
