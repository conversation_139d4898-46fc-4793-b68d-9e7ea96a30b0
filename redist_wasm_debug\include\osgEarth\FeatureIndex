/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#ifndef OSGEARTHFEATURES_FEATURE_INDEX
#define OSGEARTHFEATURES_FEATURE_INDEX 1

#include <osgEarth/Common>
#include <osgEarth/Feature>
#include <osgEarth/ObjectIndex>

namespace osgEarth
{
    /**
     * Client interface for fetching features from an index given an object id.
     */
    class OSGEARTH_EXPORT FeatureIndex : public osg::Referenced
    {
    public:
        /** Get a feature given an object id. */
        virtual Feature* getFeature(ObjectID oid) const =0;

        /** Get the object ID corresponding to a feature ID. */
        virtual ObjectID getObjectID(FeatureID fid) const =0;

        /** Number of features in the index */
        virtual int size() const =0;
    };

    /**
     * Interface for building a Feature Index.
     */
    class OSGEARTH_EXPORT FeatureIndexBuilder : public ObjectIndexBuilder<Feature>
    {
        //empty
    };
} // namespace osgEarth

#endif // OSGEARTHFEATURES_FEATURE_INDEX
