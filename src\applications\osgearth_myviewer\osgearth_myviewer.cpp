/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */

#include <osgViewer/Viewer>
#include <osgViewer/ViewerEventHandlers>
#include <osgEarth/EarthManipulator>
#include <osgEarth/ExampleResources>
#include <osgEarth/MapNode>
#include <osgEarth/PhongLightingEffect>
#include <osgEarth/Map>
#include <osgEarth/TerrainEngineNode>
#include <osgEarth/XYZ>
#include <osgEarth/TileKey>
#include <osgEarth/ImageLayer>
#include <osgEarth/GeodeticGraticule>
#include <osgEarth/Sky>
#include <osgEarth/FeatureModelLayer>
#include <osgEarth/GeosFeatureSource>
#include <osgEarth/Style>
#include <osgEarth/StyleSheet>
#include <osgEarth/LineSymbol>
#include <osgEarth/AltitudeSymbol>
#include <osgEarth/RenderSymbol>
#include <osgEarth/Stroke>
#include <osgEarth/AnnotationLayer>
#include <osgEarth/LocalGeometryNode>
#include <osgEarth/Geometry>
#include <osgEarth/HTTPClient>
#include <osgEarth/Cache>
#include <osgEarth/CachePolicy>
#include <osgGA/TrackballManipulator>
#include <osgDB/ReadFile>
#include <osgDB/FileUtils>
#include <iostream>
#include <fstream>
#include <thread>
#include <chrono>

#ifdef EMSCRIPTEN
#include "WebAssemblyHTTPClient.h"
#include "WebAssemblyInputHandler.h"
#endif

#include <osgEarth/Metrics>
#include <osgEarth/FeatureModelLayer>
#include <osgEarth/JsonUtils>
#include <osgEarth/FeatureSource>
#include <osgEarth/StyleSheet>

// 添加天空效果相关头文件
#include <osgEarth/Sky>
#include <osgEarth/DateTime>
#include <osgEarth/Ephemeris>
#include <osgEarth/Shadowing>

#define LC "[myviewer] "

using namespace osgEarth;
using namespace osgEarth::Util;

// Function declarations
void addSimpleChinaBoundary(Map *map);

// Configure HTTP proxy and SSL settings
void configureNetworking()
{
#ifdef EMSCRIPTEN
    // WebAssembly平台网络配置
    std::cout << "[myviewer] 配置WebAssembly网络环境" << std::endl;

    // 获取WebAssembly HTTP客户端实例
    osgEarth::WebAssemblyHTTPClient &wasmClient = osgEarth::WebAssemblyHTTPClientFactory::getInstance();
    wasmClient.setConnectTimeout(15.0); // 增加超时时间
    wasmClient.setReadTimeout(30.0);
    wasmClient.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");

    std::cout << "[myviewer] WebAssembly HTTP客户端配置完成" << std::endl;
#else
    // 桌面平台网络配置
    std::cout << "[myviewer] 配置桌面版网络环境" << std::endl;

    // Set proxy using environment variables (more reliable)
    _putenv("OSG_CURL_PROXY=127.0.0.1");
    _putenv("OSG_CURL_PROXYPORT=10809");
    _putenv("HTTP_PROXY=http://127.0.0.1:10809");
    _putenv("HTTPS_PROXY=http://127.0.0.1:10809");

    _putenv("FONTCONFIG_PATH=C:\\dev\\vcpkg\\installed\\x64-windows\\etc\\fonts");
    _putenv("OE_LOG_LEVEL=WARN");
    _putenv("OSG_NOTIFY_LEVEL=WARN");
    _putenv("OSGEARTH_NOTIFY_LEVEL=WARN");

    // Enable HTTP debugging to see detailed error messages
    _putenv("OSGEARTH_HTTP_DEBUG=1");

    // Also try the osgEarth API method
    ProxySettings proxy("127.0.0.1", 10809);
    HTTPClient::setProxySettings(proxy);

    // Set timeout for HTTP requests
    HTTPClient::setTimeout(30);        // 30 seconds timeout
    HTTPClient::setConnectTimeout(10); // 10 seconds connect timeout

    // Set global HTTP headers to simulate Chrome browser
    HTTPClient::setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
#endif

    // Removed debug output for cleaner console

#ifdef DEBUG_MYVIEWER
    // Test a simple HTTP request to verify connectivity with detailed logging
    // std::cout << LC << "Testing network connectivity..." << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly平台网络连接测试
    std::cout << LC << "WebAssembly网络连接测试暂时跳过" << std::endl;
    // 在WebAssembly中，网络请求通常是异步的，这里暂时跳过同步测试
#else
    // 桌面平台网络连接测试
    // Create request with Chrome-like headers
    HTTPRequest request("https://mt1.google.com/vt/lyrs=s&x=0&y=0&z=1");
    request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
    request.addHeader("Accept", "image/webp,image/apng,image/*,*/*;q=0.8");
    request.addHeader("Accept-Language", "en-US,en;q=0.9");
    request.addHeader("Accept-Encoding", "gzip, deflate, br");
    request.addHeader("Connection", "keep-alive");
    request.addHeader("Referer", "https://maps.google.com/");

    // Network connectivity test disabled for cleaner console output
    /*
    std::cout << LC << "Sending test request to: " << request.getURL() << std::endl;
    std::cout << LC << "Request headers configured (Chrome simulation)" << std::endl;

    HTTPResponse response = HTTPClient::get(request);
    std::cout << LC << "Response code: " << response.getCode() << std::endl;
    std::cout << LC << "Response message: " << response.getMessage() << std::endl;

    if (response.isOK())
    {
        std::cout << LC << "Network test successful - proxy is working" << std::endl;
        std::cout << LC << "Content-Type: " << response.getMimeType() << std::endl;

        // Check response size
        std::cout << LC << "Response received successfully" << std::endl;
    }
    else
    {
        std::cout << LC << "Network test failed: " << response.getCode() << " - " << response.getMessage() << std::endl;

        // Try a simpler test with HTTP instead of HTTPS
        std::cout << LC << "Trying HTTP test..." << std::endl;
        HTTPRequest httpRequest("http://www.google.com");
        HTTPResponse httpResponse = HTTPClient::get(httpRequest);
        if (httpResponse.isOK())
        {
            std::cout << LC << "HTTP test successful" << std::endl;
        }
        else
        {
            std::cout << LC << "HTTP test also failed: " << httpResponse.getCode() << " - " << httpResponse.getMessage() << std::endl;
        }
    }
    */
#endif // EMSCRIPTEN
#endif // DEBUG_MYVIEWER
}

// Setup cache directory and global cache settings
void setupCache()
{
    // Removed debug output for cleaner console

    // Create cache directory if it doesn't exist
    std::string cacheDir = "cache";
    if (!osgDB::fileExists(cacheDir))
    {
        osgDB::makeDirectory(cacheDir);
        // Cache directory created silently
    }

    // Create subdirectories for different tile types
    std::string googleCacheDir = cacheDir + "/google_satellite";
    std::string elevationCacheDir = cacheDir + "/aws_terrarium";

    if (!osgDB::fileExists(googleCacheDir))
    {
        osgDB::makeDirectory(googleCacheDir);
    }

    if (!osgDB::fileExists(elevationCacheDir))
    {
        osgDB::makeDirectory(elevationCacheDir);
    }

    // Cache directories configured silently
}

/*
#ifdef DEBUG_MYVIEWER
// Monitor tile downloads and cache status - DISABLED FOR CLEANER OUTPUT
void monitorTileDownloads()
{
    std::cout << LC << "=== Tile Download Monitoring ===" << std::endl;

    // Check cache directories for files
    std::string googleCacheDir = "cache/google_satellite";
    std::string elevationCacheDir = "cache/aws_terrarium";

    // Count files in Google cache
    int googleFiles = 0;
    if (osgDB::fileExists(googleCacheDir))
    {
        osgDB::DirectoryContents googleContents = osgDB::getDirectoryContents(googleCacheDir);
        googleFiles = googleContents.size() - 2; // Exclude . and ..
        std::cout << LC << "Google satellite cache files: " << googleFiles << std::endl;

        // List first few files for debugging
        int count = 0;
        for (const auto &file : googleContents)
        {
            if (file != "." && file != ".." && count < 5)
            {
                std::cout << LC << "  - " << file << std::endl;
                count++;
            }
        }
    }

    // Count files in elevation cache
    int elevationFiles = 0;
    if (osgDB::fileExists(elevationCacheDir))
    {
        osgDB::DirectoryContents elevationContents = osgDB::getDirectoryContents(elevationCacheDir);
        elevationFiles = elevationContents.size() - 2; // Exclude . and ..
        std::cout << LC << "Elevation cache files: " << elevationFiles << std::endl;

        // List first few files for debugging
        int count = 0;
        for (const auto &file : elevationContents)
        {
            if (file != "." && file != ".." && count < 5)
            {
                std::cout << LC << "  - " << file << std::endl;
                count++;
            }
        }
    }

    std::cout << LC << "Total cached tiles: " << (googleFiles + elevationFiles) << std::endl;
    std::cout << LC << "=================================" << std::endl;
}
#endif
*/

// Apply downloaded tiles directly to earth surface - DISABLED
// void applyTilesToEarth(osgEarth::MapNode *mapNode);

/*
#ifdef DEBUG_MYVIEWER
// Force tile downloads by making direct HTTP requests - DISABLED FOR CLEANER OUTPUT
void forceTileDownloads()
{
    std::cout << LC << "=== Forcing Tile Downloads ===" << std::endl;

    // Download more tiles for better coverage
    std::vector<std::string> testURLs;

    // Google satellite tiles for zoom level 2 (better resolution)
    for (int x = 0; x < 4; ++x)
    {
        for (int y = 0; y < 4; ++y)
        {
            std::ostringstream url;
            url << "https://mt" << (x % 4) << ".google.com/vt/lyrs=s&x=" << x << "&y=" << y << "&z=2";
            testURLs.push_back(url.str());
        }
    }

    // Add some elevation tiles
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/0/0.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/1/0.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/0/1.png");
    testURLs.push_back("https://s3.amazonaws.com/elevation-tiles-prod/terrarium/2/1/1.png");

    std::cout << LC << "Will download " << testURLs.size() << " tiles..." << std::endl;

    for (const auto &url : testURLs)
    {
        std::cout << LC << "Testing download: " << url << std::endl;

        HTTPRequest request(url);
        request.addHeader("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36");
        request.addHeader("Referer", "https://maps.google.com/");

        HTTPResponse response = HTTPClient::get(request);

        if (response.isOK())
        {
            std::cout << LC << "  SUCCESS - Code: " << response.getCode() << ", Type: " << response.getMimeType() << std::endl;

            // Save to cache manually for testing
            std::string filename = url.substr(url.find_last_of('/') + 1);
            if (filename.empty())
                filename = "tile.dat";

            std::string cachePath;
            if (url.find("google.com") != std::string::npos)
            {
                cachePath = "cache/google_satellite/" + filename;
            }
            else
            {
                cachePath = "cache/aws_terrarium/" + filename;
            }

            // Try to save the response data
            std::string data = response.getPartAsString(0);
            if (!data.empty())
            {
                std::ofstream file(cachePath, std::ios::binary);
                if (file.is_open())
                {
                    file.write(data.c_str(), data.length());
                    file.close();
                    std::cout << LC << "  SAVED: " << cachePath << " (" << data.length() << " bytes)" << std::endl;
                }
                else
                {
                    std::cout << LC << "  FAILED to save: " << cachePath << std::endl;
                }
            }
            else
            {
                std::cout << LC << "  No data to save" << std::endl;
            }
        }
        else
        {
            std::cout << LC << "  FAILED - Code: " << response.getCode() << ", Message: " << response.getMessage() << std::endl;
        }
    }

    std::cout << LC << "===============================" << std::endl;
}
#endif
*/

/*
#ifdef DEBUG_MYVIEWER
// Test XYZ layers by manually requesting tiles - DISABLED FOR CLEANER OUTPUT
void testXYZLayers(osgEarth::MapNode *mapNode)
{
    if (!mapNode)
        return;

    std::cout << LC << "=== Testing XYZ Layer Functionality ===" << std::endl;

    auto map = mapNode->getMap();
    LayerVector layers;
    map->getLayers(layers);

    for (auto layer : layers)
    {
        auto xyzLayer = dynamic_cast<XYZImageLayer *>(layer.get());
        if (xyzLayer)
        {
            std::cout << LC << "Found XYZ Image Layer: " << xyzLayer->getName() << std::endl;

            // Create a test tile key
            const Profile *profile = xyzLayer->getProfile();
            if (profile)
            {
                TileKey testKey(1, 0, 0, profile); // Level 1, tile (0,0)
                std::cout << LC << "Testing tile key: " << testKey.str() << std::endl;

                // Try to create multiple images to force XYZ driver calls
                for (int level = 0; level <= 3; ++level)
                {
                    for (int x = 0; x < (1 << level); ++x)
                    {
                        for (int y = 0; y < (1 << level); ++y)
                        {
                            TileKey key(level, x, y, profile);
                            std::cout << LC << "Forcing tile creation: " << key.str() << std::endl;

                            // Force call to createImageImplementation which calls XYZ::Driver
                            std::cout << LC << "About to call createImageImplementation for " << key.str() << std::endl;
                            GeoImage geoImage = xyzLayer->createImageImplementation(key, nullptr);
                            std::cout << LC << "Finished calling createImageImplementation for " << key.str() << std::endl;
                            if (geoImage.valid())
                            {
                                std::cout << LC << "SUCCESS: Created image " << geoImage.getImage()->s() << "x" << geoImage.getImage()->t() << std::endl;
                            }
                            else
                            {
                                std::cout << LC << "FAILED: Could not create image for " << key.str() << std::endl;
                            }

                            // Only test a few tiles to avoid spam
                            if (level >= 2)
                                break;
                        }
                        if (level >= 2)
                            break;
                    }
                }
            }
        }
    }

    std::cout << LC << "=== XYZ Layer Test Complete ===" << std::endl;
}
#endif
*/

/*
#ifdef DEBUG_MYVIEWER
// Apply downloaded tiles directly to earth surface - DISABLED FOR CLEANER OUTPUT
void applyTilesToEarth(osgEarth::MapNode *mapNode)
{
    if (!mapNode)
    {
        std::cout << LC << "MapNode is null, cannot apply tiles" << std::endl;
        return;
    }

    std::cout << LC << "=== Applying Tiles to Earth Surface ===" << std::endl;

    // Try to force tile loading by accessing the map layers
    auto map = mapNode->getMap();
    if (map)
    {
        LayerVector layers;
        map->getLayers(layers);

        for (auto layer : layers)
        {
            std::string layerName = layer->getName();
            if (layerName == "Google Satellite" || layerName == "AWS Terrarium")
            {
                std::cout << LC << "Forcing update for layer: " << layerName << std::endl;

                // Multiple refresh attempts
                layer->setEnabled(false);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                layer->setEnabled(true);

                // Force layer to refresh
                layer->dirty();

                // Force cache refresh
                layer->setCachePolicy(CachePolicy::USAGE_NO_CACHE);
                std::this_thread::sleep_for(std::chrono::milliseconds(100));
                layer->setCachePolicy(CachePolicy::USAGE_READ_WRITE);

                std::cout << LC << "Layer " << layerName << " refresh completed" << std::endl;
            }
        }
    }

    std::cout << LC << "Tile application completed" << std::endl;
}
#endif
*/

int usage(const char *name)
{
    std::cout
        << "Custom osgEarth viewer with Google Maps and China boundaries" << std::endl
        << "Usage: " << name << " [config.earth]" << std::endl
        << "If no config file is provided, uses built-in configuration" << std::endl
        << "Features:" << std::endl
        << "  - Google Maps satellite imagery" << std::endl
        << "  - AWS Terrarium elevation data" << std::endl
        << "  - Atmospheric effects" << std::endl
        << "  - Geodetic graticule" << std::endl
        << "  - China boundaries in red" << std::endl;

    return 0;
}

osg::ref_ptr<Map> createMap()
{
    // Create the map
    auto map = new Map();

    std::cout << "[myviewer] 创建基础数字地球地图" << std::endl;

#ifdef EMSCRIPTEN
    // WebAssembly版本 - 使用开放街道地图，避免网络问题
    auto osmImagery = new XYZImageLayer();
    osmImagery->setName("OpenStreetMap");

    // 使用开放街道地图的标准瓦片服务
    std::string osmURL = "https://a.tile.openstreetmap.org/{z}/{x}/{y}.png";
    URI osmURI(osmURL);
    osmImagery->setURL(osmURI);

    // 设置OSM的格式和配置
    osmImagery->setFormat("png");

    // 使用spherical-mercator配置文件
    const Profile *profile = Profile::create("spherical-mercator");
    osmImagery->setProfile(profile);
    osmImagery->setMinLevel(0);
    osmImagery->setMaxLevel(10); // 降低最大级别以减少网络负载
    osmImagery->setEnabled(true);
    osmImagery->setVisible(true);

    map->addLayer(osmImagery);
    std::cout << "[myviewer] 添加OpenStreetMap图像层" << std::endl;
#else
    // 桌面版本 - 使用Google卫星图像
    auto googleImagery = new XYZImageLayer();
    googleImagery->setName("Google Satellite");

    // Set URL with logging - try different Google Maps servers
    std::string googleURL = "https://mt1.google.com/vt/lyrs=s&x={x}&y={y}&z={z}";
    URI googleURI(googleURL);
    googleImagery->setURL(googleURI);

    // Set additional options for better compatibility
    googleImagery->setFormat("jpg");

    // Use global-mercator profile for Google Maps
    const Profile *profile = Profile::create("global-mercator");
    googleImagery->setProfile(profile);
    googleImagery->setMinLevel(0);
    googleImagery->setMaxLevel(18); // Set reasonable max level to avoid warning
    googleImagery->setEnabled(true);
    googleImagery->setVisible(true);

    map->addLayer(googleImagery);
    std::cout << "[myviewer] 添加Google卫星图像层" << std::endl;
#endif

    // 添加经纬度网格 - 对所有版本都使用
    auto graticule = new GeodeticGraticule();
    graticule->setName("Geodetic Graticule");
    graticule->setColor(Color(0.8f, 0.8f, 0.0f, 0.8f)); // 半透明黄色
    graticule->setLineWidth(1.0f);                      // 细线
    graticule->setGridLinesVisible(true);
    graticule->setGridLabelsVisible(true);
    graticule->setEdgeLabelsVisible(true);
    graticule->setVisible(true);
    graticule->setEnabled(true);
    map->addLayer(graticule);
    std::cout << "[myviewer] 添加经纬度网格" << std::endl;

    // 暂时不添加高程数据，减少WebAssembly版本的复杂性
    // 高程数据会增加网络负载和处理复杂度

    std::cout << "[myviewer] 地图创建完成" << std::endl;
    return map;
}

void addChinaBoundaries(Map *map)
{
    // For now, use the simplified boundary implementation
    // TODO: Implement proper GeoJSON loading when API is clarified
    addSimpleChinaBoundary(map);
}

void addSimpleChinaBoundary(Map *map)
{

    // Create a simple annotation layer for China boundaries
    auto annotationLayer = new AnnotationLayer();
    annotationLayer->setName("China Boundaries (Simplified)");

    // Create a simple rectangular boundary for China (approximate)
    auto lineString = new LineString();
    lineString->push_back(osg::Vec3d(73.5, 53.5, 0));  // Northwest corner
    lineString->push_back(osg::Vec3d(134.8, 53.5, 0)); // Northeast corner
    lineString->push_back(osg::Vec3d(134.8, 18.2, 0)); // Southeast corner
    lineString->push_back(osg::Vec3d(73.5, 18.2, 0));  // Southwest corner
    lineString->push_back(osg::Vec3d(73.5, 53.5, 0));  // Close the rectangle

    // Create style for red 3-pixel boundaries
    Style lineStyle;
    lineStyle.getOrCreate<LineSymbol>()->stroke().mutable_value().color() = Color::Red;
    lineStyle.getOrCreate<LineSymbol>()->stroke().mutable_value().width() = Distance(3.0f, Units::PIXELS);
    lineStyle.getOrCreate<AltitudeSymbol>()->clamping() = AltitudeSymbol::CLAMP_TO_TERRAIN;
    lineStyle.getOrCreate<AltitudeSymbol>()->technique() = AltitudeSymbol::TECHNIQUE_DRAPE;

    // Create LocalGeometryNode with the line geometry
    auto geometryNode = new LocalGeometryNode(lineString, lineStyle);
    geometryNode->setPosition(GeoPoint(SpatialReference::get("wgs84"), 0, 0, 0));

    // Add to annotation layer
    annotationLayer->addChild(geometryNode);
    annotationLayer->setEnabled(true);
    annotationLayer->setVisible(true);

    // Add to map
    map->addLayer(annotationLayer);
}

void addSkyEffects(MapNode *mapNode)
{
    // Add atmospheric sky effects using the factory method
    auto sky = SkyNode::create("simple");
    if (sky)
    {
        // Set to daytime for proper sun lighting and atmosphere
        sky->setDateTime(DateTime(2025, 1, 1, 12.0)); // Noon for proper lighting

        // Configure for maximum texture clarity - no shadows, uniform lighting
        sky->getSunLight()->setAmbient(osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f));  // Full ambient light
        sky->getSunLight()->setDiffuse(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f));  // No diffuse = no shadows
        sky->getSunLight()->setSpecular(osg::Vec4(0.0f, 0.0f, 0.0f, 1.0f)); // No specular = no highlights

        // Enable atmosphere for blue atmospheric glow
        sky->setAtmosphereVisible(true);
        sky->setStarsVisible(true);
        sky->setSunVisible(false);  // Hide sun disc for space view
        sky->setMoonVisible(false); // Hide moon for cleaner view

        mapNode->addChild(sky);
    }
}

// to set up the manipulator when the viewer is realized
struct SetRealizeCallback : public osg::Operation
{
    SetRealizeCallback() {}

    virtual void operator()(osg::Object *object)
    {
        osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(object);
        if (viewer)
        {
            // 获取地图节点和操控器
            MapNode *mapNode = MapNode::findMapNode(viewer->getSceneData());
            EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer->getCameraManipulator());

            if (mapNode && manip)
            {
                // 设置一些操控器的默认参数
                manip->getSettings()->setMinMaxPitch(osg::DegreesToRadians(-90.0), osg::DegreesToRadians(-10.0));
            }
        }
    }
};

// 窗口大小改变事件处理器，确保地球椭球比例正确
class ResizeHandler : public osgGA::GUIEventHandler
{
public:
    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::RESIZE)
        {
            osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(&aa);
            if (viewer)
            {
                double fovy = 45.0;
                double aspectRatio = double(ea.getWindowWidth()) / double(ea.getWindowHeight());
                double zNear = 1.0;
                double zFar = 1e12;

                viewer->getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);
                viewer->getCamera()->setViewport(0, 0, ea.getWindowWidth(), ea.getWindowHeight());
            }
        }
        return false;
    }
};

// LOD监控回调
class LODMonitorCallback : public osg::NodeCallback
{
public:
    LODMonitorCallback(MapNode *mapNode) : _mapNode(mapNode), _frameCount(0) {}

    virtual void operator()(osg::Node *node, osg::NodeVisitor *nv)
    {
        _frameCount++;
        if (_frameCount % 60 == 0) // 每60帧输出一次
        {
            if (_mapNode.valid())
            {
                osgViewer::Viewer *viewer = dynamic_cast<osgViewer::Viewer *>(nv->getUserData());
                if (viewer)
                {
                    // LOD monitoring disabled for cleaner output
                    /*
                    EarthManipulator *manip = dynamic_cast<EarthManipulator *>(viewer->getCameraManipulator());
                    if (manip)
                    {
                        double distance = manip->getDistance();
                        std::cout << "[LOD Monitor] Frame: " << _frameCount << ", Distance: " << distance << " meters" << std::endl;
                    }
                    */
                }
            }
        }
        traverse(node, nv);
    }

protected:
    osg::observer_ptr<MapNode> _mapNode;
    unsigned int _frameCount;
};

// 时间控制和键盘交互处理器
class TimeControlHandler : public osgGA::GUIEventHandler
{
public:
    TimeControlHandler(SkyNode *skyNode, ShadowCaster *shadowCaster = nullptr)
        : _skyNode(skyNode), _shadowCaster(shadowCaster), _hour(12.0f), _animateTime(false), _lastTime(0.0)
    {
        _startTime = osg::Timer::instance()->time_s(); // 初始化开始时间
    }

    virtual bool handle(const osgGA::GUIEventAdapter &ea, osgGA::GUIActionAdapter &aa)
    {
        if (ea.getEventType() == osgGA::GUIEventAdapter::KEYDOWN)
        {
            switch (ea.getKey())
            {
            case 't':
            case 'T':
                // 切换时间动画
                _animateTime = !_animateTime;
                // std::cout << "Time animation: " << (_animateTime ? "ON" : "OFF") << std::endl;
                aa.requestRedraw();
                return true;

            case '+':
            case '=':
                // 增加时间
                _hour += 0.5f;
                if (_hour >= 24.0f)
                    _hour = 0.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '-':
            case '_':
                // 减少时间
                _hour -= 0.5f;
                if (_hour < 0.0f)
                    _hour = 23.5f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '1':
                // 日出 (6:00)
                _hour = 6.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '2':
                // 正午 (12:00)
                _hour = 12.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '3':
                // 日落 (18:00)
                _hour = 18.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case '4':
                // 午夜 (0:00)
                _hour = 0.0f;
                updateTime();
                aa.requestRedraw();
                return true;

            case 's':
            case 'S':
                // 切换阴影
                if (_shadowCaster != nullptr)
                {
                    bool enabled = _shadowCaster->getEnabled();
                    _shadowCaster->setEnabled(!enabled);
                    // std::cout << "Shadows: " << (!enabled ? "ON" : "OFF") << std::endl;
                    aa.requestRedraw();
                }
                return true;

            default:
                break;
            }
        }
        else if (ea.getEventType() == osgGA::GUIEventAdapter::FRAME && _animateTime)
        {
            // 自动时间动画
            double currentTime = osg::Timer::instance()->time_s();
            double elapsed = currentTime - _startTime;

            // 每24秒循环一天（1秒 = 1小时）
            _hour = fmod(elapsed, 24.0);
            updateTime();
            aa.requestRedraw();
        }

        return false;
    }

    virtual void getUsage(osg::ApplicationUsage &usage) const
    {
        usage.addKeyboardMouseBinding("t", "Toggle time animation");
        usage.addKeyboardMouseBinding("+/-", "Increase/Decrease time");
        usage.addKeyboardMouseBinding("1", "Set time to sunrise (6:00)");
        usage.addKeyboardMouseBinding("2", "Set time to noon (12:00)");
        usage.addKeyboardMouseBinding("3", "Set time to sunset (18:00)");
        usage.addKeyboardMouseBinding("4", "Set time to midnight (0:00)");
        usage.addKeyboardMouseBinding("s", "Toggle shadows on/off");
    }

protected:
    void updateTime()
    {
        if (_skyNode.valid())
        {
            DateTime currentDate = _skyNode->getDateTime();
            DateTime newTime(currentDate.year(), currentDate.month(), currentDate.day(), _hour);
            _skyNode->setDateTime(newTime);

            // std::cout << "Time set to: " << std::fixed << std::setprecision(1) << _hour << ":00" << std::endl;
        }
    }

    osg::observer_ptr<SkyNode> _skyNode;
    osg::observer_ptr<ShadowCaster> _shadowCaster;
    float _hour;
    bool _animateTime;
    double _startTime;
    double _lastTime;
};

int main(int argc, char **argv)
{
    // Configure networking and cache before creating map
    configureNetworking();

    // Set notify level based on environment variables, defaulting to WARN
    const char *osg_notify_level = getenv("OSG_NOTIFY_LEVEL");

    osg::NotifySeverity level = osg::WARN; // Default to WARN

    if (osg_notify_level)
    {
        std::string levelStr = osg_notify_level;
        if (levelStr == "DEBUG")
            level = osg::DEBUG_INFO;
        else if (levelStr == "INFO")
            level = osg::INFO;
        else if (levelStr == "NOTICE")
            level = osg::NOTICE;
        else if (levelStr == "WARN")
            level = osg::WARN;
        else if (levelStr == "FATAL")
            level = osg::FATAL;
    }

    osg::setNotifyLevel(level);

    osg::ArgumentParser arguments(&argc, argv);
    if (arguments.read("--help"))
        return usage(argv[0]);

    // start up osgEarth
    osgEarth::initialize(arguments);

    // create a simple view but DON'T pass the arguments, since that can
    // trigger unwanted auto-configuration (like going fullscreen).
    osgViewer::Viewer viewer;

    // Apply global defaults to the camera's stateset to ensure lines and text render properly.
    GLUtils::setGlobalDefaults(viewer.getCamera()->getOrCreateStateSet());

#ifdef EMSCRIPTEN
    // WebAssembly平台图形配置
    std::cout << "[myviewer] 配置WebAssembly渲染环境" << std::endl;

    // WebAssembly使用Canvas，不需要创建GraphicsContext
    // 设置默认视口大小
    int canvasWidth = 1280;
    int canvasHeight = 720;
    viewer.getCamera()->setViewport(new osg::Viewport(0, 0, canvasWidth, canvasHeight));

    // 设置投影矩阵
    double fovy = 45.0;
    double aspectRatio = double(canvasWidth) / double(canvasHeight);
    double zNear = 1.0;
    double zFar = 1e12;
    viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);

    // 设置计算近远平面的回调
    viewer.getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);

    // 创建WebAssembly输入处理器
    std::shared_ptr<osgEarth::WebAssemblyInputHandler> inputHandler =
        osgEarth::WebAssemblyInputHandlerFactory::create(&viewer);

    // 设置全局实例
    osgEarth::WebAssemblyInputHandlerFactory::setInstance(inputHandler);

    // 初始化输入处理器
    if (!inputHandler->initialize("canvas"))
    {
        std::cout << "[myviewer] 警告：WebAssembly输入处理器初始化失败" << std::endl;
    }

    // 添加到viewer的事件处理器
    viewer.addEventHandler(inputHandler.get());

    std::cout << "[myviewer] WebAssembly渲染环境配置完成" << std::endl;
#else
    // 桌面平台图形配置
    std::cout << "[myviewer] 配置桌面版渲染环境" << std::endl;

    // Create a graphic context and window
    osg::ref_ptr<osg::GraphicsContext::Traits> traits = new osg::GraphicsContext::Traits;
    traits->x = 100;
    traits->y = 100;
    traits->width = 1980;
    traits->height = 1024;
    traits->windowDecoration = true;
    traits->doubleBuffer = true;
    traits->sharedContext = 0;

    osg::ref_ptr<osg::GraphicsContext> gc = osg::GraphicsContext::createGraphicsContext(traits.get());
    if (gc.valid())
    {
        viewer.getCamera()->setGraphicsContext(gc.get());
        viewer.getCamera()->setViewport(new osg::Viewport(0, 0, traits->width, traits->height));

        // 设置投影矩阵，确保地球椭球比例正确，不受窗口宽高比影响
        double fovy = 45.0; // 视场角
        double aspectRatio = double(traits->width) / double(traits->height);
        double zNear = 1.0;
        double zFar = 1e12; // 足够大的远平面以显示整个地球

        // 使用透视投影，但确保椭球体不会因窗口比例而变形
        viewer.getCamera()->setProjectionMatrixAsPerspective(fovy, aspectRatio, zNear, zFar);

        // 重要：设置计算近远平面的回调，确保地球几何体正确显示
        viewer.getCamera()->setComputeNearFarMode(osg::CullSettings::COMPUTE_NEAR_FAR_USING_BOUNDING_VOLUMES);
    }
    else
    {
        osg::notify(osg::FATAL) << "Unable to create graphics context." << std::endl;
        return 1;
    }

    std::cout << "[myviewer] 桌面版渲染环境配置完成" << std::endl;
#endif

    // install our default manipulator (do this before calling load)
    osg::ref_ptr<EarthManipulator> manip = new EarthManipulator();
    EarthManipulator::Settings *settings = manip->getSettings();

    // Set up Google Earth-like controls
    EarthManipulator::ActionOptions panOptions;
    panOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    panOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    panOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_PAN, EarthManipulator::MOUSE_LEFT_BUTTON, 0, panOptions);

    EarthManipulator::ActionOptions rotateOptions;
    rotateOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_X, 5.0);
    rotateOptions.add(EarthManipulator::OPTION_SCALE_Y, 5.0);
    settings->bindMouse(EarthManipulator::ACTION_ROTATE, EarthManipulator::MOUSE_MIDDLE_BUTTON, 0, rotateOptions);

    EarthManipulator::ActionOptions zoomOptions;
    zoomOptions.add(EarthManipulator::OPTION_CONTINUOUS, true);
    zoomOptions.add(EarthManipulator::OPTION_SCALE_X, 2.0); // 增加缩放敏感度
    zoomOptions.add(EarthManipulator::OPTION_SCALE_Y, 2.0);
    settings->bindMouse(EarthManipulator::ACTION_ZOOM, EarthManipulator::MOUSE_RIGHT_BUTTON, 0, zoomOptions);

    // Scroll wheel forward: Zoom in - 增加敏感度
    EarthManipulator::ActionOptions scrollInOptions;
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5); // 增加滚轮敏感度
    scrollInOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_IN, osgGA::GUIEventAdapter::SCROLL_UP, 0, scrollInOptions);

    // Scroll wheel backward: Zoom out - 增加敏感度
    EarthManipulator::ActionOptions scrollOutOptions;
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_X, 1.5);
    scrollOutOptions.add(EarthManipulator::OPTION_SCALE_Y, 1.5);
    settings->bindScroll(EarthManipulator::ACTION_ZOOM_OUT, osgGA::GUIEventAdapter::SCROLL_DOWN, 0, scrollOutOptions);

    // 配置LOD敏感度
    settings->setMinMaxDistance(1000.0, 50000000.0); // 设置缩放范围
    settings->setZoomToMouse(true);                  // 启用鼠标位置缩放

    viewer.setCameraManipulator(manip);

    // disable the small-feature culling; necessary for some feature rendering
    viewer.getCamera()->setSmallFeatureCullingPixelSize(-1.0f);

#ifndef EMSCRIPTEN
    // 桌面平台：添加窗口大小改变事件处理器，确保地球椭球比例正确
    viewer.addEventHandler(new ResizeHandler());
#endif

    // install the handler:
    viewer.setRealizeOperation(new SetRealizeCallback);

    // *** 修改：直接在代码中创建地图和节点，避免文件依赖 ***
    std::cout << "[myviewer] WebAssembly: 尝试创建默认地图" << std::endl;

    // 创建地图
    osg::ref_ptr<Map> map = createMap();
    if (!map.valid())
    {
        std::cerr << "[myviewer] 错误：无法创建地图" << std::endl;
        return 1;
    }

    // 创建地图节点
    osg::ref_ptr<MapNode> mapNode = new MapNode(map.get());
    if (!mapNode.valid())
    {
        std::cerr << "[myviewer] 错误：无法创建地图节点" << std::endl;
        return 1;
    }

    std::cout << "[myviewer] 成功创建地图和节点" << std::endl;

    // 设置场景数据
    viewer.setSceneData(mapNode.get());

    // 添加天空效果
    std::cout << "[myviewer] 添加天空效果" << std::endl;

    // 简化天空节点配置，修正白圈问题
    osg::ref_ptr<SkyNode> skyNode = SkyNode::create("simple");
    if (skyNode.valid())
    {
        // 设置基本的天空参数
        skyNode->setEphemeris(new Ephemeris);
        skyNode->setDateTime(DateTime(2024, 6, 15, 12.0));

        // 配置天空可见性 - 关键修正：隐藏太阳和月亮避免白圈
        skyNode->setSunVisible(false);       // 隐藏太阳圆盘避免白圈
        skyNode->setMoonVisible(false);      // 隐藏月亮
        skyNode->setStarsVisible(true);      // 显示星空
        skyNode->setAtmosphereVisible(true); // 显示大气层 - 这是蓝色效果的关键

        // 调整太阳光参数 - 优化纹理清晰度
        osg::Light *sunLight = skyNode->getSunLight();
        if (sunLight)
        {
            // 高环境光确保纹理清晰，低漫反射减少阴影
            sunLight->setAmbient(osg::Vec4(0.8f, 0.8f, 0.8f, 1.0f));  // 高环境光
            sunLight->setDiffuse(osg::Vec4(0.3f, 0.3f, 0.3f, 1.0f));  // 低漫反射
            sunLight->setSpecular(osg::Vec4(0.1f, 0.1f, 0.1f, 1.0f)); // 最小镜面反射
        }

        // 添加天空节点到地图节点
        mapNode->addChild(skyNode);

        // 将天空的光源附加到视口
        skyNode->attach(&viewer, 0);

        // 创建时间控制器（不使用阴影）
        TimeControlHandler *timeControlHandler = new TimeControlHandler(skyNode.get(), nullptr);
        viewer.addEventHandler(timeControlHandler);

        std::cout << "[myviewer] 天空效果配置完成" << std::endl;
    }
    else
    {
        std::cout << "[myviewer] 警告：无法创建天空节点" << std::endl;
    }

    // 设置默认视点位置
    std::cout << "[myviewer] 设置默认视点" << std::endl;

    double longitude = 104.0;     // 东经104度
    double latitude = 35.5;       // 北纬35.5度
    double altitude = 15000000.0; // 高度1500万米，可以看到整个中国

    // 创建视点位置
    Viewpoint vp;
    vp.focalPoint() = GeoPoint(
        mapNode->getMapSRS(),
        longitude, latitude, 0.0, // 焦点在地面
        ALTMODE_ABSOLUTE);
    vp.range() = Distance(altitude, Units::METERS); // 观察距离
    vp.heading() = Angle(0.0, Units::DEGREES);      // 朝向北方
    vp.pitch() = Angle(-90.0, Units::DEGREES);      // 俯视角度（垂直向下看）

    // 应用视点设置
    EarthManipulator *earthManip = dynamic_cast<EarthManipulator *>(viewer.getCameraManipulator());
    if (earthManip)
    {
        earthManip->setViewpoint(vp);
        std::cout << "[myviewer] 视点设置完成" << std::endl;
    }
    else
    {
        std::cout << "[myviewer] 警告：无法设置视点" << std::endl;
    }

    std::cout << "[myviewer] osgEarth数字地球初始化完成。按 'T' 键控制时间动画。" << std::endl;

    // 运行viewer
    return viewer.run();
}
