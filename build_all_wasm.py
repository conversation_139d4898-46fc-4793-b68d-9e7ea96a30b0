#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的WebAssembly编译流程
按顺序编译OSG、osg<PERSON><PERSON>h、weejobs，然后编译数字地球应用
"""

import os
import subprocess
import sys
import time

def run_script(script_name, description):
    """运行Python脚本"""
    print(f"\n{'='*60}")
    print(f"🚀 {description}")
    print(f"脚本: {script_name}")
    print(f"{'='*60}")
    
    start_time = time.time()
    
    try:
        result = subprocess.run([sys.executable, script_name], 
                              capture_output=True, text=True)
        
        if result.stdout:
            print("输出:")
            print(result.stdout)
        
        if result.stderr:
            print("错误:")
            print(result.stderr)
        
        elapsed = time.time() - start_time
        success = result.returncode == 0
        
        print(f"\n{'✅' if success else '❌'} {description} {'成功' if success else '失败'}")
        print(f"耗时: {elapsed:.1f}秒")
        
        if not success:
            print(f"返回码: {result.returncode}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 执行脚本时出错: {e}")
        return False

def check_prerequisites():
    """检查编译前提条件"""
    print("🔍 检查编译前提条件...")
    
    # 检查Emscripten
    try:
        result = subprocess.run("C:\\dev\\emsdk\\emsdk_env.bat && emcc --version", 
                              shell=True, capture_output=True, text=True)
        if result.returncode != 0:
            print("❌ Emscripten未正确安装")
            return False
        print("✅ Emscripten已安装")
    except:
        print("❌ 无法检查Emscripten")
        return False
    
    # 检查源码目录
    base_dir = "F:\\cmo-dev\\my_osgearth_web"
    
    # OSG源码
    osg_source = f"{base_dir}\\OpenSceneGraph"
    if not os.path.exists(osg_source):
        print(f"❌ OSG源码目录不存在: {osg_source}")
        print("请从 https://github.com/openscenegraph/OpenSceneGraph 下载OSG源码")
        return False
    print("✅ OSG源码目录存在")
    
    # osgEarth源码
    osgearth_source = f"{base_dir}\\osgearth_origin\\osgearth"
    if not os.path.exists(osgearth_source):
        print(f"❌ osgEarth源码目录不存在: {osgearth_source}")
        print("请从 https://github.com/gwaldron/osgearth 下载osgEarth源码")
        return False
    print("✅ osgEarth源码目录存在")
    
    return True

def main():
    print("🌍 完整的osgEarth WebAssembly编译流程")
    print("===================================")
    print("按顺序编译所有依赖库和应用程序")
    
    start_time = time.time()
    
    # 检查前提条件
    if not check_prerequisites():
        print("\n❌ 前提条件检查失败，请解决上述问题后重试")
        return False
    
    print("\n✅ 前提条件检查通过，开始编译流程...")
    
    # 编译步骤
    steps = [
        ("build_weejobs_wasm.py", "编译weejobs线程池库"),
        ("build_osg_wasm.py", "编译OSG库"),
        ("build_osgearth_wasm.py", "编译osgEarth库"),
    ]
    
    # 执行编译步骤
    for script, description in steps:
        if not run_script(script, description):
            print(f"\n❌ {description}失败，停止编译流程")
            return False
        
        print(f"\n✅ {description}完成")
        time.sleep(1)  # 短暂暂停
    
    # 编译数字地球应用
    print(f"\n{'='*60}")
    print("🌍 编译基础数字地球应用")
    print(f"{'='*60}")
    
    app_script = "redist_wasm1\\compile_earth_basic.py"
    if os.path.exists(app_script):
        if not run_script(app_script, "编译基础数字地球应用"):
            print("\n❌ 基础数字地球应用编译失败")
            return False
        print("\n✅ 基础数字地球应用编译完成")
    else:
        print(f"⚠️ 应用编译脚本不存在: {app_script}")
    
    # 总结
    total_time = time.time() - start_time
    print(f"\n{'='*60}")
    print("🎉 完整编译流程完成！")
    print(f"总耗时: {total_time/60:.1f}分钟")
    print(f"{'='*60}")
    
    # 显示结果
    install_dir = "F:\\cmo-dev\\my_osgearth_web\\osgearth_third_party\\wasm_dep"
    print(f"\n📁 安装目录: {install_dir}")
    print("库文件结构:")
    print("  include/")
    print("    ├── osg/")
    print("    ├── osgEarth/")
    print("    └── weejobs/")
    print("  lib/")
    print("    ├── libosg*.a")
    print("    └── libosgEarth*.a")
    
    print("\n🌐 测试地址:")
    print("  • 基础数字地球: http://localhost:8080/osgearth_basic_earth.html")
    
    print("\n📋 下一步:")
    print("  1. 启动HTTP服务器: python redist_wasm1/server.py 8080")
    print("  2. 在浏览器中访问测试页面")
    print("  3. 验证数字地球功能是否正常")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
