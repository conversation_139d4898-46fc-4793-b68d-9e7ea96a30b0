/* osgEarth
 * Copyright 2025 Pelican Mapping
 * MIT License
 */
#ifndef OSGEARTH_DRIVER_CACHE_FILESYSTEM
#define OSGEARTH_DRIVER_CACHE_FILESYSTEM 1

#include <osgEarth/Common>
#include <osgEarth/Cache>

namespace osgEarth { namespace Drivers
{
    using namespace osgEarth;
    
    /**
     * Serializable options for the FileSystemCache.
     */
    class FileSystemCacheOptions : public CacheOptions
    {
    public:
        FileSystemCacheOptions( const ConfigOptions& options =ConfigOptions() )
            : CacheOptions( options )
        {
            setDriver( "filesystem" );
            fromConfig( _conf ); 
        }

        /** dtor */
        virtual ~FileSystemCacheOptions() { }

    public:
        OE_OPTION(std::string, rootPath);
        OE_OPTION(unsigned, threads, 1u);
        OE_OPTION(std::string, format, "osgb");

    public:
        virtual Config getConfig() const {
            Config conf = ConfigOptions::getConfig();
            conf.set("path", rootPath() );
            conf.set("threads", threads() );
            conf.set("image_format", format());
            return conf;
        }
        virtual void mergeConfig( const Config& conf ) {
            ConfigOptions::mergeConfig( conf );
            fromConfig( conf );
        }

    private:
        void fromConfig( const Config& conf ) {
            conf.get("path", rootPath() );
            conf.get("threads", threads() );
            conf.get("image_format", format());
        }
    };

} } // namespace osgEarth::Drivers

#endif // OSGEARTH_DRIVER_CACHE_FILESYSTEM

