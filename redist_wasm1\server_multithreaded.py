#!/usr/bin/env python3
"""
支持SharedArrayBuffer的HTTP服务器
用于WebAssembly多线程应用
"""

import http.server
import socketserver
import os
import sys
from urllib.parse import urlparse

class SharedArrayBufferHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    """
    自定义HTTP请求处理器，添加支持SharedArrayBuffer的HTTP头
    """
    
    def end_headers(self):
        # 添加支持SharedArrayBuffer的HTTP头
        self.send_header('Cross-Origin-Embedder-Policy', 'require-corp')
        self.send_header('Cross-Origin-Opener-Policy', 'same-origin')
        
        # 添加其他安全头
        self.send_header('Cross-Origin-Resource-Policy', 'cross-origin')
        
        # 缓存控制
        if self.path.endswith('.wasm'):
            self.send_header('Cache-Control', 'no-cache')
        elif self.path.endswith('.js'):
            self.send_header('Cache-Control', 'no-cache')
        
        super().end_headers()
    
    def guess_type(self, path):
        """
        设置正确的MIME类型
        """
        mimetype, encoding = super().guess_type(path)
        
        # 设置WebAssembly文件的MIME类型
        if path.endswith('.wasm'):
            return 'application/wasm', encoding
        elif path.endswith('.js'):
            return 'application/javascript', encoding
        
        return mimetype, encoding
    
    def log_message(self, format, *args):
        """
        自定义日志格式
        """
        timestamp = self.log_date_time_string()
        print(f"[{timestamp}] {format % args}")

def main():
    """
    启动支持SharedArrayBuffer的HTTP服务器
    """
    port = 8080
    if len(sys.argv) > 1:
        try:
            port = int(sys.argv[1])
        except ValueError:
            print(f"错误: 无效的端口号 '{sys.argv[1]}'")
            sys.exit(1)
    
    # 检查当前目录是否包含WebAssembly文件
    wasm_files = [f for f in os.listdir('.') if f.endswith('.wasm')]
    js_files = [f for f in os.listdir('.') if f.endswith('.js') and not f.startswith('server_')]
    
    print("🧵 启动支持多线程的WebAssembly HTTP服务器")
    print("=" * 60)
    print(f"端口: {port}")
    print(f"目录: {os.getcwd()}")
    print(f"WebAssembly文件: {len(wasm_files)} 个")
    print(f"JavaScript文件: {len(js_files)} 个")
    print()
    
    if wasm_files:
        print("📁 发现的WebAssembly文件:")
        for wasm_file in sorted(wasm_files):
            size = os.path.getsize(wasm_file) / (1024 * 1024)
            print(f"  • {wasm_file} ({size:.1f} MB)")
        print()
    
    print("🔧 HTTP头配置:")
    print("  • Cross-Origin-Embedder-Policy: require-corp")
    print("  • Cross-Origin-Opener-Policy: same-origin")
    print("  • Cross-Origin-Resource-Policy: cross-origin")
    print()
    
    print("🌐 访问地址:")
    print(f"  • 多线程测试: http://localhost:{port}/test_multithreaded.html")
    print(f"  • 调试页面: http://localhost:{port}/debug_test.html")
    print(f"  • 简单测试: http://localhost:{port}/simple_test.html")
    print()
    
    print("⚠️  注意事项:")
    print("  • 确保浏览器支持SharedArrayBuffer")
    print("  • Chrome需要启用 --enable-features=SharedArrayBuffer")
    print("  • Firefox需要在about:config中启用相关设置")
    print()
    
    try:
        with socketserver.TCPServer(("", port), SharedArrayBufferHTTPRequestHandler) as httpd:
            print(f"🚀 服务器启动成功，监听端口 {port}")
            print("按 Ctrl+C 停止服务器")
            print("=" * 60)
            httpd.serve_forever()
    except KeyboardInterrupt:
        print("\n🛑 服务器已停止")
    except OSError as e:
        if e.errno == 98:  # Address already in use
            print(f"❌ 错误: 端口 {port} 已被占用")
            print("请尝试使用其他端口，例如:")
            print(f"python server_multithreaded.py {port + 1}")
        else:
            print(f"❌ 错误: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
